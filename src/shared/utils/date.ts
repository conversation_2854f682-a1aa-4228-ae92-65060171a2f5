// APISportsGame CMS - Shared Date Utilities
// Centralized date handling với dayjs

import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import customParseFormat from 'dayjs/plugin/customParseFormat';

// ============================================================================
// DAYJS CONFIGURATION
// ============================================================================

// Extend dayjs với required plugins
dayjs.extend(relativeTime);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);

// ============================================================================
// DATE UTILITIES
// ============================================================================

export const dateUtils = {
  /**
   * Format date to relative time (e.g., "2 hours ago")
   */
  fromNow: (date: string | Date) => {
    return dayjs(date).fromNow();
  },

  /**
   * Format date to standard format
   */
  format: (date: string | Date, format = 'YYYY-MM-DD HH:mm:ss') => {
    return dayjs(date).format(format);
  },

  /**
   * Format date for display
   */
  formatDisplay: (date: string | Date) => {
    return dayjs(date).format('MMM DD, YYYY');
  },

  /**
   * Format date with time for display
   */
  formatDateTime: (date: string | Date) => {
    return dayjs(date).format('MMM DD, YYYY HH:mm');
  },

  /**
   * Check if date is today
   */
  isToday: (date: string | Date) => {
    return dayjs(date).isSame(dayjs(), 'day');
  },

  /**
   * Check if date is yesterday
   */
  isYesterday: (date: string | Date) => {
    return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day');
  },

  /**
   * Get start of day
   */
  startOfDay: (date?: string | Date) => {
    return dayjs(date).startOf('day');
  },

  /**
   * Get end of day
   */
  endOfDay: (date?: string | Date) => {
    return dayjs(date).endOf('day');
  },

  /**
   * Convert to ISO string
   */
  toISOString: (date: string | Date) => {
    return dayjs(date).toISOString();
  },

  /**
   * Parse date for form inputs
   */
  parseForForm: (date: string | Date | null) => {
    if (!date) return null;
    return dayjs(date);
  },

  /**
   * Get current timestamp
   */
  now: () => {
    return dayjs();
  },

  /**
   * Add time to date
   */
  add: (date: string | Date, amount: number, unit: dayjs.ManipulateType) => {
    return dayjs(date).add(amount, unit);
  },

  /**
   * Subtract time from date
   */
  subtract: (date: string | Date, amount: number, unit: dayjs.ManipulateType) => {
    return dayjs(date).subtract(amount, unit);
  },

  /**
   * Check if date is before another date
   */
  isBefore: (date1: string | Date, date2: string | Date) => {
    return dayjs(date1).isBefore(dayjs(date2));
  },

  /**
   * Check if date is after another date
   */
  isAfter: (date1: string | Date, date2: string | Date) => {
    return dayjs(date1).isAfter(dayjs(date2));
  },

  /**
   * Get difference between dates
   */
  diff: (date1: string | Date, date2: string | Date, unit?: dayjs.QUnitType) => {
    return dayjs(date1).diff(dayjs(date2), unit);
  },
};

// ============================================================================
// COMMON DATE FORMATS
// ============================================================================

export const DATE_FORMATS = {
  DISPLAY: 'MMM DD, YYYY',
  DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',
  ISO: 'YYYY-MM-DD',
  ISO_WITH_TIME: 'YYYY-MM-DD HH:mm:ss',
  TIME_ONLY: 'HH:mm',
  MONTH_YEAR: 'MMM YYYY',
  FULL: 'dddd, MMMM DD, YYYY',
} as const;

// ============================================================================
// EXPORTS
// ============================================================================

export { dayjs };
export default dateUtils;
