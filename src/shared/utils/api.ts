// APISportsGame CMS - Shared API Utilities
// Common API utilities và error handling

import { AxiosError } from 'axios';

// ============================================================================
// API ERROR HANDLING
// ============================================================================

export interface ApiErrorResponse {
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
}

export class ApiError extends Error {
  public statusCode: number;
  public response?: ApiErrorResponse;

  constructor(error: AxiosError) {
    const response = error.response?.data as ApiErrorResponse;
    const message = Array.isArray(response?.message) 
      ? response.message.join(', ')
      : response?.message || error.message || 'An error occurred';

    super(message);
    this.name = 'ApiError';
    this.statusCode = error.response?.status || 500;
    this.response = response;
  }
}

// ============================================================================
// API RESPONSE UTILITIES
// ============================================================================

export const apiUtils = {
  /**
   * Handle API errors consistently
   */
  handleError: (error: unknown): ApiError => {
    if (error instanceof AxiosError) {
      return new ApiError(error);
    }
    
    if (error instanceof Error) {
      const apiError = new ApiError({} as AxiosError);
      apiError.message = error.message;
      return apiError;
    }

    const apiError = new ApiError({} as AxiosError);
    apiError.message = 'Unknown error occurred';
    return apiError;
  },

  /**
   * Extract error message for display
   */
  getErrorMessage: (error: unknown): string => {
    const apiError = apiUtils.handleError(error);
    return apiError.message;
  },

  /**
   * Check if error is specific status code
   */
  isErrorStatus: (error: unknown, statusCode: number): boolean => {
    const apiError = apiUtils.handleError(error);
    return apiError.statusCode === statusCode;
  },

  /**
   * Check if error is unauthorized
   */
  isUnauthorized: (error: unknown): boolean => {
    return apiUtils.isErrorStatus(error, 401);
  },

  /**
   * Check if error is forbidden
   */
  isForbidden: (error: unknown): boolean => {
    return apiUtils.isErrorStatus(error, 403);
  },

  /**
   * Check if error is not found
   */
  isNotFound: (error: unknown): boolean => {
    return apiUtils.isErrorStatus(error, 404);
  },

  /**
   * Check if error is validation error
   */
  isValidationError: (error: unknown): boolean => {
    return apiUtils.isErrorStatus(error, 400);
  },

  /**
   * Format pagination params
   */
  formatPaginationParams: (page: number, limit: number) => ({
    page,
    limit,
    offset: (page - 1) * limit,
  }),

  /**
   * Format filter params (remove undefined values)
   */
  formatFilterParams: (filters: Record<string, any>) => {
    const cleanFilters: Record<string, any> = {};
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        cleanFilters[key] = value;
      }
    });

    return cleanFilters;
  },

  /**
   * Build query string from params
   */
  buildQueryString: (params: Record<string, any>): string => {
    const cleanParams = apiUtils.formatFilterParams(params);
    const searchParams = new URLSearchParams();

    Object.entries(cleanParams).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach(v => searchParams.append(key, String(v)));
      } else {
        searchParams.append(key, String(value));
      }
    });

    return searchParams.toString();
  },
};

// ============================================================================
// PAGINATION UTILITIES
// ============================================================================

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: PaginationMeta;
}

export const paginationUtils = {
  /**
   * Calculate total pages
   */
  calculateTotalPages: (total: number, limit: number): number => {
    return Math.ceil(total / limit);
  },

  /**
   * Calculate offset from page and limit
   */
  calculateOffset: (page: number, limit: number): number => {
    return (page - 1) * limit;
  },

  /**
   * Get pagination info for display
   */
  getPaginationInfo: (meta: PaginationMeta) => {
    const start = paginationUtils.calculateOffset(meta.page, meta.limit) + 1;
    const end = Math.min(start + meta.limit - 1, meta.total);
    
    return {
      start,
      end,
      total: meta.total,
      hasNext: meta.page < meta.totalPages,
      hasPrev: meta.page > 1,
    };
  },
};

// ============================================================================
// EXPORTS
// ============================================================================

export default apiUtils;
