// APISportsGame CMS - Shared Common Types
// Common types used across all modules

// ============================================================================
// COMMON ENTITY TYPES
// ============================================================================

export interface BaseEntity {
  id: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface TimestampedEntity {
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiError {
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: PaginationMeta;
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

// ============================================================================
// FORM TYPES
// ============================================================================

export interface FormFieldOption {
  label: string;
  value: any;
  disabled?: boolean;
}

export interface FormFieldProps {
  name: string;
  label: string;
  required?: boolean;
  placeholder?: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'date' | 'switch';
  options?: FormFieldOption[];
  disabled?: boolean;
  tooltip?: string;
}

// ============================================================================
// TABLE TYPES
// ============================================================================

export interface TableColumn<T> {
  key: string;
  title: string;
  dataIndex: keyof T;
  render?: (value: any, record: T) => React.ReactNode;
  sorter?: boolean;
  width?: number;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
}

export interface TableAction<T> {
  key: string;
  label: string;
  icon?: React.ReactNode;
  onClick: (record: T) => void;
  disabled?: (record: T) => boolean;
  danger?: boolean;
}

export interface TablePagination {
  current: number;
  pageSize: number;
  total: number;
  onChange: (page: number, pageSize: number) => void;
}

// ============================================================================
// FILTER TYPES
// ============================================================================

export interface BaseFilters {
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface DateRangeFilter {
  startDate?: string;
  endDate?: string;
}

export interface StatusFilter {
  isActive?: boolean;
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface ComponentWithChildren {
  children: React.ReactNode;
}

export interface ComponentWithClassName {
  className?: string;
}

export interface LoadingProps {
  loading?: boolean;
}

export interface ErrorProps {
  error?: string | null;
}

// ============================================================================
// MODAL TYPES
// ============================================================================

export interface ModalProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  width?: number;
  destroyOnHidden?: boolean;
}

export interface ConfirmModalProps extends ModalProps {
  onConfirm: () => void;
  confirmText?: string;
  cancelText?: string;
  danger?: boolean;
}

// ============================================================================
// NOTIFICATION TYPES
// ============================================================================

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface NotificationConfig {
  type: NotificationType;
  message: string;
  description?: string;
  duration?: number;
}

// ============================================================================
// PERMISSION TYPES
// ============================================================================

export interface Permission {
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

export interface PermissionCheck {
  hasPermission: boolean;
  reason?: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;

// ============================================================================
// ASYNC OPERATION TYPES
// ============================================================================

export interface AsyncOperation<T = any> {
  loading: boolean;
  data: T | null;
  error: string | null;
}

export interface MutationState {
  loading: boolean;
  error: string | null;
  success: boolean;
}

// ============================================================================
// EXPORT ALL
// ============================================================================

// Remove circular export
