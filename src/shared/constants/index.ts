// APISportsGame CMS - Shared Constants
// Application-wide constants

// ============================================================================
// API CONFIGURATION
// ============================================================================

export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
} as const;

// ============================================================================
// PAGINATION DEFAULTS
// ============================================================================

export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100,
} as const;

// ============================================================================
// VALIDATION RULES
// ============================================================================

export const VALIDATION = {
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 50,
    PATTERN: /^[a-zA-Z0-9_]+$/,
  },
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 100,
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  SEARCH: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 100,
  },
} as const;

// ============================================================================
// UI CONSTANTS
// ============================================================================

export const UI = {
  DEBOUNCE_DELAY: 300,
  ANIMATION_DURATION: 200,
  NOTIFICATION_DURATION: 4000,
  MODAL_WIDTH: {
    SMALL: 400,
    MEDIUM: 600,
    LARGE: 800,
    EXTRA_LARGE: 1000,
  },
} as const;

// ============================================================================
// STATUS COLORS
// ============================================================================

export const STATUS_COLORS = {
  SUCCESS: '#52c41a',
  ERROR: '#ff4d4f',
  WARNING: '#faad14',
  INFO: '#1890ff',
  PROCESSING: '#722ed1',
  DEFAULT: '#d9d9d9',
} as const;

// ============================================================================
// USER ROLES & TIERS
// ============================================================================

export const USER_ROLES = {
  ADMIN: 'admin',
  EDITOR: 'editor',
  MODERATOR: 'moderator',
} as const;

export const USER_TIERS = {
  FREE: 'free',
  PREMIUM: 'premium',
  ENTERPRISE: 'enterprise',
} as const;

export const ROLE_COLORS = {
  [USER_ROLES.ADMIN]: 'red',
  [USER_ROLES.EDITOR]: 'blue',
  [USER_ROLES.MODERATOR]: 'green',
} as const;

export const TIER_COLORS = {
  [USER_TIERS.FREE]: 'default',
  [USER_TIERS.PREMIUM]: 'gold',
  [USER_TIERS.ENTERPRISE]: 'purple',
} as const;

// ============================================================================
// API LIMITS
// ============================================================================

export const API_LIMITS = {
  FREE_TIER: 1000,
  PREMIUM_TIER: 50000,
  ENTERPRISE_TIER: 200000,
  UNLIMITED: null,
} as const;

// ============================================================================
// DATE FORMATS
// ============================================================================

export const DATE_FORMATS = {
  DISPLAY: 'MMM DD, YYYY',
  DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',
  ISO: 'YYYY-MM-DD',
  ISO_WITH_TIME: 'YYYY-MM-DD HH:mm:ss',
  TIME_ONLY: 'HH:mm',
  MONTH_YEAR: 'MMM YYYY',
  FULL: 'dddd, MMMM DD, YYYY',
} as const;

// ============================================================================
// ROUTES
// ============================================================================

export const ROUTES = {
  HOME: '/',
  LOGIN: '/auth/login',
  DASHBOARD: '/dashboard',
  USERS: '/dashboard/users',
  LEAGUES: '/dashboard/leagues',
  TEAMS: '/dashboard/teams',
  FIXTURES: '/dashboard/fixtures',
  ANALYTICS: '/dashboard/analytics',
  SYNC: '/dashboard/sync',
  PROFILE: '/dashboard/profile',
  SETTINGS: '/dashboard/settings',
} as const;

// ============================================================================
// LOCAL STORAGE KEYS
// ============================================================================

export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_PREFERENCES: 'user_preferences',
  THEME: 'theme',
  LANGUAGE: 'language',
} as const;

// ============================================================================
// ERROR MESSAGES
// ============================================================================

export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied. Insufficient permissions.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SERVER_ERROR: 'Internal server error. Please try again later.',
  UNKNOWN_ERROR: 'An unknown error occurred.',
} as const;

// ============================================================================
// SUCCESS MESSAGES
// ============================================================================

export const SUCCESS_MESSAGES = {
  CREATED: 'Created successfully',
  UPDATED: 'Updated successfully',
  DELETED: 'Deleted successfully',
  SAVED: 'Saved successfully',
  SYNCED: 'Synced successfully',
  EXPORTED: 'Exported successfully',
} as const;

// ============================================================================
// QUERY KEYS
// ============================================================================

export const QUERY_KEYS = {
  AUTH: ['auth'],
  USERS: ['users'],
  LEAGUES: ['leagues'],
  TEAMS: ['teams'],
  FIXTURES: ['fixtures'],
  DASHBOARD: ['dashboard'],
  SYNC: ['sync'],
} as const;

// ============================================================================
// PERMISSIONS
// ============================================================================

export const PERMISSIONS = {
  USERS: {
    VIEW: 'users:view',
    CREATE: 'users:create',
    UPDATE: 'users:update',
    DELETE: 'users:delete',
  },
  LEAGUES: {
    VIEW: 'leagues:view',
    CREATE: 'leagues:create',
    UPDATE: 'leagues:update',
    DELETE: 'leagues:delete',
  },
  TEAMS: {
    VIEW: 'teams:view',
    CREATE: 'teams:create',
    UPDATE: 'teams:update',
    DELETE: 'teams:delete',
  },
  FIXTURES: {
    VIEW: 'fixtures:view',
    CREATE: 'fixtures:create',
    UPDATE: 'fixtures:update',
    DELETE: 'fixtures:delete',
  },
  SYNC: {
    VIEW: 'sync:view',
    EXECUTE: 'sync:execute',
  },
  ANALYTICS: {
    VIEW: 'analytics:view',
  },
} as const;

// ============================================================================
// EXPORT TYPES
// ============================================================================

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];
export type UserTier = typeof USER_TIERS[keyof typeof USER_TIERS];
export type Route = typeof ROUTES[keyof typeof ROUTES];
export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS][keyof typeof PERMISSIONS[keyof typeof PERMISSIONS]];

// ============================================================================
// DEFAULT EXPORT
// ============================================================================

export default {
  API_CONFIG,
  PAGINATION,
  VALIDATION,
  UI,
  STATUS_COLORS,
  USER_ROLES,
  USER_TIERS,
  ROLE_COLORS,
  TIER_COLORS,
  API_LIMITS,
  DATE_FORMATS,
  ROUTES,
  STORAGE_KEYS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  QUERY_KEYS,
  PERMISSIONS,
};
