// APISportsGame CMS - League Management Page
// Dashboard page for managing leagues

'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Space,
  Button,
  Modal,
  message,
  Tabs,
} from 'antd';
import {
  TrophyOutlined,
  GlobalOutlined,
  CalendarOutlined,
  SyncOutlined,
  PlusOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import { DashboardLayout } from '@/components/layouts/dashboard-layout';
import { LeagueTable } from '@/modules/leagues/components/league-table';
import { LeagueForm } from '@/modules/leagues/components/league-form';
import { usePermissions } from '@/stores/auth-store';
import { League, LeagueFilters } from '@/modules/leagues/types';

const { Title, Text } = Typography;

// ============================================================================
// MOCK DATA (Replace với real API calls)
// ============================================================================

const mockLeagues: League[] = [
  {
    id: 1,
    name: 'Premier League',
    country: 'England',
    logo: 'https://media.api-sports.io/football/leagues/39.png',
    flag: 'https://media.api-sports.io/flags/gb.svg',
    season: 2024,
    start: '2024-08-17',
    end: '2025-05-25',
    current: true,
    coverage: {
      fixtures: {
        events: true,
        lineups: true,
        statistics_fixtures: true,
        statistics_players: true,
      },
      standings: true,
      players: true,
      top_scorers: true,
      top_assists: true,
      top_cards: true,
      injuries: true,
      predictions: true,
      odds: true,
    },
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-15'),
  },
  {
    id: 2,
    name: 'La Liga',
    country: 'Spain',
    logo: 'https://media.api-sports.io/football/leagues/140.png',
    flag: 'https://media.api-sports.io/flags/es.svg',
    season: 2024,
    start: '2024-08-18',
    end: '2025-05-25',
    current: true,
    coverage: {
      fixtures: {
        events: true,
        lineups: true,
        statistics_fixtures: true,
        statistics_players: false,
      },
      standings: true,
      players: true,
      top_scorers: true,
      top_assists: false,
      top_cards: false,
      injuries: false,
      predictions: true,
      odds: true,
    },
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-15'),
  },
];

const mockStats = {
  total: 25,
  active: 18,
  current: 12,
  byCountry: {
    England: 3,
    Spain: 2,
    Germany: 3,
    Italy: 2,
    France: 2,
  },
  bySeason: {
    '2024': 15,
    '2023': 8,
    '2022': 2,
  },
};

// ============================================================================
// LEAGUE MANAGEMENT PAGE
// ============================================================================

export default function LeagueManagementPage() {
  // ========================================================================
  // STATE
  // ========================================================================

  const [loading, setLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [selectedLeague, setSelectedLeague] = useState<League | null>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [activeTab, setActiveTab] = useState('all');

  // Filters và pagination
  const [filters, setFilters] = useState<LeagueFilters>({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: mockLeagues.length,
  });

  // ========================================================================
  // PERMISSIONS
  // ========================================================================

  const permissions = usePermissions();

  // ========================================================================
  // EFFECTS
  // ========================================================================

  useEffect(() => {
    // Load initial data
    handleRefresh();
  }, []);

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleRefresh = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('Leagues refreshed successfully');
    } catch (error) {
      message.error('Failed to refresh leagues');
    } finally {
      setLoading(false);
    }
  };

  const handleAddLeague = () => {
    setSelectedLeague(null);
    setFormMode('create');
    setShowForm(true);
  };

  const handleEditLeague = (league: League) => {
    setSelectedLeague(league);
    setFormMode('edit');
    setShowForm(true);
  };

  const handleDeleteLeague = async (league: League) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success(`League "${league.name}" deleted successfully`);
      handleRefresh();
    } catch (error) {
      message.error('Failed to delete league');
    } finally {
      setLoading(false);
    }
  };

  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (formMode === 'create') {
        message.success('League created successfully');
      } else {
        message.success('League updated successfully');
      }
      
      setShowForm(false);
      handleRefresh();
    } catch (error) {
      message.error(`Failed to ${formMode} league`);
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async () => {
    setLoading(true);
    try {
      // Simulate sync operation
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success('Leagues synchronized successfully');
      handleRefresh();
    } catch (error) {
      message.error('Failed to sync leagues');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = () => {
    message.info('Export functionality will be implemented');
  };

  // ========================================================================
  // RENDER
  // ========================================================================

  if (!permissions.canManageLeagues) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <Text type="secondary">You don't have permission to manage leagues.</Text>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Page Header */}
        <div className="mb-6">
          <Title level={2} className="mb-2">
            <TrophyOutlined className="mr-3" />
            League Management
          </Title>
          <Text type="secondary">
            Manage football leagues, seasons, and coverage settings
          </Text>
        </div>

        {/* Statistics Cards */}
        <Row gutter={16} className="mb-6">
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Leagues"
                value={mockStats.total}
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Active Leagues"
                value={mockStats.active}
                prefix={<CalendarOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Current Season"
                value={mockStats.current}
                prefix={<GlobalOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Countries"
                value={Object.keys(mockStats.byCountry).length}
                prefix={<BarChartOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>

        {/* League Management Tabs */}
        <Card>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            tabBarExtraContent={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddLeague}
              >
                Add League
              </Button>
            }
            items={[
              {
                key: 'all',
                label: (
                  <Space>
                    <TrophyOutlined />
                    All Leagues ({mockStats.total})
                  </Space>
                ),
                children: (
                  <LeagueTable
                    leagues={mockLeagues}
                    loading={loading}
                    onEdit={handleEditLeague}
                    onDelete={handleDeleteLeague}
                    onAdd={handleAddLeague}
                    onRefresh={handleRefresh}
                    onExport={handleExport}
                    onSync={handleSync}
                    pagination={{
                      ...pagination,
                      onChange: (page, pageSize) => {
                        setPagination({ ...pagination, current: page, pageSize });
                      },
                    }}
                    filters={filters}
                    onFiltersChange={setFilters}
                  />
                ),
              },
              {
                key: 'active',
                label: (
                  <Space>
                    <CalendarOutlined />
                    Active Leagues ({mockStats.active})
                  </Space>
                ),
                children: (
                  <LeagueTable
                    leagues={mockLeagues.filter(league => league.current)}
                    loading={loading}
                    onEdit={handleEditLeague}
                    onDelete={handleDeleteLeague}
                    onAdd={handleAddLeague}
                    onRefresh={handleRefresh}
                    onExport={handleExport}
                    onSync={handleSync}
                    pagination={{
                      ...pagination,
                      onChange: (page, pageSize) => {
                        setPagination({ ...pagination, current: page, pageSize });
                      },
                    }}
                    filters={filters}
                    onFiltersChange={setFilters}
                  />
                ),
              },
            ]}
          />
        </Card>

        {/* League Form Modal */}
        <Modal
          title={null}
          open={showForm}
          onCancel={() => setShowForm(false)}
          footer={null}
          width={900}
          destroyOnHidden
        >
          <LeagueForm
            mode={formMode}
            initialValues={selectedLeague}
            loading={loading}
            onSubmit={handleFormSubmit}
            onCancel={() => setShowForm(false)}
          />
        </Modal>
      </div>
    </DashboardLayout>
  );
}
