'use client';

// APISportsGame CMS - User Management Page
// Main page cho quản lý SystemUser và RegisteredUser

import React, { useState } from 'react';
import {
  Tabs,
  Typography,
  Space,
  Card,
  Statistic,
  Row,
  Col,
  Modal,
  message,
  Button
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  CrownOutlined,
  ApiOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { DashboardLayout } from '@/components/layouts/dashboard-layout';
import { UserTable, UserForm } from '@/modules/users';
import { usePermissions } from '@/stores/auth-store';
import { User, UserFilters } from '@/modules/users/types';
import { SystemUser, RegisteredUser } from '@/modules/auth/types';

const { Title, Text } = Typography;

// ============================================================================
// MOCK DATA - sẽ được thay thế bằng real API calls
// ============================================================================

const mockSystemUsers: SystemUser[] = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    isActive: true,
    lastLoginAt: new Date('2024-01-15T10:30:00Z'),
    createdAt: new Date('2024-01-01T00:00:00Z'),
  },
  {
    id: 2,
    username: 'editor1',
    email: '<EMAIL>',
    role: 'editor',
    isActive: true,
    lastLoginAt: new Date('2024-01-15T09:15:00Z'),
    createdAt: new Date('2024-01-05T00:00:00Z'),
  },
  {
    id: 3,
    username: 'moderator1',
    email: '<EMAIL>',
    role: 'moderator',
    isActive: false,
    lastLoginAt: new Date('2024-01-10T14:20:00Z'),
    createdAt: new Date('2024-01-10T00:00:00Z'),
  },
];

const mockRegisteredUsers: RegisteredUser[] = [
  {
    id: 1,
    username: 'developer1',
    email: '<EMAIL>',
    tier: 'premium',
    isActive: true,
    isEmailVerified: true,
    apiCallsUsed: 15000,
    apiCallsLimit: 50000,
    subscriptionEndDate: new Date('2024-12-31T23:59:59Z'),
    lastLoginAt: new Date('2024-01-15T11:00:00Z'),
    createdAt: new Date('2024-01-01T00:00:00Z'),
  },
  {
    id: 2,
    username: 'startup_user',
    email: '<EMAIL>',
    tier: 'enterprise',
    isActive: true,
    isEmailVerified: true,
    apiCallsUsed: 75000,
    apiCallsLimit: 200000,
    subscriptionEndDate: new Date('2024-06-30T23:59:59Z'),
    lastLoginAt: new Date('2024-01-15T08:30:00Z'),
    createdAt: new Date('2023-12-15T00:00:00Z'),
  },
  {
    id: 3,
    username: 'free_user',
    email: '<EMAIL>',
    tier: 'free',
    isActive: true,
    isEmailVerified: false,
    apiCallsUsed: 950,
    apiCallsLimit: 1000,
    subscriptionEndDate: null,
    lastLoginAt: new Date('2024-01-14T16:45:00Z'),
    createdAt: new Date('2024-01-14T00:00:00Z'),
  },
];

// ============================================================================
// USER MANAGEMENT PAGE COMPONENT
// ============================================================================

export default function UserManagementPage() {
  const permissions = usePermissions();

  // State management
  const [activeTab, setActiveTab] = useState('system');
  const [loading, setLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // Filters
  const [systemUserFilters, setSystemUserFilters] = useState<UserFilters>({});
  const [registeredUserFilters, setRegisteredUserFilters] = useState<UserFilters>({});

  // Pagination
  const [systemUserPagination, setSystemUserPagination] = useState({
    current: 1,
    pageSize: 10,
    total: mockSystemUsers.length,
  });

  const [registeredUserPagination, setRegisteredUserPagination] = useState({
    current: 1,
    pageSize: 10,
    total: mockRegisteredUsers.length,
  });

  // ========================================================================
  // PERMISSION CHECK
  // ========================================================================

  if (!permissions.canManageUsers) {
    return (
      <DashboardLayout>
        <Card>
          <div className="text-center py-8">
            <UserOutlined className="text-4xl text-gray-400 mb-4" />
            <Title level={3}>Access Denied</Title>
            <Text type="secondary">
              You don't have permission to manage users.
            </Text>
          </div>
        </Card>
      </DashboardLayout>
    );
  }

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleAddUser = (userType: 'system' | 'registered') => {
    setFormMode('create');
    setSelectedUser(null);
    setActiveTab(userType);
    setShowForm(true);
  };

  const handleEditUser = (user: User) => {
    setFormMode('edit');
    setSelectedUser(user);
    setShowForm(true);
  };

  const handleDeleteUser = (user: User) => {
    // Mock delete - sẽ được thay thế bằng API call
    message.success(`User "${user.username}" deleted successfully`);
  };

  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      message.success(
        `User ${formMode === 'create' ? 'created' : 'updated'} successfully`
      );
      setShowForm(false);
      setSelectedUser(null);
    } catch (error) {
      message.error('Operation failed');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setLoading(true);
    // Mock refresh
    setTimeout(() => {
      setLoading(false);
      message.success('Data refreshed');
    }, 1000);
  };

  const handleExport = () => {
    message.info('Export functionality will be implemented');
  };

  // ========================================================================
  // STATISTICS
  // ========================================================================

  const systemUserStats = {
    total: mockSystemUsers.length,
    active: mockSystemUsers.filter(u => u.isActive).length,
    admins: mockSystemUsers.filter(u => u.role === 'admin').length,
    editors: mockSystemUsers.filter(u => u.role === 'editor').length,
  };

  const registeredUserStats = {
    total: mockRegisteredUsers.length,
    active: mockRegisteredUsers.filter(u => u.isActive).length,
    verified: mockRegisteredUsers.filter(u => u.isEmailVerified).length,
    premium: mockRegisteredUsers.filter(u => u.tier === 'premium' || u.tier === 'enterprise').length,
  };

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <Title level={2} className="mb-2">
            User Management
          </Title>
          <Text type="secondary" className="text-base">
            Manage system users and registered API users
          </Text>
        </div>

        {/* Statistics Cards */}
        <Row gutter={16}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total System Users"
                value={systemUserStats.total}
                prefix={<CrownOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Active System Users"
                value={systemUserStats.active}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Registered Users"
                value={registeredUserStats.total}
                prefix={<TeamOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Verified Users"
                value={registeredUserStats.verified}
                prefix={<ApiOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
        </Row>

        {/* User Management Tabs */}
        <Card>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            tabBarExtraContent={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => handleAddUser(activeTab as 'system' | 'registered')}
              >
                Add {activeTab === 'system' ? 'System User' : 'User'}
              </Button>
            }
            items={[
              {
                key: 'system',
                label: (
                  <Space>
                    <CrownOutlined />
                    System Users ({systemUserStats.total})
                  </Space>
                ),
                children: (
                  <UserTable
                    users={mockSystemUsers}
                    loading={loading}
                    userType="system"
                    onEdit={handleEditUser}
                    onDelete={handleDeleteUser}
                    onAdd={() => handleAddUser('system')}
                    onRefresh={handleRefresh}
                    onExport={handleExport}
                    pagination={{
                      ...systemUserPagination,
                      onChange: (page, pageSize) => {
                        setSystemUserPagination({ ...systemUserPagination, current: page, pageSize });
                      },
                    }}
                    filters={systemUserFilters}
                    onFiltersChange={setSystemUserFilters}
                  />
                ),
              },
              {
                key: 'registered',
                label: (
                  <Space>
                    <TeamOutlined />
                    Registered Users ({registeredUserStats.total})
                  </Space>
                ),
                children: (
                  <UserTable
                    users={mockRegisteredUsers}
                    loading={loading}
                    userType="registered"
                    onEdit={handleEditUser}
                    onDelete={handleDeleteUser}
                    onAdd={() => handleAddUser('registered')}
                    onRefresh={handleRefresh}
                    onExport={handleExport}
                    pagination={{
                      ...registeredUserPagination,
                      onChange: (page, pageSize) => {
                        setRegisteredUserPagination({ ...registeredUserPagination, current: page, pageSize });
                      },
                    }}
                    filters={registeredUserFilters}
                    onFiltersChange={setRegisteredUserFilters}
                  />
                ),
              },
            ]}
          />
        </Card>

        {/* User Form Modal */}
        <Modal
          title={null}
          open={showForm}
          onCancel={() => setShowForm(false)}
          footer={null}
          width={800}
          destroyOnHidden
        >
          <UserForm
            userType={activeTab as 'system' | 'registered'}
            mode={formMode}
            initialValues={selectedUser || undefined}
            loading={loading}
            onSubmit={handleFormSubmit}
            onCancel={() => setShowForm(false)}
          />
        </Modal>
      </div>
    </DashboardLayout>
  );
}
