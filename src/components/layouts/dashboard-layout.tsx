'use client';

// APISportsGame CMS - Dashboard Layout
// Main layout cho dashboard với navigation và authentication

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Typography,
  Space,
  Button,
  Badge,
  Spin,
  Alert
} from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  TrophyOutlined,
  CalendarOutlined,
  Bar<PERSON><PERSON>Outlined,
  SyncOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ApiOutlined
} from '@ant-design/icons';
import { useAuth, usePermissions } from '@/stores/auth-store';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

// ============================================================================
// DASHBOARD LAYOUT COMPONENT
// ============================================================================

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, isAuthenticated, isLoading, logout, getProfile } = useAuth();
  const permissions = usePermissions();

  const [collapsed, setCollapsed] = useState(false);
  const [mounted, setMounted] = useState(false);

  // ========================================================================
  // EFFECTS
  // ========================================================================

  useEffect(() => {
    setMounted(true);
  }, []);

  // Check authentication và load profile
  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      router.push('/auth/login');
      return;
    }

    if (isAuthenticated && !user) {
      getProfile().catch(() => {
        router.push('/auth/login');
      });
    }
  }, [isAuthenticated, isLoading, user, router, getProfile]);

  // ========================================================================
  // MENU CONFIGURATION
  // ========================================================================

  const getMenuItems = () => {
    const items = [
      {
        key: '/dashboard',
        icon: <DashboardOutlined />,
        label: 'Dashboard',
      }
    ];

    // User Management - chỉ admin
    if (permissions.canManageUsers) {
      items.push({
        key: '/dashboard/users',
        icon: <UserOutlined />,
        label: 'User Management',
      });
    }

    // Sports Data Management
    if (permissions.canManageLeagues) {
      items.push({
        key: '/dashboard/leagues',
        icon: <TrophyOutlined />,
        label: 'Quản lý Leagues',
      });
    }

    if (permissions.canManageTeams) {
      items.push({
        key: '/dashboard/teams',
        icon: <TeamOutlined />,
        label: 'Quản lý Teams',
      });
    }

    if (permissions.canManageFixtures) {
      items.push({
        key: '/dashboard/fixtures',
        icon: <CalendarOutlined />,
        label: 'Quản lý Fixtures',
      });
    }

    // Analytics
    if (permissions.canViewAnalytics) {
      items.push({
        key: '/dashboard/analytics',
        icon: <BarChartOutlined />,
        label: 'Analytics',
      });
    }

    // Sync Operations
    if (permissions.canSync) {
      items.push({
        key: '/dashboard/sync',
        icon: <SyncOutlined />,
        label: 'Sync Operations',
      });
    }

    return items;
  };

  // ========================================================================
  // USER DROPDOWN MENU
  // ========================================================================

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Thông tin cá nhân',
      onClick: () => router.push('/dashboard/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Cài đặt',
      onClick: () => router.push('/dashboard/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Đăng xuất',
      onClick: () => {
        logout();
        router.push('/auth/login');
      },
    },
  ];

  // ========================================================================
  // HANDLERS
  // ========================================================================

  const handleMenuClick = ({ key }: { key: string }) => {
    router.push(key);
  };

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  // ========================================================================
  // LOADING STATE
  // ========================================================================

  if (!mounted || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spin size="large" tip="Đang tải..." />
      </div>
    );
  }

  // ========================================================================
  // UNAUTHENTICATED STATE
  // ========================================================================

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert
          message="Chưa đăng nhập"
          description="Vui lòng đăng nhập để tiếp tục"
          type="warning"
          showIcon
        />
      </div>
    );
  }

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <Layout className="min-h-screen">
      {/* Sidebar */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        theme="dark"
        width={256}
        className="shadow-lg"
      >
        {/* Logo */}
        <div className="h-16 flex items-center justify-center border-b border-gray-700">
          <Space>
            <ApiOutlined className="text-white text-xl" />
            {!collapsed && (
              <Title level={4} className="text-white m-0">
                CMS
              </Title>
            )}
          </Space>
        </div>

        {/* Navigation Menu */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[pathname]}
          items={getMenuItems()}
          onClick={handleMenuClick}
          className="border-r-0"
        />
      </Sider>

      {/* Main Layout */}
      <Layout>
        {/* Header */}
        <Header className="bg-white shadow-sm px-4 flex items-center justify-between">
          {/* Left side */}
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={toggleCollapsed}
              className="text-lg"
            />
            <Title level={4} className="m-0">
              APISportsGame CMS
            </Title>
          </Space>

          {/* Right side */}
          <Space size="middle">
            {/* Notifications */}
            <Badge count={0} showZero={false}>
              <Button
                type="text"
                icon={<BellOutlined />}
                className="text-lg"
              />
            </Badge>

            {/* User Info */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Space className="cursor-pointer hover:bg-gray-50 px-2 py-1 rounded">
                <Avatar
                  size="small"
                  icon={<UserOutlined />}
                  className="bg-blue-500"
                />
                <div className="text-left">
                  <div className="text-sm font-medium">
                    {user?.username}
                  </div>
                  <div className="text-xs text-gray-500">
                    {permissions.isSystemUser && `${(user as any)?.role}`}
                    {permissions.isRegisteredUser && `${(user as any)?.tier}`}
                  </div>
                </div>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        {/* Content */}
        <Content className="p-6 bg-gray-50 overflow-auto">
          {children}
        </Content>
      </Layout>
    </Layout>
  );
}

export default DashboardLayout;
