'use client';

// APISportsGame CMS - League Table Component
// Reusable table cho League management

import React, { useState } from 'react';
import {
  Table,
  Tag,
  Space,
  Button,
  Dropdown,
  Avatar,
  Typography,
  Tooltip,
  Modal,
  message,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Card,
  Image
} from 'antd';
import {
  TrophyOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  SearchOutlined,
  FilterOutlined,
  PlusOutlined,
  ExportOutlined,
  ReloadOutlined,
  EyeOutlined,
  FlagOutlined
} from '@ant-design/icons';
import { League, LeagueFilters } from '@/types';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

// Extend dayjs với relativeTime plugin
dayjs.extend(relativeTime);

const { Text } = Typography;
const { Search } = Input;
const { Option } = Select;

// ============================================================================
// TYPES
// ============================================================================

interface LeagueTableProps {
  leagues: League[];
  loading?: boolean;
  onEdit?: (league: League) => void;
  onDelete?: (league: League) => void;
  onView?: (league: League) => void;
  onAdd?: () => void;
  onRefresh?: () => void;
  onExport?: () => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  filters?: LeagueFilters;
  onFiltersChange?: (filters: LeagueFilters) => void;
}

// ============================================================================
// LEAGUE TABLE COMPONENT
// ============================================================================

export function LeagueTable({
  leagues,
  loading = false,
  onEdit,
  onDelete,
  onView,
  onAdd,
  onRefresh,
  onExport,
  pagination,
  filters = {},
  onFiltersChange
}: LeagueTableProps) {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // ========================================================================
  // COLUMN DEFINITIONS
  // ========================================================================

  const columns = [
    {
      title: 'League',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: League) => (
        <Space>
          <Avatar
            size="small"
            src={record.logo}
            icon={<TrophyOutlined />}
            className="bg-blue-500"
          />
          <div>
            <div className="font-medium">{name}</div>
            <Text type="secondary" className="text-xs">
              API ID: {record.apiFootballId}
            </Text>
          </div>
        </Space>
      ),
      sorter: true,
      width: 250,
    },
    {
      title: 'Country',
      dataIndex: 'country',
      key: 'country',
      render: (country: string, record: League) => (
        <Space>
          {record.flag && (
            <Image
              src={record.flag}
              alt={country}
              width={20}
              height={15}
              preview={false}
              fallback="/placeholder-flag.png"
            />
          )}
          <span>{country}</span>
        </Space>
      ),
      filters: [
        { text: 'England', value: 'England' },
        { text: 'Spain', value: 'Spain' },
        { text: 'Germany', value: 'Germany' },
        { text: 'Italy', value: 'Italy' },
        { text: 'France', value: 'France' },
      ],
      width: 150,
    },
    {
      title: 'Season',
      dataIndex: 'season',
      key: 'season',
      render: (season: number) => (
        <Tag color="blue">{season}</Tag>
      ),
      sorter: true,
      width: 100,
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'error'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
      filters: [
        { text: 'Active', value: true },
        { text: 'Inactive', value: false },
      ],
      width: 100,
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => (
        <Tooltip title={dayjs(date).format('YYYY-MM-DD HH:mm:ss')}>
          <Text type="secondary" className="text-sm">
            {dayjs(date).format('MMM DD, YYYY')}
          </Text>
        </Tooltip>
      ),
      sorter: true,
      width: 120,
    },
    {
      title: 'Updated',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date: string) => (
        <Tooltip title={dayjs(date).format('YYYY-MM-DD HH:mm:ss')}>
          <Text type="secondary" className="text-sm">
            {dayjs(date).fromNow()}
          </Text>
        </Tooltip>
      ),
      sorter: true,
      width: 120,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record: League) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => onView?.(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(record)}
            />
          </Tooltip>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'view',
                  label: 'View Details',
                  icon: <EyeOutlined />,
                  onClick: () => onView?.(record),
                },
                {
                  key: 'edit',
                  label: 'Edit League',
                  icon: <EditOutlined />,
                  onClick: () => onEdit?.(record),
                },
                {
                  type: 'divider',
                },
                {
                  key: 'delete',
                  label: 'Delete League',
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: () => handleDelete(record),
                },
              ],
            }}
            trigger={['click']}
          >
            <Button type="text" size="small" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleDelete = (league: League) => {
    Modal.confirm({
      title: 'Delete League',
      content: `Are you sure you want to delete league "${league.name}"?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        onDelete?.(league);
        message.success('League deleted successfully');
      },
    });
  };

  const handleBulkDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('Please select leagues to delete');
      return;
    }

    Modal.confirm({
      title: 'Delete Selected Leagues',
      content: `Are you sure you want to delete ${selectedRowKeys.length} selected leagues?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        setSelectedRowKeys([]);
        message.success(`${selectedRowKeys.length} leagues deleted successfully`);
      },
    });
  };

  const handleFilterChange = (key: keyof LeagueFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    onFiltersChange?.(newFilters);
  };

  // ========================================================================
  // ROW SELECTION
  // ========================================================================

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record: League) => ({
      disabled: false,
      name: record.name,
    }),
  };

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <div className="space-y-4">
      {/* Header Actions */}
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Search
                placeholder="Search leagues..."
                allowClear
                style={{ width: 300 }}
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                onSearch={(value) => handleFilterChange('search', value)}
              />
              <Button
                icon={<FilterOutlined />}
                onClick={() => setShowFilters(!showFilters)}
              >
                Filters
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              {selectedRowKeys.length > 0 && (
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleBulkDelete}
                >
                  Delete ({selectedRowKeys.length})
                </Button>
              )}
              <Button
                icon={<ReloadOutlined />}
                onClick={onRefresh}
                loading={loading}
              >
                Refresh
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={onExport}
              >
                Export
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={onAdd}
              >
                Add League
              </Button>
            </Space>
          </Col>
        </Row>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded">
            <Row gutter={16}>
              <Col span={6}>
                <Select
                  placeholder="Select Country"
                  allowClear
                  style={{ width: '100%' }}
                  value={filters.country}
                  onChange={(value) => handleFilterChange('country', value)}
                >
                  <Option value="England">England</Option>
                  <Option value="Spain">Spain</Option>
                  <Option value="Germany">Germany</Option>
                  <Option value="Italy">Italy</Option>
                  <Option value="France">France</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select
                  placeholder="Select Season"
                  allowClear
                  style={{ width: '100%' }}
                  value={filters.season}
                  onChange={(value) => handleFilterChange('season', value)}
                >
                  <Option value={2024}>2024</Option>
                  <Option value={2023}>2023</Option>
                  <Option value={2022}>2022</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Select
                  placeholder="Select Status"
                  allowClear
                  style={{ width: '100%' }}
                  value={filters.isActive}
                  onChange={(value) => handleFilterChange('isActive', value)}
                >
                  <Option value={true}>Active</Option>
                  <Option value={false}>Inactive</Option>
                </Select>
              </Col>
              <Col span={6}>
                <DatePicker
                  placeholder="Created From"
                  style={{ width: '100%' }}
                  onChange={(date) => handleFilterChange('createdFrom', date?.toISOString())}
                />
              </Col>
            </Row>
          </div>
        )}
      </Card>

      {/* Data Table */}
      <Card>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={leagues}
          loading={loading}
          pagination={pagination ? {
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} leagues`,
            onChange: pagination.onChange,
          } : false}
          rowKey="id"
          scroll={{ x: 1200 }}
          size="middle"
        />
      </Card>
    </div>
  );
}

export default LeagueTable;
