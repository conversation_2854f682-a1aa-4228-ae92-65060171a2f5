'use client';

// APISportsGame CMS - User Table Component
// Reusable table cho SystemUser và RegisteredUser management

import React, { useState } from 'react';
import {
  Table,
  Tag,
  Space,
  Button,
  Dropdown,
  Avatar,
  Typography,
  Tooltip,
  Modal,
  message,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Card
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  SearchOutlined,
  FilterOutlined,
  PlusOutlined,
  ExportOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { SystemUser, RegisteredUser, UserFilters } from '@/types';
import dayjs from 'dayjs';

const { Text } = Typography;
const { Search } = Input;
const { Option } = Select;

// ============================================================================
// TYPES
// ============================================================================

interface UserTableProps {
  users: (SystemUser | RegisteredUser)[];
  loading?: boolean;
  userType: 'system' | 'registered';
  onEdit?: (user: SystemUser | RegisteredUser) => void;
  onDelete?: (user: SystemUser | RegisteredUser) => void;
  onAdd?: () => void;
  onRefresh?: () => void;
  onExport?: () => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  filters?: UserFilters;
  onFiltersChange?: (filters: UserFilters) => void;
}

// ============================================================================
// USER TABLE COMPONENT
// ============================================================================

export function UserTable({
  users,
  loading = false,
  userType,
  onEdit,
  onDelete,
  onAdd,
  onRefresh,
  onExport,
  pagination,
  filters = {},
  onFiltersChange
}: UserTableProps) {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // ========================================================================
  // HELPER FUNCTIONS
  // ========================================================================

  const isSystemUser = (user: SystemUser | RegisteredUser): user is SystemUser => {
    return 'role' in user;
  };

  const isRegisteredUser = (user: SystemUser | RegisteredUser): user is RegisteredUser => {
    return 'tier' in user;
  };

  // ========================================================================
  // COLUMN DEFINITIONS
  // ========================================================================

  const getColumns = () => {
    const baseColumns = [
      {
        title: 'User',
        dataIndex: 'username',
        key: 'username',
        render: (username: string, record: SystemUser | RegisteredUser) => (
          <Space>
            <Avatar size="small" icon={<UserOutlined />} />
            <div>
              <div className="font-medium">{username}</div>
              <Text type="secondary" className="text-xs">
                {record.email}
              </Text>
            </div>
          </Space>
        ),
        sorter: true,
      },
      {
        title: userType === 'system' ? 'Role' : 'Tier',
        dataIndex: userType === 'system' ? 'role' : 'tier',
        key: userType === 'system' ? 'role' : 'tier',
        render: (value: string) => {
          const colors = {
            // System roles
            admin: 'red',
            editor: 'blue',
            moderator: 'green',
            // User tiers
            free: 'default',
            premium: 'gold',
            enterprise: 'purple',
          };
          return (
            <Tag color={colors[value as keyof typeof colors] || 'default'}>
              {value.toUpperCase()}
            </Tag>
          );
        },
        filters: userType === 'system' 
          ? [
              { text: 'Admin', value: 'admin' },
              { text: 'Editor', value: 'editor' },
              { text: 'Moderator', value: 'moderator' },
            ]
          : [
              { text: 'Free', value: 'free' },
              { text: 'Premium', value: 'premium' },
              { text: 'Enterprise', value: 'enterprise' },
            ],
      },
      {
        title: 'Status',
        dataIndex: 'isActive',
        key: 'isActive',
        render: (isActive: boolean, record: SystemUser | RegisteredUser) => (
          <Space direction="vertical" size="small">
            <Tag color={isActive ? 'success' : 'error'}>
              {isActive ? 'Active' : 'Inactive'}
            </Tag>
            {isRegisteredUser(record) && (
              <Tag color={record.isEmailVerified ? 'success' : 'warning'} className="text-xs">
                {record.isEmailVerified ? 'Verified' : 'Unverified'}
              </Tag>
            )}
          </Space>
        ),
        filters: [
          { text: 'Active', value: true },
          { text: 'Inactive', value: false },
        ],
      }
    ];

    // Add API usage column for RegisteredUser
    if (userType === 'registered') {
      baseColumns.push({
        title: 'API Usage',
        key: 'apiUsage',
        render: (_, record: RegisteredUser) => {
          const percentage = record.apiCallsLimit 
            ? (record.apiCallsUsed / record.apiCallsLimit) * 100 
            : 0;
          
          return (
            <div>
              <div className="text-sm">
                {record.apiCallsUsed.toLocaleString()} / {record.apiCallsLimit?.toLocaleString() || '∞'}
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                <div 
                  className={`h-1 rounded-full ${
                    percentage > 90 ? 'bg-red-500' : 
                    percentage > 70 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${Math.min(percentage, 100)}%` }}
                />
              </div>
            </div>
          );
        },
      });
    }

    // Add common columns
    baseColumns.push(
      {
        title: 'Last Login',
        dataIndex: 'lastLoginAt',
        key: 'lastLoginAt',
        render: (date: string) => (
          <Tooltip title={dayjs(date).format('YYYY-MM-DD HH:mm:ss')}>
            <Text type="secondary" className="text-sm">
              {dayjs(date).fromNow()}
            </Text>
          </Tooltip>
        ),
        sorter: true,
      },
      {
        title: 'Created',
        dataIndex: 'createdAt',
        key: 'createdAt',
        render: (date: string) => (
          <Text type="secondary" className="text-sm">
            {dayjs(date).format('MMM DD, YYYY')}
          </Text>
        ),
        sorter: true,
      },
      {
        title: 'Actions',
        key: 'actions',
        width: 120,
        render: (_, record: SystemUser | RegisteredUser) => (
          <Space>
            <Tooltip title="Edit">
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={() => onEdit?.(record)}
              />
            </Tooltip>
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'edit',
                    label: 'Edit User',
                    icon: <EditOutlined />,
                    onClick: () => onEdit?.(record),
                  },
                  {
                    key: 'delete',
                    label: 'Delete User',
                    icon: <DeleteOutlined />,
                    danger: true,
                    onClick: () => handleDelete(record),
                  },
                ],
              }}
              trigger={['click']}
            >
              <Button type="text" size="small" icon={<MoreOutlined />} />
            </Dropdown>
          </Space>
        ),
      }
    );

    return baseColumns;
  };

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleDelete = (user: SystemUser | RegisteredUser) => {
    Modal.confirm({
      title: 'Delete User',
      content: `Are you sure you want to delete user "${user.username}"?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        onDelete?.(user);
        message.success('User deleted successfully');
      },
    });
  };

  const handleBulkDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('Please select users to delete');
      return;
    }

    Modal.confirm({
      title: 'Delete Selected Users',
      content: `Are you sure you want to delete ${selectedRowKeys.length} selected users?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        // Handle bulk delete
        setSelectedRowKeys([]);
        message.success(`${selectedRowKeys.length} users deleted successfully`);
      },
    });
  };

  const handleFilterChange = (key: keyof UserFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    onFiltersChange?.(newFilters);
  };

  // ========================================================================
  // ROW SELECTION
  // ========================================================================

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record: SystemUser | RegisteredUser) => ({
      disabled: false,
      name: record.username,
    }),
  };

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <div className="space-y-4">
      {/* Header Actions */}
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Search
                placeholder={`Search ${userType} users...`}
                allowClear
                style={{ width: 300 }}
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                onSearch={(value) => handleFilterChange('search', value)}
              />
              <Button
                icon={<FilterOutlined />}
                onClick={() => setShowFilters(!showFilters)}
              >
                Filters
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              {selectedRowKeys.length > 0 && (
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleBulkDelete}
                >
                  Delete ({selectedRowKeys.length})
                </Button>
              )}
              <Button
                icon={<ReloadOutlined />}
                onClick={onRefresh}
                loading={loading}
              >
                Refresh
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={onExport}
              >
                Export
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={onAdd}
              >
                Add {userType === 'system' ? 'System User' : 'User'}
              </Button>
            </Space>
          </Col>
        </Row>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded">
            <Row gutter={16}>
              <Col span={6}>
                <Select
                  placeholder={userType === 'system' ? 'Select Role' : 'Select Tier'}
                  allowClear
                  style={{ width: '100%' }}
                  value={userType === 'system' ? filters.role : filters.tier}
                  onChange={(value) => 
                    handleFilterChange(userType === 'system' ? 'role' : 'tier', value)
                  }
                >
                  {userType === 'system' ? (
                    <>
                      <Option value="admin">Admin</Option>
                      <Option value="editor">Editor</Option>
                      <Option value="moderator">Moderator</Option>
                    </>
                  ) : (
                    <>
                      <Option value="free">Free</Option>
                      <Option value="premium">Premium</Option>
                      <Option value="enterprise">Enterprise</Option>
                    </>
                  )}
                </Select>
              </Col>
              <Col span={6}>
                <Select
                  placeholder="Select Status"
                  allowClear
                  style={{ width: '100%' }}
                  value={filters.isActive}
                  onChange={(value) => handleFilterChange('isActive', value)}
                >
                  <Option value={true}>Active</Option>
                  <Option value={false}>Inactive</Option>
                </Select>
              </Col>
              <Col span={6}>
                <DatePicker
                  placeholder="Created From"
                  style={{ width: '100%' }}
                  onChange={(date) => handleFilterChange('createdFrom', date?.toISOString())}
                />
              </Col>
              <Col span={6}>
                <DatePicker
                  placeholder="Created To"
                  style={{ width: '100%' }}
                  onChange={(date) => handleFilterChange('createdTo', date?.toISOString())}
                />
              </Col>
            </Row>
          </div>
        )}
      </Card>

      {/* Data Table */}
      <Card>
        <Table
          rowSelection={rowSelection}
          columns={getColumns()}
          dataSource={users}
          loading={loading}
          pagination={pagination ? {
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} users`,
            onChange: pagination.onChange,
          } : false}
          rowKey="id"
          scroll={{ x: 1200 }}
          size="middle"
        />
      </Card>
    </div>
  );
}

export default UserTable;
