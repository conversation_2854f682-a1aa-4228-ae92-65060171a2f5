// APISportsGame CMS - Auth Module Types
// Authentication related types

import { BaseEntity } from '@/shared/types/common';

// ============================================================================
// USER TYPES
// ============================================================================

export interface SystemUser extends BaseEntity {
  username: string;
  email: string;
  role: 'admin' | 'editor' | 'moderator';
  isActive: boolean;
  lastLoginAt: Date;
}

export interface RegisteredUser extends BaseEntity {
  username: string;
  email: string;
  tier: 'free' | 'premium' | 'enterprise';
  isActive: boolean;
  isEmailVerified: boolean;
  apiCallsUsed: number;
  apiCallsLimit: number | null;
  subscriptionEndDate: Date | null;
  lastLoginAt: Date;
}

export type User = SystemUser | RegisteredUser;

// ============================================================================
// ROLE & TIER TYPES
// ============================================================================

export type SystemRole = 'admin' | 'editor' | 'moderator';
export type RegisteredUserTier = 'free' | 'premium' | 'enterprise';

// ============================================================================
// JWT TOKEN TYPES
// ============================================================================

export interface SystemUserToken {
  sub: number;
  username: string;
  email: string;
  role: SystemRole;
  userType: 'system';
  iat?: number;
  exp?: number;
}

export interface RegisteredUserToken {
  sub: number;
  username: string;
  email: string;
  tier: RegisteredUserTier;
  userType: 'registered';
  isEmailVerified: boolean;
  iat?: number;
  exp?: number;
}

export type UserToken = SystemUserToken | RegisteredUserToken;

// ============================================================================
// AUTH REQUEST/RESPONSE TYPES
// ============================================================================

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  user: SystemUser | RegisteredUser;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface RefreshTokenResponse {
  access_token: string;
  refresh_token: string;
}

// ============================================================================
// AUTH STATE TYPES
// ============================================================================

export interface AuthState {
  user: SystemUser | RegisteredUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  getProfile: () => Promise<void>;
  refreshToken: () => Promise<void>;
  clearError: () => void;
}

// ============================================================================
// PERMISSION TYPES
// ============================================================================

export interface PermissionState {
  // User type checks
  isSystemUser: boolean;
  isRegisteredUser: boolean;

  // Role checks (SystemUser)
  isAdmin: boolean;
  isEditor: boolean;
  isModerator: boolean;

  // Tier checks (RegisteredUser)
  isFree: boolean;
  isPremium: boolean;
  isEnterprise: boolean;

  // Permission checks
  canManageUsers: boolean;
  canManageLeagues: boolean;
  canManageTeams: boolean;
  canManageFixtures: boolean;
  canSync: boolean;
  canViewAnalytics: boolean;

  // Current user
  currentUser: SystemUser | RegisteredUser | null;
}

// ============================================================================
// AUTH HOOK TYPES
// ============================================================================

export interface UseAuthReturn extends AuthState, AuthActions { }

export interface UsePermissionsReturn extends PermissionState { }

// ============================================================================
// TYPE GUARDS
// ============================================================================

export const isSystemUser = (user: SystemUser | RegisteredUser | null): user is SystemUser => {
  return user !== null && 'role' in user;
};

export const isRegisteredUser = (user: SystemUser | RegisteredUser | null): user is RegisteredUser => {
  return user !== null && 'tier' in user;
};

export const isSystemUserToken = (token: UserToken): token is SystemUserToken => {
  return token.userType === 'system';
};

export const isRegisteredUserToken = (token: UserToken): token is RegisteredUserToken => {
  return token.userType === 'registered';
};

// ============================================================================
// EXPORT ALL
// ============================================================================

// Remove circular export
