// APISportsGame CMS - Users Module Types
// User management related types

import { BaseFilters, PaginatedResponse } from '@/shared/types/common';
import { SystemUser, RegisteredUser, SystemRole, RegisteredUserTier } from '@/modules/auth/types';

// ============================================================================
// USER MANAGEMENT TYPES
// ============================================================================

export type User = SystemUser | RegisteredUser;
export type UserType = 'system' | 'registered';

// ============================================================================
// FORM TYPES
// ============================================================================

export interface CreateSystemUserForm {
  username: string;
  email: string;
  password: string;
  role: SystemRole;
  isActive?: boolean;
}

export interface UpdateSystemUserForm {
  username?: string;
  email?: string;
  password?: string;
  role?: SystemRole;
  isActive?: boolean;
}

export interface CreateRegisteredUserForm {
  username: string;
  email: string;
  password: string;
  tier?: RegisteredUserTier;
  isActive?: boolean;
  isEmailVerified?: boolean;
  apiCallsLimit?: number;
  subscriptionEndDate?: string;
}

export interface UpdateRegisteredUserForm {
  username?: string;
  email?: string;
  password?: string;
  tier?: RegisteredUserTier;
  isActive?: boolean;
  isEmailVerified?: boolean;
  apiCallsUsed?: number;
  apiCallsLimit?: number;
  subscriptionEndDate?: string;
}

// ============================================================================
// FILTER TYPES
// ============================================================================

export interface UserFilters extends BaseFilters {
  role?: SystemRole;
  tier?: RegisteredUserTier;
  isActive?: boolean;
  isEmailVerified?: boolean;
  createdFrom?: string;
  createdTo?: string;
}

export interface SystemUserFilters extends BaseFilters {
  role?: SystemRole;
  isActive?: boolean;
  createdFrom?: string;
  createdTo?: string;
}

export interface RegisteredUserFilters extends BaseFilters {
  tier?: RegisteredUserTier;
  isActive?: boolean;
  isEmailVerified?: boolean;
  createdFrom?: string;
  createdTo?: string;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface UsersResponse extends PaginatedResponse<User> { }
export interface SystemUsersResponse extends PaginatedResponse<SystemUser> { }
export interface RegisteredUsersResponse extends PaginatedResponse<RegisteredUser> { }

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface UserTableProps {
  users: User[];
  loading?: boolean;
  userType: UserType;
  onEdit?: (user: User) => void;
  onDelete?: (user: User) => void;
  onAdd?: () => void;
  onRefresh?: () => void;
  onExport?: () => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  filters?: UserFilters;
  onFiltersChange?: (filters: UserFilters) => void;
}

export interface UserFormProps {
  userType: UserType;
  mode: 'create' | 'edit';
  initialValues?: Partial<User>;
  loading?: boolean;
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseUsersOptions {
  userType?: UserType;
  filters?: UserFilters;
  pagination?: {
    page: number;
    limit: number;
  };
  enabled?: boolean;
}

export interface UseUsersReturn {
  users: User[];
  loading: boolean;
  error: string | null;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  refetch: () => void;
}

export interface UseUserMutationsReturn {
  createUser: {
    mutate: (data: CreateSystemUserForm | CreateRegisteredUserForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  updateUser: {
    mutate: (id: number, data: UpdateSystemUserForm | UpdateRegisteredUserForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  deleteUser: {
    mutate: (id: number) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
}

// ============================================================================
// STATISTICS TYPES
// ============================================================================

export interface UserStatistics {
  total: number;
  active: number;
  inactive: number;
  systemUsers: {
    total: number;
    active: number;
    admins: number;
    editors: number;
    moderators: number;
  };
  registeredUsers: {
    total: number;
    active: number;
    verified: number;
    free: number;
    premium: number;
    enterprise: number;
  };
}

// ============================================================================
// TYPE GUARDS
// ============================================================================

export const isSystemUserForm = (
  form: CreateSystemUserForm | CreateRegisteredUserForm
): form is CreateSystemUserForm => {
  return 'role' in form;
};

export const isRegisteredUserForm = (
  form: CreateSystemUserForm | CreateRegisteredUserForm
): form is CreateRegisteredUserForm => {
  return 'tier' in form || !('role' in form);
};

// ============================================================================
// EXPORT ALL
// ============================================================================

// Remove circular export

// Re-export auth types for convenience
export type {
  SystemUser,
  RegisteredUser,
  SystemRole,
  RegisteredUserTier,
} from '@/modules/auth/types';
