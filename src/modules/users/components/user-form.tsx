'use client';

// APISportsGame CMS - User Form Component
// Reusable form cho create/edit SystemUser và RegisteredUser

import React, { useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Switch,
  Button,
  Space,
  Card,
  Row,
  Col,
  Typography,
  Divider,
  InputNumber,
  DatePicker,
  Alert
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  LockOutlined,
  SaveOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { User, UserFormProps } from '../types';
import { dateUtils } from '@/shared/utils/date';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// UserFormProps is now imported from types

// ============================================================================
// USER FORM COMPONENT
// ============================================================================

export function UserForm({
  userType,
  mode,
  initialValues,
  loading = false,
  onSubmit,
  onCancel
}: UserFormProps) {
  const [form] = Form.useForm();

  // ========================================================================
  // EFFECTS
  // ========================================================================

  useEffect(() => {
    if (initialValues) {
      // Convert dates for form
      const formValues = {
        ...initialValues,
        subscriptionEndDate: dateUtils.parseForForm(initialValues.subscriptionEndDate),
      };
      form.setFieldsValue(formValues);
    }
  }, [initialValues, form]);

  // ========================================================================
  // FORM VALIDATION RULES
  // ========================================================================

  const validationRules = {
    username: [
      { required: true, message: 'Username is required' },
      { min: 3, message: 'Username must be at least 3 characters' },
      { max: 50, message: 'Username must not exceed 50 characters' },
      { pattern: /^[a-zA-Z0-9_]+$/, message: 'Username can only contain letters, numbers, and underscores' }
    ],
    email: [
      { required: true, message: 'Email is required' },
      { type: 'email' as const, message: 'Please enter a valid email' }
    ],
    password: mode === 'create' ? [
      { required: true, message: 'Password is required' },
      { min: 6, message: 'Password must be at least 6 characters' },
      { max: 100, message: 'Password must not exceed 100 characters' }
    ] : [
      { min: 6, message: 'Password must be at least 6 characters' },
      { max: 100, message: 'Password must not exceed 100 characters' }
    ],
    role: [
      { required: true, message: 'Role is required' }
    ],
    tier: [
      { required: true, message: 'Tier is required' }
    ]
  };

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleSubmit = async (values: any) => {
    try {
      // Process form values
      const processedValues = {
        ...values,
        subscriptionEndDate: values.subscriptionEndDate
          ? dateUtils.toISOString(values.subscriptionEndDate)
          : null,
      };

      // Remove password if empty in edit mode
      if (mode === 'edit' && !processedValues.password) {
        delete processedValues.password;
      }

      onSubmit(processedValues);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleReset = () => {
    form.resetFields();
  };

  // ========================================================================
  // RENDER HELPERS
  // ========================================================================

  const renderSystemUserFields = () => (
    <>
      <Col span={24}>
        <Form.Item
          name="role"
          label="Role"
          rules={validationRules.role}
        >
          <Select placeholder="Select user role">
            <Option value="admin">
              <Space>
                <span>👑</span>
                <div>
                  <div>Admin</div>
                  <Text type="secondary" className="text-xs">
                    Full system access
                  </Text>
                </div>
              </Space>
            </Option>
            <Option value="editor">
              <Space>
                <span>✏️</span>
                <div>
                  <div>Editor</div>
                  <Text type="secondary" className="text-xs">
                    Content management
                  </Text>
                </div>
              </Space>
            </Option>
            <Option value="moderator">
              <Space>
                <span>🛡️</span>
                <div>
                  <div>Moderator</div>
                  <Text type="secondary" className="text-xs">
                    Content moderation
                  </Text>
                </div>
              </Space>
            </Option>
          </Select>
        </Form.Item>
      </Col>
    </>
  );

  const renderRegisteredUserFields = () => (
    <>
      <Col span={12}>
        <Form.Item
          name="tier"
          label="Subscription Tier"
          rules={validationRules.tier}
        >
          <Select placeholder="Select subscription tier">
            <Option value="free">
              <Space>
                <span>🆓</span>
                <div>
                  <div>Free</div>
                  <Text type="secondary" className="text-xs">
                    Basic features
                  </Text>
                </div>
              </Space>
            </Option>
            <Option value="premium">
              <Space>
                <span>⭐</span>
                <div>
                  <div>Premium</div>
                  <Text type="secondary" className="text-xs">
                    Enhanced features
                  </Text>
                </div>
              </Space>
            </Option>
            <Option value="enterprise">
              <Space>
                <span>🏢</span>
                <div>
                  <div>Enterprise</div>
                  <Text type="secondary" className="text-xs">
                    Full features
                  </Text>
                </div>
              </Space>
            </Option>
          </Select>
        </Form.Item>
      </Col>

      <Col span={12}>
        <Form.Item
          name="isEmailVerified"
          label="Email Verified"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </Col>

      <Col span={12}>
        <Form.Item
          name="apiCallsUsed"
          label="API Calls Used"
        >
          <InputNumber
            min={0}
            style={{ width: '100%' }}
            formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={value => value!.replace(/\$\s?|(,*)/g, '')}
          />
        </Form.Item>
      </Col>

      <Col span={12}>
        <Form.Item
          name="apiCallsLimit"
          label="API Calls Limit"
        >
          <InputNumber
            min={0}
            style={{ width: '100%' }}
            formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={value => value!.replace(/\$\s?|(,*)/g, '')}
            placeholder="Leave empty for unlimited"
          />
        </Form.Item>
      </Col>

      <Col span={24}>
        <Form.Item
          name="subscriptionEndDate"
          label="Subscription End Date"
        >
          <DatePicker
            style={{ width: '100%' }}
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="Select subscription end date"
          />
        </Form.Item>
      </Col>
    </>
  );

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <Card>
      <div className="mb-6">
        <Title level={4}>
          {mode === 'create' ? 'Create' : 'Edit'} {userType === 'system' ? 'System User' : 'Registered User'}
        </Title>
        <Text type="secondary">
          {mode === 'create'
            ? `Add a new ${userType} user to the system`
            : `Update ${userType} user information`
          }
        </Text>
      </div>

      {/* Info Alert */}
      <Alert
        message={`${userType === 'system' ? 'System User' : 'Registered User'} Information`}
        description={
          userType === 'system'
            ? 'System users have administrative access to the CMS. Choose roles carefully.'
            : 'Registered users are API consumers with different subscription tiers and usage limits.'
        }
        type="info"
        showIcon
        className="mb-6"
      />

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        autoComplete="off"
        size="large"
      >
        <Row gutter={16}>
          {/* Basic Information */}
          <Col span={24}>
            <Divider orientation="left">Basic Information</Divider>
          </Col>

          <Col span={12}>
            <Form.Item
              name="username"
              label="Username"
              rules={validationRules.username}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Enter username"
                disabled={mode === 'edit'}
              />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="email"
              label="Email"
              rules={validationRules.email}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="Enter email address"
              />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item
              name="password"
              label={mode === 'create' ? 'Password' : 'New Password (leave empty to keep current)'}
              rules={validationRules.password}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder={mode === 'create' ? 'Enter password' : 'Enter new password'}
              />
            </Form.Item>
          </Col>

          {/* Role/Tier Specific Fields */}
          <Col span={24}>
            <Divider orientation="left">
              {userType === 'system' ? 'Role & Permissions' : 'Subscription & Usage'}
            </Divider>
          </Col>

          {userType === 'system' ? renderSystemUserFields() : renderRegisteredUserFields()}

          {/* Status */}
          <Col span={24}>
            <Divider orientation="left">Status</Divider>
          </Col>

          <Col span={12}>
            <Form.Item
              name="isActive"
              label="Active Status"
              valuePropName="checked"
              initialValue={true}
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>

        {/* Form Actions */}
        <Divider />
        <Row justify="end">
          <Col>
            <Space>
              <Button onClick={handleReset}>
                Reset
              </Button>
              <Button onClick={onCancel}>
                <CloseOutlined />
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<SaveOutlined />}
              >
                {mode === 'create' ? 'Create User' : 'Update User'}
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    </Card>
  );
}

export default UserForm;
