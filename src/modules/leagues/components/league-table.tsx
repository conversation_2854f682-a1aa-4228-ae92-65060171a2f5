// APISportsGame CMS - League Table Component
// Displays leagues in a data table với advanced features

'use client';

import React, { useState } from 'react';
import {
  Table,
  Card,
  Space,
  Button,
  Input,
  Select,
  Tag,
  Avatar,
  Tooltip,
  Modal,
  Typography,
  Row,
  Col,
  Dropdown,
  message,
  Image,
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  FilterOutlined,
  SyncOutlined,
  PlusOutlined,
  ExportOutlined,
  ReloadOutlined,
  MoreOutlined,
  TrophyOutlined,
  CalendarOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import { League, LeagueFilters, LeagueTableProps, getLeagueStatus } from '../types';
import { dateUtils } from '@/shared/utils/date';

const { Text } = Typography;
const { Search } = Input;
const { Option } = Select;

// ============================================================================
// LEAGUE TABLE COMPONENT
// ============================================================================

export const LeagueTable: React.FC<LeagueTableProps> = ({
  leagues,
  loading = false,
  onEdit,
  onDelete,
  onAdd,
  onRefresh,
  onExport,
  onSync,
  pagination,
  filters = {},
  onFiltersChange,
}) => {
  // ========================================================================
  // STATE
  // ========================================================================

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchText, setSearchText] = useState(filters.search || '');

  // ========================================================================
  // COLUMN DEFINITIONS
  // ========================================================================

  const columns = [
    {
      title: 'League',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: League) => (
        <Space>
          <Avatar 
            size="small" 
            src={record.logo} 
            icon={<TrophyOutlined />}
          />
          <div>
            <div className="font-medium">{name}</div>
            <Text type="secondary" className="text-sm">
              Season {record.season}
            </Text>
          </div>
        </Space>
      ),
      sorter: true,
    },
    {
      title: 'Country',
      dataIndex: 'country',
      key: 'country',
      render: (country: string, record: League) => (
        <Space>
          {record.flag && (
            <Image
              src={record.flag}
              alt={country}
              width={20}
              height={15}
              preview={false}
            />
          )}
          <Text>{country}</Text>
        </Space>
      ),
      filters: [
        { text: 'England', value: 'England' },
        { text: 'Spain', value: 'Spain' },
        { text: 'Germany', value: 'Germany' },
        { text: 'Italy', value: 'Italy' },
        { text: 'France', value: 'France' },
      ],
      sorter: true,
    },
    {
      title: 'Season',
      dataIndex: 'season',
      key: 'season',
      render: (season: number) => (
        <Tag color="blue">{season}</Tag>
      ),
      sorter: true,
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record: League) => {
        const status = getLeagueStatus(record);
        const statusConfig = {
          active: { color: 'success', text: 'Active' },
          upcoming: { color: 'processing', text: 'Upcoming' },
          finished: { color: 'default', text: 'Finished' },
          inactive: { color: 'error', text: 'Inactive' },
        };
        
        const config = statusConfig[status];
        
        return (
          <Space direction="vertical" size="small">
            <Tag color={config.color}>{config.text}</Tag>
            {record.current && (
              <Tag color="gold" size="small">Current</Tag>
            )}
          </Space>
        );
      },
      filters: [
        { text: 'Active', value: 'active' },
        { text: 'Upcoming', value: 'upcoming' },
        { text: 'Finished', value: 'finished' },
        { text: 'Inactive', value: 'inactive' },
      ],
    },
    {
      title: 'Duration',
      key: 'duration',
      render: (_, record: League) => (
        <Space direction="vertical" size="small">
          <Text type="secondary" className="text-sm">
            <CalendarOutlined /> {dateUtils.formatDisplay(record.start)}
          </Text>
          <Text type="secondary" className="text-sm">
            to {dateUtils.formatDisplay(record.end)}
          </Text>
        </Space>
      ),
    },
    {
      title: 'Coverage',
      key: 'coverage',
      render: (_, record: League) => {
        const coverageItems = [];
        if (record.coverage.fixtures.events) coverageItems.push('Events');
        if (record.coverage.standings) coverageItems.push('Standings');
        if (record.coverage.players) coverageItems.push('Players');
        
        return (
          <Space wrap>
            {coverageItems.slice(0, 2).map(item => (
              <Tag key={item} size="small">{item}</Tag>
            ))}
            {coverageItems.length > 2 && (
              <Tooltip title={coverageItems.slice(2).join(', ')}>
                <Tag size="small">+{coverageItems.length - 2}</Tag>
              </Tooltip>
            )}
          </Space>
        );
      },
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => (
        <Text type="secondary" className="text-sm">
          {dateUtils.formatDisplay(date)}
        </Text>
      ),
      sorter: true,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record: League) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(record)}
            />
          </Tooltip>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'delete',
                  label: 'Delete',
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: () => handleDelete(record),
                },
              ],
            }}
            trigger={['click']}
          >
            <Button
              type="text"
              size="small"
              icon={<MoreOutlined />}
            />
          </Dropdown>
        </Space>
      ),
    },
  ];

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleView = (league: League) => {
    // Navigate to league details page
    console.log('View league:', league);
  };

  const handleDelete = (league: League) => {
    Modal.confirm({
      title: 'Delete League',
      content: `Are you sure you want to delete league "${league.name}"?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        onDelete?.(league);
      },
    });
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    onFiltersChange?.({ ...filters, search: value });
  };

  const handleFilterChange = (key: string, value: any) => {
    onFiltersChange?.({ ...filters, [key]: value });
  };

  const handleBulkDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('Please select leagues to delete');
      return;
    }

    Modal.confirm({
      title: 'Delete Selected Leagues',
      content: `Are you sure you want to delete ${selectedRowKeys.length} selected leagues?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        // Handle bulk delete
        message.success(`${selectedRowKeys.length} leagues deleted successfully`);
        setSelectedRowKeys([]);
      },
    });
  };

  // ========================================================================
  // ROW SELECTION
  // ========================================================================

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record: League) => ({
      disabled: false,
      name: record.name,
    }),
  };

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <Card>
      {/* Table Header */}
      <div className="mb-4">
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Space>
              <Search
                placeholder="Search leagues..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onSearch={handleSearch}
                style={{ width: 300 }}
                allowClear
              />
              <Select
                placeholder="Country"
                value={filters.country}
                onChange={(value) => handleFilterChange('country', value)}
                style={{ width: 150 }}
                allowClear
              >
                <Option value="England">England</Option>
                <Option value="Spain">Spain</Option>
                <Option value="Germany">Germany</Option>
                <Option value="Italy">Italy</Option>
                <Option value="France">France</Option>
              </Select>
              <Select
                placeholder="Season"
                value={filters.season}
                onChange={(value) => handleFilterChange('season', value)}
                style={{ width: 120 }}
                allowClear
              >
                {Array.from({ length: 5 }, (_, i) => {
                  const year = new Date().getFullYear() - i;
                  return (
                    <Option key={year} value={year}>{year}</Option>
                  );
                })}
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              {selectedRowKeys.length > 0 && (
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleBulkDelete}
                >
                  Delete ({selectedRowKeys.length})
                </Button>
              )}
              <Button
                icon={<SyncOutlined />}
                onClick={onSync}
                loading={loading}
              >
                Sync
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={onExport}
              >
                Export
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={onRefresh}
                loading={loading}
              >
                Refresh
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={onAdd}
              >
                Add League
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Data Table */}
      <Table
        columns={columns}
        dataSource={leagues}
        rowKey="id"
        loading={loading}
        pagination={pagination}
        rowSelection={rowSelection}
        scroll={{ x: 1200 }}
        size="middle"
      />
    </Card>
  );
};
