// APISportsGame CMS - League Form Component
// Form for creating và editing leagues

'use client';

import React, { useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  DatePicker,
  Switch,
  Button,
  Card,
  Row,
  Col,
  Space,
  Typography,
  Divider,
  Upload,
  message,
  Collapse,
} from 'antd';
import {
  SaveOutlined,
  CloseOutlined,
  UploadOutlined,
  TrophyOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { League, LeagueFormProps, LEAGUE_COUNTRIES, LEAGUE_SEASONS } from '../types';
import { dateUtils } from '@/shared/utils/date';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { Panel } = Collapse;

// ============================================================================
// LEAGUE FORM COMPONENT
// ============================================================================

export const LeagueForm: React.FC<LeagueFormProps> = ({
  mode,
  initialValues,
  loading = false,
  onSubmit,
  onCancel,
}) => {
  // ========================================================================
  // FORM SETUP
  // ========================================================================

  const [form] = Form.useForm();

  useEffect(() => {
    if (initialValues) {
      // Convert dates for form
      const formValues = {
        ...initialValues,
        dateRange: initialValues.start && initialValues.end 
          ? [dateUtils.parseForForm(initialValues.start), dateUtils.parseForForm(initialValues.end)]
          : null,
      };
      form.setFieldsValue(formValues);
    }
  }, [initialValues, form]);

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  const handleSubmit = async (values: any) => {
    try {
      // Process form values
      const processedValues = {
        ...values,
        start: values.dateRange?.[0]?.toISOString(),
        end: values.dateRange?.[1]?.toISOString(),
      };

      // Remove dateRange field
      delete processedValues.dateRange;

      await onSubmit(processedValues);
      
      if (mode === 'create') {
        form.resetFields();
        message.success('League created successfully');
      } else {
        message.success('League updated successfully');
      }
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // ========================================================================
  // RENDER
  // ========================================================================

  return (
    <Card>
      <div className="mb-6">
        <Title level={3} className="mb-2">
          <TrophyOutlined className="mr-2" />
          {mode === 'create' ? 'Create New League' : 'Edit League'}
        </Title>
        <Text type="secondary">
          {mode === 'create' 
            ? 'Add a new league to the system'
            : 'Update league information and settings'
          }
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        disabled={loading}
        size="large"
      >
        {/* Basic Information */}
        <Card size="small" className="mb-4">
          <Title level={5} className="mb-4">Basic Information</Title>
          
          <Row gutter={16}>
            <Col xs={24} md={12}>
              <Form.Item
                name="name"
                label="League Name"
                rules={[
                  { required: true, message: 'Please enter league name' },
                  { min: 2, message: 'Name must be at least 2 characters' },
                ]}
              >
                <Input placeholder="e.g., Premier League" />
              </Form.Item>
            </Col>
            
            <Col xs={24} md={12}>
              <Form.Item
                name="country"
                label="Country"
                rules={[{ required: true, message: 'Please select country' }]}
              >
                <Select
                  placeholder="Select country"
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {LEAGUE_COUNTRIES.map(country => (
                    <Option key={country} value={country}>{country}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col xs={24} md={8}>
              <Form.Item
                name="season"
                label="Season"
                rules={[{ required: true, message: 'Please select season' }]}
              >
                <Select placeholder="Select season">
                  {LEAGUE_SEASONS.map(season => (
                    <Option key={season} value={season}>{season}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            
            <Col xs={24} md={16}>
              <Form.Item
                name="dateRange"
                label="Season Duration"
                rules={[{ required: true, message: 'Please select season dates' }]}
              >
                <RangePicker 
                  style={{ width: '100%' }}
                  placeholder={['Start Date', 'End Date']}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col xs={24} md={12}>
              <Form.Item
                name="logo"
                label="League Logo URL"
              >
                <Input placeholder="https://example.com/logo.png" />
              </Form.Item>
            </Col>
            
            <Col xs={24} md={12}>
              <Form.Item
                name="flag"
                label="Country Flag URL"
              >
                <Input placeholder="https://example.com/flag.png" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="current"
            label="Current Season"
            valuePropName="checked"
          >
            <Switch checkedChildren="Yes" unCheckedChildren="No" />
          </Form.Item>
        </Card>

        {/* Coverage Settings */}
        <Card size="small" className="mb-4">
          <Title level={5} className="mb-4">
            <SettingOutlined className="mr-2" />
            Coverage Settings
          </Title>
          
          <Collapse ghost>
            <Panel header="Fixture Coverage" key="fixtures">
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name={['coverage', 'fixtures', 'events']}
                    label="Events"
                    valuePropName="checked"
                  >
                    <Switch size="small" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name={['coverage', 'fixtures', 'lineups']}
                    label="Lineups"
                    valuePropName="checked"
                  >
                    <Switch size="small" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name={['coverage', 'fixtures', 'statistics_fixtures']}
                    label="Fixture Statistics"
                    valuePropName="checked"
                  >
                    <Switch size="small" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name={['coverage', 'fixtures', 'statistics_players']}
                    label="Player Statistics"
                    valuePropName="checked"
                  >
                    <Switch size="small" />
                  </Form.Item>
                </Col>
              </Row>
            </Panel>
            
            <Panel header="Additional Coverage" key="additional">
              <Row gutter={16}>
                <Col xs={24} md={8}>
                  <Form.Item
                    name={['coverage', 'standings']}
                    label="Standings"
                    valuePropName="checked"
                  >
                    <Switch size="small" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name={['coverage', 'players']}
                    label="Players"
                    valuePropName="checked"
                  >
                    <Switch size="small" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name={['coverage', 'top_scorers']}
                    label="Top Scorers"
                    valuePropName="checked"
                  >
                    <Switch size="small" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name={['coverage', 'top_assists']}
                    label="Top Assists"
                    valuePropName="checked"
                  >
                    <Switch size="small" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name={['coverage', 'top_cards']}
                    label="Top Cards"
                    valuePropName="checked"
                  >
                    <Switch size="small" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name={['coverage', 'injuries']}
                    label="Injuries"
                    valuePropName="checked"
                  >
                    <Switch size="small" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name={['coverage', 'predictions']}
                    label="Predictions"
                    valuePropName="checked"
                  >
                    <Switch size="small" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name={['coverage', 'odds']}
                    label="Odds"
                    valuePropName="checked"
                  >
                    <Switch size="small" />
                  </Form.Item>
                </Col>
              </Row>
            </Panel>
          </Collapse>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end">
          <Space>
            <Button
              size="large"
              icon={<CloseOutlined />}
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              size="large"
              icon={<SaveOutlined />}
              htmlType="submit"
              loading={loading}
            >
              {mode === 'create' ? 'Create League' : 'Update League'}
            </Button>
          </Space>
        </div>
      </Form>
    </Card>
  );
};
