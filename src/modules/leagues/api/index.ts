// APISportsGame CMS - League API Module
// League management API calls

import axios, { AxiosInstance } from 'axios';
import { API_CONFIG } from '@/shared/constants';
import { apiUtils, PaginatedResponse } from '@/shared/utils/api';
import {
  League,
  LeagueFilters,
  CreateLeagueForm,
  UpdateLeagueForm,
  LeagueStatsResponse,
  LeagueSyncStatus,
  LeagueSyncOptions,
} from '../types';

// ============================================================================
// LEAGUE API ENDPOINTS
// ============================================================================

const LEAGUE_ENDPOINTS = {
  list: '/leagues',
  active: '/leagues/active',
  byId: (id: number) => `/leagues/${id}`,
  create: '/leagues',
  update: (id: number) => `/leagues/${id}`,
  delete: (id: number) => `/leagues/${id}`,
  stats: '/leagues/stats',
  sync: '/sync/leagues',
  syncStatus: '/sync/leagues/status',
  fixtures: (id: number) => `/leagues/${id}/fixtures`,
  standings: (id: number) => `/leagues/${id}/standings`,
  teams: (id: number) => `/leagues/${id}/teams`,
} as const;

// ============================================================================
// LEAGUE API CLIENT
// ============================================================================

class LeagueApiClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor - add JWT token
    this.instance.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(apiUtils.handleError(error))
    );

    // Response interceptor - handle errors
    this.instance.interceptors.response.use(
      (response) => response,
      (error) => Promise.reject(apiUtils.handleError(error))
    );
  }

  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('access_token');
    }
    return null;
  }

  // ========================================================================
  // LEAGUE CRUD OPERATIONS
  // ========================================================================

  /**
   * Get paginated list of leagues
   */
  async getLeagues(
    filters: LeagueFilters = {},
    page = 1,
    limit = 10
  ): Promise<PaginatedResponse<League>> {
    try {
      const params = {
        ...apiUtils.formatFilterParams(filters),
        ...apiUtils.formatPaginationParams(page, limit),
      };

      const response = await this.instance.get<PaginatedResponse<League>>(
        LEAGUE_ENDPOINTS.list,
        { params }
      );

      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Get active leagues only
   */
  async getActiveLeagues(): Promise<League[]> {
    try {
      const response = await this.instance.get<League[]>(
        LEAGUE_ENDPOINTS.active
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Get league by ID
   */
  async getLeagueById(id: number): Promise<League> {
    try {
      const response = await this.instance.get<League>(
        LEAGUE_ENDPOINTS.byId(id)
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Create new league
   */
  async createLeague(data: CreateLeagueForm): Promise<League> {
    try {
      const response = await this.instance.post<League>(
        LEAGUE_ENDPOINTS.create,
        data
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Update existing league
   */
  async updateLeague(id: number, data: UpdateLeagueForm): Promise<League> {
    try {
      const response = await this.instance.put<League>(
        LEAGUE_ENDPOINTS.update(id),
        data
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Delete league
   */
  async deleteLeague(id: number): Promise<void> {
    try {
      await this.instance.delete(LEAGUE_ENDPOINTS.delete(id));
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  // ========================================================================
  // LEAGUE STATISTICS
  // ========================================================================

  /**
   * Get league statistics
   */
  async getLeagueStats(): Promise<LeagueStatsResponse> {
    try {
      const response = await this.instance.get<LeagueStatsResponse>(
        LEAGUE_ENDPOINTS.stats
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  // ========================================================================
  // LEAGUE SYNC OPERATIONS
  // ========================================================================

  /**
   * Sync leagues from external API
   */
  async syncLeagues(options: LeagueSyncOptions = {}): Promise<void> {
    try {
      await this.instance.post(LEAGUE_ENDPOINTS.sync, options);
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Get sync status
   */
  async getSyncStatus(): Promise<LeagueSyncStatus> {
    try {
      const response = await this.instance.get<LeagueSyncStatus>(
        LEAGUE_ENDPOINTS.syncStatus
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  // ========================================================================
  // LEAGUE RELATED DATA
  // ========================================================================

  /**
   * Get league fixtures
   */
  async getLeagueFixtures(
    id: number,
    filters: any = {}
  ): Promise<any[]> {
    try {
      const params = apiUtils.formatFilterParams(filters);
      const response = await this.instance.get(
        LEAGUE_ENDPOINTS.fixtures(id),
        { params }
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Get league standings
   */
  async getLeagueStandings(id: number): Promise<any[]> {
    try {
      const response = await this.instance.get(
        LEAGUE_ENDPOINTS.standings(id)
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Get league teams
   */
  async getLeagueTeams(id: number): Promise<any[]> {
    try {
      const response = await this.instance.get(
        LEAGUE_ENDPOINTS.teams(id)
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  // ========================================================================
  // BULK OPERATIONS
  // ========================================================================

  /**
   * Bulk delete leagues
   */
  async bulkDeleteLeagues(ids: number[]): Promise<void> {
    try {
      await this.instance.delete(LEAGUE_ENDPOINTS.list, {
        data: { ids }
      });
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }

  /**
   * Export leagues data
   */
  async exportLeagues(filters: LeagueFilters = {}): Promise<Blob> {
    try {
      const params = apiUtils.formatFilterParams(filters);
      const response = await this.instance.get(
        `${LEAGUE_ENDPOINTS.list}/export`,
        {
          params,
          responseType: 'blob'
        }
      );
      return response.data;
    } catch (error) {
      throw apiUtils.handleError(error);
    }
  }
}

// ============================================================================
// EXPORT SINGLETON INSTANCE
// ============================================================================

export const leagueApi = new LeagueApiClient();
export default leagueApi;
