// APISportsGame CMS - Leagues Module Types
// League management related types

import { BaseEntity, BaseFilters, PaginatedResponse } from '@/shared/types/common';

// ============================================================================
// LEAGUE ENTITY TYPES
// ============================================================================

export interface League extends BaseEntity {
  name: string;
  country: string;
  logo: string;
  flag: string;
  season: number;
  start: string;
  end: string;
  current: boolean;
  coverage: {
    fixtures: {
      events: boolean;
      lineups: boolean;
      statistics_fixtures: boolean;
      statistics_players: boolean;
    };
    standings: boolean;
    players: boolean;
    top_scorers: boolean;
    top_assists: boolean;
    top_cards: boolean;
    injuries: boolean;
    predictions: boolean;
    odds: boolean;
  };
}

// ============================================================================
// FORM TYPES
// ============================================================================

export interface CreateLeagueForm {
  name: string;
  country: string;
  logo?: string;
  flag?: string;
  season: number;
  start: string;
  end: string;
  current?: boolean;
  coverage?: Partial<League['coverage']>;
}

export interface UpdateLeagueForm {
  name?: string;
  country?: string;
  logo?: string;
  flag?: string;
  season?: number;
  start?: string;
  end?: string;
  current?: boolean;
  coverage?: Partial<League['coverage']>;
}

// ============================================================================
// FILTER TYPES
// ============================================================================

export interface LeagueFilters extends BaseFilters {
  country?: string;
  season?: number;
  current?: boolean;
  hasFixtures?: boolean;
  hasStandings?: boolean;
  createdFrom?: string;
  createdTo?: string;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface LeaguesResponse extends PaginatedResponse<League> {}

export interface LeagueStatsResponse {
  total: number;
  active: number;
  current: number;
  byCountry: Record<string, number>;
  bySeason: Record<string, number>;
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface LeagueTableProps {
  leagues: League[];
  loading?: boolean;
  onEdit?: (league: League) => void;
  onDelete?: (league: League) => void;
  onAdd?: () => void;
  onRefresh?: () => void;
  onExport?: () => void;
  onSync?: () => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  filters?: LeagueFilters;
  onFiltersChange?: (filters: LeagueFilters) => void;
}

export interface LeagueFormProps {
  mode: 'create' | 'edit';
  initialValues?: Partial<League>;
  loading?: boolean;
  onSubmit: (values: CreateLeagueForm | UpdateLeagueForm) => void;
  onCancel: () => void;
}

export interface LeagueCardProps {
  league: League;
  onEdit?: (league: League) => void;
  onDelete?: (league: League) => void;
  onView?: (league: League) => void;
}

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseLeaguesOptions {
  filters?: LeagueFilters;
  pagination?: {
    page: number;
    limit: number;
  };
  enabled?: boolean;
}

export interface UseLeaguesReturn {
  leagues: League[];
  loading: boolean;
  error: string | null;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  refetch: () => void;
}

export interface UseLeagueMutationsReturn {
  createLeague: {
    mutate: (data: CreateLeagueForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  updateLeague: {
    mutate: (id: number, data: UpdateLeagueForm) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  deleteLeague: {
    mutate: (id: number) => Promise<void>;
    loading: boolean;
    error: string | null;
  };
  syncLeagues: {
    mutate: () => Promise<void>;
    loading: boolean;
    error: string | null;
  };
}

// ============================================================================
// SYNC TYPES
// ============================================================================

export interface LeagueSyncStatus {
  isRunning: boolean;
  lastSync: Date | null;
  nextScheduled: Date | null;
  totalSynced: number;
  errors: string[];
  progress?: {
    current: number;
    total: number;
    percentage: number;
  };
}

export interface LeagueSyncOptions {
  countries?: string[];
  seasons?: number[];
  forceUpdate?: boolean;
  includeInactive?: boolean;
}

// ============================================================================
// COVERAGE TYPES
// ============================================================================

export interface CoverageConfig {
  fixtures: {
    events: boolean;
    lineups: boolean;
    statistics_fixtures: boolean;
    statistics_players: boolean;
  };
  standings: boolean;
  players: boolean;
  top_scorers: boolean;
  top_assists: boolean;
  top_cards: boolean;
  injuries: boolean;
  predictions: boolean;
  odds: boolean;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type LeagueStatus = 'active' | 'inactive' | 'upcoming' | 'finished';
export type LeagueSeason = number;
export type LeagueCountry = string;

// ============================================================================
// TYPE GUARDS
// ============================================================================

export const isActiveLeague = (league: League): boolean => {
  return league.current && new Date(league.end) > new Date();
};

export const isUpcomingLeague = (league: League): boolean => {
  return new Date(league.start) > new Date();
};

export const isFinishedLeague = (league: League): boolean => {
  return new Date(league.end) < new Date();
};

export const getLeagueStatus = (league: League): LeagueStatus => {
  if (isUpcomingLeague(league)) return 'upcoming';
  if (isFinishedLeague(league)) return 'finished';
  if (isActiveLeague(league)) return 'active';
  return 'inactive';
};

// ============================================================================
// CONSTANTS
// ============================================================================

export const LEAGUE_COUNTRIES = [
  'England', 'Spain', 'Germany', 'Italy', 'France',
  'Netherlands', 'Portugal', 'Brazil', 'Argentina',
  'Mexico', 'United States', 'Japan', 'South Korea'
] as const;

export const CURRENT_SEASON = new Date().getFullYear();

export const LEAGUE_SEASONS = Array.from(
  { length: 10 }, 
  (_, i) => CURRENT_SEASON - i
);

// ============================================================================
// EXPORT ALL
// ============================================================================

// Remove circular export
