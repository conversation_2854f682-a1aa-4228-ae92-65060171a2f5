// APISportsGame CMS - API Client
// Base API client setup theo CMS Development Guide

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  SystemUser, 
  RegisteredUser, 
  League, 
  Team, 
  Fixture,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  PaginatedResponse,
  ApiError,
  DashboardStats,
  SyncStatus
} from '@/types';

// ============================================================================
// API CONFIGURATION
// ============================================================================

const API_BASE_URL = 'http://localhost:3000';

// API Endpoints theo CMS Development Guide
export const endpoints = {
  // Authentication endpoints
  auth: {
    login: '/auth/login',
    profile: '/auth/profile',
    refresh: '/auth/refresh',
    logout: '/auth/logout',
    adminRegister: '/auth/admin/register'
  },
  // RegisteredUser endpoints
  users: {
    register: '/users/register',
    login: '/users/login',
    verifyEmail: '/users/verify-email',
    profile: '/users/profile',
    apiUsage: '/users/api-usage'
  },
  // League endpoints
  leagues: {
    list: '/leagues',
    active: '/leagues/active',
    byId: (id: number) => `/leagues/${id}`,
    create: '/leagues',
    update: (id: number) => `/leagues/${id}`,
    delete: (id: number) => `/leagues/${id}`,
    fixtures: (id: number) => `/leagues/${id}/fixtures`
  },
  // Team endpoints
  teams: {
    list: '/teams',
    byId: (id: number) => `/teams/${id}`,
    create: '/teams',
    update: (id: number) => `/teams/${id}`,
    delete: (id: number) => `/teams/${id}`,
    fixtures: (id: number) => `/teams/${id}/fixtures`
  },
  // Fixture endpoints
  fixtures: {
    list: '/fixtures',
    byId: (id: number) => `/fixtures/${id}`,
    live: '/fixtures/live',
    today: '/fixtures/today',
    byDate: (date: string) => `/fixtures/date/${date}`,
    create: '/fixtures',
    update: (id: number) => `/fixtures/${id}`,
    delete: (id: number) => `/fixtures/${id}`
  },
  // Sync endpoints
  sync: {
    leagues: '/sync/leagues',
    teams: '/sync/teams',
    fixtures: '/sync/fixtures',
    daily: '/sync/daily',
    status: '/sync/status'
  }
};

// ============================================================================
// AXIOS INSTANCE SETUP
// ============================================================================

class ApiClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor - thêm JWT token
    this.instance.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor - xử lý errors
    this.instance.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Handle 401 errors - token expired
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            await this.refreshToken();
            const token = this.getToken();
            if (token) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.instance(originalRequest);
            }
          } catch (refreshError) {
            this.logout();
            window.location.href = '/auth/login';
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('access_token');
    }
    return null;
  }

  private setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token);
    }
  }

  private removeToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
  }

  private async refreshToken(): Promise<void> {
    const refreshToken = typeof window !== 'undefined' 
      ? localStorage.getItem('refresh_token') 
      : null;
    
    if (!refreshToken) {
      throw new Error('No refresh token');
    }

    const response = await this.instance.post(endpoints.auth.refresh, {
      refresh_token: refreshToken
    });

    const { access_token } = response.data;
    this.setToken(access_token);
  }

  private logout(): void {
    this.removeToken();
  }

  // ============================================================================
  // AUTHENTICATION METHODS
  // ============================================================================

  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.instance.post<LoginResponse>(
      endpoints.auth.login, 
      credentials
    );
    
    const { access_token, refresh_token } = response.data;
    this.setToken(access_token);
    
    if (typeof window !== 'undefined') {
      localStorage.setItem('refresh_token', refresh_token);
    }
    
    return response.data;
  }

  async getProfile(): Promise<SystemUser | RegisteredUser> {
    const response = await this.instance.get<SystemUser | RegisteredUser>(
      endpoints.auth.profile
    );
    return response.data;
  }

  async logoutUser(): Promise<void> {
    try {
      await this.instance.post(endpoints.auth.logout);
    } finally {
      this.logout();
    }
  }

  async createSystemUser(userData: any): Promise<SystemUser> {
    const response = await this.instance.post<SystemUser>(
      endpoints.auth.adminRegister,
      userData
    );
    return response.data;
  }

  // ============================================================================
  // LEAGUE METHODS
  // ============================================================================

  async getLeagues(params?: any): Promise<PaginatedResponse<League>> {
    const response = await this.instance.get<PaginatedResponse<League>>(
      endpoints.leagues.list,
      { params }
    );
    return response.data;
  }

  async getLeague(id: number): Promise<League> {
    const response = await this.instance.get<League>(
      endpoints.leagues.byId(id)
    );
    return response.data;
  }

  async createLeague(leagueData: any): Promise<League> {
    const response = await this.instance.post<League>(
      endpoints.leagues.create,
      leagueData
    );
    return response.data;
  }

  async updateLeague(id: number, leagueData: any): Promise<League> {
    const response = await this.instance.put<League>(
      endpoints.leagues.update(id),
      leagueData
    );
    return response.data;
  }

  async deleteLeague(id: number): Promise<void> {
    await this.instance.delete(endpoints.leagues.delete(id));
  }

  // ============================================================================
  // TEAM METHODS
  // ============================================================================

  async getTeams(params?: any): Promise<PaginatedResponse<Team>> {
    const response = await this.instance.get<PaginatedResponse<Team>>(
      endpoints.teams.list,
      { params }
    );
    return response.data;
  }

  async getTeam(id: number): Promise<Team> {
    const response = await this.instance.get<Team>(
      endpoints.teams.byId(id)
    );
    return response.data;
  }

  async createTeam(teamData: any): Promise<Team> {
    const response = await this.instance.post<Team>(
      endpoints.teams.create,
      teamData
    );
    return response.data;
  }

  async updateTeam(id: number, teamData: any): Promise<Team> {
    const response = await this.instance.put<Team>(
      endpoints.teams.update(id),
      teamData
    );
    return response.data;
  }

  async deleteTeam(id: number): Promise<void> {
    await this.instance.delete(endpoints.teams.delete(id));
  }

  // ============================================================================
  // FIXTURE METHODS
  // ============================================================================

  async getFixtures(params?: any): Promise<PaginatedResponse<Fixture>> {
    const response = await this.instance.get<PaginatedResponse<Fixture>>(
      endpoints.fixtures.list,
      { params }
    );
    return response.data;
  }

  async getFixture(id: number): Promise<Fixture> {
    const response = await this.instance.get<Fixture>(
      endpoints.fixtures.byId(id)
    );
    return response.data;
  }

  async getLiveFixtures(): Promise<Fixture[]> {
    const response = await this.instance.get<Fixture[]>(
      endpoints.fixtures.live
    );
    return response.data;
  }

  async getTodayFixtures(): Promise<Fixture[]> {
    const response = await this.instance.get<Fixture[]>(
      endpoints.fixtures.today
    );
    return response.data;
  }

  // ============================================================================
  // SYNC METHODS
  // ============================================================================

  async syncLeagues(): Promise<void> {
    await this.instance.post(endpoints.sync.leagues);
  }

  async syncTeams(): Promise<void> {
    await this.instance.post(endpoints.sync.teams);
  }

  async syncFixtures(): Promise<void> {
    await this.instance.post(endpoints.sync.fixtures);
  }

  async getSyncStatus(): Promise<SyncStatus> {
    const response = await this.instance.get<SyncStatus>(
      endpoints.sync.status
    );
    return response.data;
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
export default apiClient;
