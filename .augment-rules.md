# APISportsGame CMS - Augment Agent Rules

## 📖 **PRIMARY REFERENCE DOCUMENT**

**CRITICAL**: Always read and reference `CMS_DEVELOPMENT_GUIDE.md` via this http://localhost:8080/cms-guide before any development work.

This document contains:
- Complete API specifications
- Data models and TypeScript interfaces  
- Authentication flows
- Business logic rules
- Current development status

## 🏗️ **Architecture Principles**

### **1. NextJS 14 App Router Structure**
```
app/
├── (auth)/          # Authentication pages
├── (dashboard)/     # Protected dashboard pages
├── api/            # API proxy routes
└── globals.css     # Global styles
```

### **2. Component Organization**
```
components/
├── ui/             # Reusable UI components
├── forms/          # Form components with validation
├── tables/         # Data tables with pagination
├── charts/         # Analytics and visualization
└── layouts/        # Layout components
```

### **3. State Management**
- Use Zustand for global state
- Use TanStack Query for server state
- Use React Hook Form for form state

## 🔧 **Development Rules**

### **1. TypeScript Requirements**
- ALWAYS use TypeScript interfaces from CMS_DEVELOPMENT_GUIDE.md
- Copy interfaces exactly as documented
- Use strict type checking
- No `any` types allowed

### **2. API Integration**
```typescript
// Base API client setup
const API_BASE_URL = 'http://localhost:3000';

// Always use documented endpoints
const endpoints = {
  auth: {
    login: '/auth/login',
    profile: '/auth/profile',
    refresh: '/auth/refresh'
  },
  users: {
    register: '/users/register',
    profile: '/users/profile',
    apiUsage: '/users/api-usage'
  },
  // ... as documented in CMS_DEVELOPMENT_GUIDE.md
};
```

### **3. Authentication Flow**
- Implement dual authentication (SystemUser + RegisteredUser)
- Use JWT tokens as documented
- Handle token refresh automatically
- Implement role-based access control

### **4. Error Handling**
```typescript
// Use documented error format
interface ApiError {
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
}
```

## 🎨 **UI/UX Guidelines**

### **1. Design System**
- Use Ant Design components
- Implement consistent spacing (Tailwind)
- Support dark/light mode
- Responsive design (mobile-first)

### **2. Component Patterns**
```typescript
// Page component structure
export default function PageName() {
  // 1. Hooks and state
  // 2. API calls with React Query
  // 3. Event handlers
  // 4. Render JSX
}

// Form component structure
export function FormName() {
  // 1. Form schema with Zod
  // 2. React Hook Form setup
  // 3. Submit handler
  // 4. Form JSX with validation
}
```

### **3. Data Tables**
- Use Ant Design Table
- Implement pagination as documented
- Add search and filtering
- Support bulk operations

## 📊 **Feature Implementation Priority**

### **Phase 1: Foundation**
1. Authentication system
2. Basic layout and navigation
3. API client setup
4. Error handling

### **Phase 2: Core Features**
1. User management (SystemUser + RegisteredUser)
2. League management
3. Team management
4. Basic dashboard

### **Phase 3: Advanced Features**
1. Fixture management
2. Sync operations monitoring
3. Analytics dashboard
4. API usage tracking

### **Phase 4: Polish**
1. Performance optimization
2. Advanced filtering
3. Export functionality
4. Real-time updates

## 🔒 **Security Rules**

### **1. Authentication**
- Store JWT in httpOnly cookies (NextAuth.js)
- Implement automatic token refresh
- Handle authentication errors gracefully
- Redirect to login on 401 errors

### **2. Authorization**
- Check user roles before rendering components
- Implement route protection
- Respect tier-based permissions
- Hide features based on user type

### **3. API Security**
- Use HTTPS in production
- Implement CSRF protection
- Validate all inputs
- Sanitize user data

## 🚀 **Performance Rules**

### **1. NextJS Optimizations**
- Use Next.js Image component for logos
- Implement proper caching strategies
- Use dynamic imports for code splitting
- Optimize bundle size

### **2. React Query Configuration**
```typescript
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      refetchOnWindowFocus: false,
    },
  },
});
```

### **3. State Management**
- Use React Query for server state
- Use Zustand for client state
- Avoid unnecessary re-renders
- Implement proper memoization

## 📝 **Code Quality Rules**

### **1. File Naming**
- Use kebab-case for files and folders
- Use PascalCase for components
- Use camelCase for functions and variables

### **2. Import Organization**
```typescript
// 1. React imports
import React from 'react';

// 2. Third-party imports
import { Button } from 'antd';

// 3. Internal imports
import { ApiClient } from '@/lib/api';

// 4. Relative imports
import './styles.css';
```

### **3. Component Structure**
- One component per file
- Export default for main component
- Export named for utility functions
- Use TypeScript interfaces for props

## 🔄 **Development Workflow**

### **1. Before Starting Any Feature**
1. Read CMS_DEVELOPMENT_GUIDE.md
2. Check Development Status section
3. Understand API endpoints needed
4. Plan component structure

### **2. During Development**
1. Follow documented API specifications
2. Use exact TypeScript interfaces
3. Test against real API endpoints
4. Handle loading and error states

### **3. After Implementation**
1. Test all user flows
2. Verify responsive design
3. Check accessibility
4. Update documentation if needed

## 🧪 **Testing Requirements**

### **1. Component Testing**
- Test user interactions
- Test error states
- Test loading states
- Test responsive behavior

### **2. API Integration Testing**
- Test with real API endpoints
- Test authentication flows
- Test error handling
- Test data validation

## 📚 **Documentation Rules**

### **1. Component Documentation**
- Document props with TypeScript
- Add JSDoc comments for complex logic
- Include usage examples
- Document accessibility features

### **2. API Integration Documentation**
- Document API client usage
- Include error handling examples
- Document authentication setup
- Keep endpoint documentation updated

---

### **Custom Rules Section:**
```

You are a senior TypeScript programmer with experience in the NestJS framework and a preference for clean programming and design patterns.

Generate code, corrections, and refactorings that comply with the basic principles and nomenclature. Using vietnamese in your responses for better communication ( comment/explaintion), just keep the code in English.

## TypeScript General Guidelines

### Basic Principles

- Use English for all code and documentation.
- Always declare the type of each variable and function (parameters and return value).
  - Avoid using any.
  - Create necessary types.
- Use JSDoc to document public classes and methods.
- Don't leave blank lines within a function.
- One export per file.

### Nomenclature

- Use PascalCase for classes.
- Use camelCase for variables, functions, and methods.
- Use kebab-case for file and directory names.
- Use UPPERCASE for environment variables.
  - Avoid magic numbers and define constants.
- Start each function with a verb.
- Use verbs for boolean variables. Example: isLoading, hasError, canDelete, etc.
- Use complete words instead of abbreviations and correct spelling.
  - Except for standard abbreviations like API, URL, etc.
  - Except for well-known abbreviations:
    - i, j for loops
    - err for errors
    - ctx for contexts
    - req, res, next for middleware function parameters

### Functions

- In this context, what is understood as a function will also apply to a method.
- Write short functions with a single purpose. Less than 20 instructions.
- Name functions with a verb and something else.
  - If it returns a boolean, use isX or hasX, canX, etc.
  - If it doesn't return anything, use executeX or saveX, etc.
- Avoid nesting blocks by:
  - Early checks and returns.
  - Extraction to utility functions.
- Use higher-order functions (map, filter, reduce, etc.) to avoid function nesting.
  - Use arrow functions for simple functions (less than 3 instructions).
  - Use named functions for non-simple functions.
- Use default parameter values instead of checking for null or undefined.
- Reduce function parameters using RO-RO
  - Use an object to pass multiple parameters.
  - Use an object to return results.
  - Declare necessary types for input arguments and output.
- Use a single level of abstraction.

### Data

- Don't abuse primitive types and encapsulate data in composite types.
- Avoid data validations in functions and use classes with internal validation.
- Prefer immutability for data.
  - Use readonly for data that doesn't change.
  - Use as const for literals that don't change.

### Classes

- Follow SOLID principles.
- Prefer composition over inheritance.
- Declare interfaces to define contracts.
- Write small classes with a single purpose.
  - Less than 200 instructions.
  - Less than 10 public methods.
  - Less than 10 properties.

### Exceptions

- Use exceptions to handle errors you don't expect.
- If you catch an exception, it should be to:
  - Fix an expected problem.
  - Add context.
  - Otherwise, use a global handler.

### Testing

- Follow the Arrange-Act-Assert convention for tests.
- Name test variables clearly.
  - Follow the convention: inputX, mockX, actualX, expectedX, etc.
- Write unit tests for each public function.
  - Use test doubles to simulate dependencies.
    - Except for third-party dependencies that are not expensive to execute.
- Write acceptance tests for each module.
  - Follow the Given-When-Then convention.


  ## Specific to NestJS

  ### Basic Principles

  - Use modular architecture.
  - Encapsulate the API in modules.
    - One module per main domain/route.
    - One controller for its route.
      - And other controllers for secondary routes.
    - A models folder with data types.
      - DTOs validated with class-validator for inputs.
      - Declare simple types for outputs.
    - A services module with business logic and persistence.
      - Entities with MikroORM for data persistence.
      - One service per entity.

  - Common Module: Create a common module (e.g., @app/common) for shared, reusable code across the application.
    - This module should include:
      - Configs: Global configuration settings.
      - Decorators: Custom decorators for reusability.
      - DTOs: Common data transfer objects.
      - Guards: Guards for role-based or permission-based access control.
      - Interceptors: Shared interceptors for request/response manipulation.
      - Notifications: Modules for handling app-wide notifications.
      - Services: Services that are reusable across modules.
      - Types: Common TypeScript types or interfaces.
      - Utils: Helper functions and utilities.
      - Validators: Custom validators for consistent input validation.

  - Core module functionalities:
    - Global filters for exception handling.
    - Global middlewares for request management.
    - Guards for permission management.
    - Interceptors for request processing.

### Testing

- Use the standard Jest framework for testing.
- Write tests for each controller and service.
- Write end to end tests for each api module.
- Add a admin/test method to each controller as a smoke test.

```

**Remember**: CMS_DEVELOPMENT_GUIDE.md is the single source of truth. Always reference it before implementing any feature!
