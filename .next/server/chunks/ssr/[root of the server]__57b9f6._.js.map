{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/shared/constants/index.ts"], "sourcesContent": ["// APISportsGame CMS - Shared Constants\n// Application-wide constants\n\n// ============================================================================\n// API CONFIGURATION\n// ============================================================================\n\nexport const API_CONFIG = {\n  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',\n  TIMEOUT: 10000,\n  RETRY_ATTEMPTS: 3,\n} as const;\n\n// ============================================================================\n// PAGINATION DEFAULTS\n// ============================================================================\n\nexport const PAGINATION = {\n  DEFAULT_PAGE: 1,\n  DEFAULT_PAGE_SIZE: 10,\n  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],\n  MAX_PAGE_SIZE: 100,\n} as const;\n\n// ============================================================================\n// VALIDATION RULES\n// ============================================================================\n\nexport const VALIDATION = {\n  USERNAME: {\n    MIN_LENGTH: 3,\n    MAX_LENGTH: 50,\n    PATTERN: /^[a-zA-Z0-9_]+$/,\n  },\n  PASSWORD: {\n    MIN_LENGTH: 6,\n    MAX_LENGTH: 100,\n  },\n  EMAIL: {\n    PATTERN: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  },\n  SEARCH: {\n    MIN_LENGTH: 2,\n    MAX_LENGTH: 100,\n  },\n} as const;\n\n// ============================================================================\n// UI CONSTANTS\n// ============================================================================\n\nexport const UI = {\n  DEBOUNCE_DELAY: 300,\n  ANIMATION_DURATION: 200,\n  NOTIFICATION_DURATION: 4000,\n  MODAL_WIDTH: {\n    SMALL: 400,\n    MEDIUM: 600,\n    LARGE: 800,\n    EXTRA_LARGE: 1000,\n  },\n} as const;\n\n// ============================================================================\n// STATUS COLORS\n// ============================================================================\n\nexport const STATUS_COLORS = {\n  SUCCESS: '#52c41a',\n  ERROR: '#ff4d4f',\n  WARNING: '#faad14',\n  INFO: '#1890ff',\n  PROCESSING: '#722ed1',\n  DEFAULT: '#d9d9d9',\n} as const;\n\n// ============================================================================\n// USER ROLES & TIERS\n// ============================================================================\n\nexport const USER_ROLES = {\n  ADMIN: 'admin',\n  EDITOR: 'editor',\n  MODERATOR: 'moderator',\n} as const;\n\nexport const USER_TIERS = {\n  FREE: 'free',\n  PREMIUM: 'premium',\n  ENTERPRISE: 'enterprise',\n} as const;\n\nexport const ROLE_COLORS = {\n  [USER_ROLES.ADMIN]: 'red',\n  [USER_ROLES.EDITOR]: 'blue',\n  [USER_ROLES.MODERATOR]: 'green',\n} as const;\n\nexport const TIER_COLORS = {\n  [USER_TIERS.FREE]: 'default',\n  [USER_TIERS.PREMIUM]: 'gold',\n  [USER_TIERS.ENTERPRISE]: 'purple',\n} as const;\n\n// ============================================================================\n// API LIMITS\n// ============================================================================\n\nexport const API_LIMITS = {\n  FREE_TIER: 1000,\n  PREMIUM_TIER: 50000,\n  ENTERPRISE_TIER: 200000,\n  UNLIMITED: null,\n} as const;\n\n// ============================================================================\n// DATE FORMATS\n// ============================================================================\n\nexport const DATE_FORMATS = {\n  DISPLAY: 'MMM DD, YYYY',\n  DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',\n  ISO: 'YYYY-MM-DD',\n  ISO_WITH_TIME: 'YYYY-MM-DD HH:mm:ss',\n  TIME_ONLY: 'HH:mm',\n  MONTH_YEAR: 'MMM YYYY',\n  FULL: 'dddd, MMMM DD, YYYY',\n} as const;\n\n// ============================================================================\n// ROUTES\n// ============================================================================\n\nexport const ROUTES = {\n  HOME: '/',\n  LOGIN: '/auth/login',\n  DASHBOARD: '/dashboard',\n  USERS: '/dashboard/users',\n  LEAGUES: '/dashboard/leagues',\n  TEAMS: '/dashboard/teams',\n  FIXTURES: '/dashboard/fixtures',\n  ANALYTICS: '/dashboard/analytics',\n  SYNC: '/dashboard/sync',\n  PROFILE: '/dashboard/profile',\n  SETTINGS: '/dashboard/settings',\n} as const;\n\n// ============================================================================\n// LOCAL STORAGE KEYS\n// ============================================================================\n\nexport const STORAGE_KEYS = {\n  ACCESS_TOKEN: 'access_token',\n  REFRESH_TOKEN: 'refresh_token',\n  USER_PREFERENCES: 'user_preferences',\n  THEME: 'theme',\n  LANGUAGE: 'language',\n} as const;\n\n// ============================================================================\n// ERROR MESSAGES\n// ============================================================================\n\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  UNAUTHORIZED: 'You are not authorized to perform this action.',\n  FORBIDDEN: 'Access denied. Insufficient permissions.',\n  NOT_FOUND: 'The requested resource was not found.',\n  VALIDATION_ERROR: 'Please check your input and try again.',\n  SERVER_ERROR: 'Internal server error. Please try again later.',\n  UNKNOWN_ERROR: 'An unknown error occurred.',\n} as const;\n\n// ============================================================================\n// SUCCESS MESSAGES\n// ============================================================================\n\nexport const SUCCESS_MESSAGES = {\n  CREATED: 'Created successfully',\n  UPDATED: 'Updated successfully',\n  DELETED: 'Deleted successfully',\n  SAVED: 'Saved successfully',\n  SYNCED: 'Synced successfully',\n  EXPORTED: 'Exported successfully',\n} as const;\n\n// ============================================================================\n// QUERY KEYS\n// ============================================================================\n\nexport const QUERY_KEYS = {\n  AUTH: ['auth'],\n  USERS: ['users'],\n  LEAGUES: ['leagues'],\n  TEAMS: ['teams'],\n  FIXTURES: ['fixtures'],\n  DASHBOARD: ['dashboard'],\n  SYNC: ['sync'],\n} as const;\n\n// ============================================================================\n// PERMISSIONS\n// ============================================================================\n\nexport const PERMISSIONS = {\n  USERS: {\n    VIEW: 'users:view',\n    CREATE: 'users:create',\n    UPDATE: 'users:update',\n    DELETE: 'users:delete',\n  },\n  LEAGUES: {\n    VIEW: 'leagues:view',\n    CREATE: 'leagues:create',\n    UPDATE: 'leagues:update',\n    DELETE: 'leagues:delete',\n  },\n  TEAMS: {\n    VIEW: 'teams:view',\n    CREATE: 'teams:create',\n    UPDATE: 'teams:update',\n    DELETE: 'teams:delete',\n  },\n  FIXTURES: {\n    VIEW: 'fixtures:view',\n    CREATE: 'fixtures:create',\n    UPDATE: 'fixtures:update',\n    DELETE: 'fixtures:delete',\n  },\n  SYNC: {\n    VIEW: 'sync:view',\n    EXECUTE: 'sync:execute',\n  },\n  ANALYTICS: {\n    VIEW: 'analytics:view',\n  },\n} as const;\n\n// ============================================================================\n// EXPORT TYPES\n// ============================================================================\n\nexport type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];\nexport type UserTier = typeof USER_TIERS[keyof typeof USER_TIERS];\nexport type Route = typeof ROUTES[keyof typeof ROUTES];\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS][keyof typeof PERMISSIONS[keyof typeof PERMISSIONS]];\n\n// ============================================================================\n// DEFAULT EXPORT\n// ============================================================================\n\nexport default {\n  API_CONFIG,\n  PAGINATION,\n  VALIDATION,\n  UI,\n  STATUS_COLORS,\n  USER_ROLES,\n  USER_TIERS,\n  ROLE_COLORS,\n  TIER_COLORS,\n  API_LIMITS,\n  DATE_FORMATS,\n  ROUTES,\n  STORAGE_KEYS,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES,\n  QUERY_KEYS,\n  PERMISSIONS,\n};\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,6BAA6B;AAE7B,+EAA+E;AAC/E,oBAAoB;AACpB,+EAA+E;;;;;;;;;;;;;;;;;;;;;AAExE,MAAM,aAAa;IACxB,UAAU,QAAQ,GAAG,CAAC,mBAAmB,IAAI;IAC7C,SAAS;IACT,gBAAgB;AAClB;AAMO,MAAM,aAAa;IACxB,cAAc;IACd,mBAAmB;IACnB,mBAAmB;QAAC;QAAI;QAAI;QAAI;KAAI;IACpC,eAAe;AACjB;AAMO,MAAM,aAAa;IACxB,UAAU;QACR,YAAY;QACZ,YAAY;QACZ,SAAS;IACX;IACA,UAAU;QACR,YAAY;QACZ,YAAY;IACd;IACA,OAAO;QACL,SAAS;IACX;IACA,QAAQ;QACN,YAAY;QACZ,YAAY;IACd;AACF;AAMO,MAAM,KAAK;IAChB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,aAAa;QACX,OAAO;QACP,QAAQ;QACR,OAAO;QACP,aAAa;IACf;AACF;AAMO,MAAM,gBAAgB;IAC3B,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;IACN,YAAY;IACZ,SAAS;AACX;AAMO,MAAM,aAAa;IACxB,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAEO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,YAAY;AACd;AAEO,MAAM,cAAc;IACzB,CAAC,WAAW,KAAK,CAAC,EAAE;IACpB,CAAC,WAAW,MAAM,CAAC,EAAE;IACrB,CAAC,WAAW,SAAS,CAAC,EAAE;AAC1B;AAEO,MAAM,cAAc;IACzB,CAAC,WAAW,IAAI,CAAC,EAAE;IACnB,CAAC,WAAW,OAAO,CAAC,EAAE;IACtB,CAAC,WAAW,UAAU,CAAC,EAAE;AAC3B;AAMO,MAAM,aAAa;IACxB,WAAW;IACX,cAAc;IACd,iBAAiB;IACjB,WAAW;AACb;AAMO,MAAM,eAAe;IAC1B,SAAS;IACT,mBAAmB;IACnB,KAAK;IACL,eAAe;IACf,WAAW;IACX,YAAY;IACZ,MAAM;AACR;AAMO,MAAM,SAAS;IACpB,MAAM;IACN,OAAO;IACP,WAAW;IACX,OAAO;IACP,SAAS;IACT,OAAO;IACP,UAAU;IACV,WAAW;IACX,MAAM;IACN,SAAS;IACT,UAAU;AACZ;AAMO,MAAM,eAAe;IAC1B,cAAc;IACd,eAAe;IACf,kBAAkB;IAClB,OAAO;IACP,UAAU;AACZ;AAMO,MAAM,iBAAiB;IAC5B,eAAe;IACf,cAAc;IACd,WAAW;IACX,WAAW;IACX,kBAAkB;IAClB,cAAc;IACd,eAAe;AACjB;AAMO,MAAM,mBAAmB;IAC9B,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;IACP,QAAQ;IACR,UAAU;AACZ;AAMO,MAAM,aAAa;IACxB,MAAM;QAAC;KAAO;IACd,OAAO;QAAC;KAAQ;IAChB,SAAS;QAAC;KAAU;IACpB,OAAO;QAAC;KAAQ;IAChB,UAAU;QAAC;KAAW;IACtB,WAAW;QAAC;KAAY;IACxB,MAAM;QAAC;KAAO;AAChB;AAMO,MAAM,cAAc;IACzB,OAAO;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,OAAO;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,UAAU;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,MAAM;QACJ,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;IACR;AACF;uCAee;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF"}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/shared/utils/api.ts"], "sourcesContent": ["// APISportsGame CMS - Shared API Utilities\n// Common API utilities và error handling\n\nimport { AxiosError } from 'axios';\n\n// ============================================================================\n// API ERROR HANDLING\n// ============================================================================\n\nexport interface ApiErrorResponse {\n  statusCode: number;\n  message: string | string[];\n  error: string;\n  timestamp: string;\n  path: string;\n}\n\nexport class ApiError extends Error {\n  public statusCode: number;\n  public response?: ApiErrorResponse;\n\n  constructor(error: AxiosError) {\n    const response = error.response?.data as ApiErrorResponse;\n    const message = Array.isArray(response?.message) \n      ? response.message.join(', ')\n      : response?.message || error.message || 'An error occurred';\n\n    super(message);\n    this.name = 'ApiError';\n    this.statusCode = error.response?.status || 500;\n    this.response = response;\n  }\n}\n\n// ============================================================================\n// API RESPONSE UTILITIES\n// ============================================================================\n\nexport const apiUtils = {\n  /**\n   * Handle API errors consistently\n   */\n  handleError: (error: unknown): ApiError => {\n    if (error instanceof AxiosError) {\n      return new ApiError(error);\n    }\n    \n    if (error instanceof Error) {\n      const apiError = new ApiError({} as AxiosError);\n      apiError.message = error.message;\n      return apiError;\n    }\n\n    const apiError = new ApiError({} as AxiosError);\n    apiError.message = 'Unknown error occurred';\n    return apiError;\n  },\n\n  /**\n   * Extract error message for display\n   */\n  getErrorMessage: (error: unknown): string => {\n    const apiError = apiUtils.handleError(error);\n    return apiError.message;\n  },\n\n  /**\n   * Check if error is specific status code\n   */\n  isErrorStatus: (error: unknown, statusCode: number): boolean => {\n    const apiError = apiUtils.handleError(error);\n    return apiError.statusCode === statusCode;\n  },\n\n  /**\n   * Check if error is unauthorized\n   */\n  isUnauthorized: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 401);\n  },\n\n  /**\n   * Check if error is forbidden\n   */\n  isForbidden: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 403);\n  },\n\n  /**\n   * Check if error is not found\n   */\n  isNotFound: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 404);\n  },\n\n  /**\n   * Check if error is validation error\n   */\n  isValidationError: (error: unknown): boolean => {\n    return apiUtils.isErrorStatus(error, 400);\n  },\n\n  /**\n   * Format pagination params\n   */\n  formatPaginationParams: (page: number, limit: number) => ({\n    page,\n    limit,\n    offset: (page - 1) * limit,\n  }),\n\n  /**\n   * Format filter params (remove undefined values)\n   */\n  formatFilterParams: (filters: Record<string, any>) => {\n    const cleanFilters: Record<string, any> = {};\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        cleanFilters[key] = value;\n      }\n    });\n\n    return cleanFilters;\n  },\n\n  /**\n   * Build query string from params\n   */\n  buildQueryString: (params: Record<string, any>): string => {\n    const cleanParams = apiUtils.formatFilterParams(params);\n    const searchParams = new URLSearchParams();\n\n    Object.entries(cleanParams).forEach(([key, value]) => {\n      if (Array.isArray(value)) {\n        value.forEach(v => searchParams.append(key, String(v)));\n      } else {\n        searchParams.append(key, String(value));\n      }\n    });\n\n    return searchParams.toString();\n  },\n};\n\n// ============================================================================\n// PAGINATION UTILITIES\n// ============================================================================\n\nexport interface PaginationParams {\n  page: number;\n  limit: number;\n}\n\nexport interface PaginationMeta {\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: PaginationMeta;\n}\n\nexport const paginationUtils = {\n  /**\n   * Calculate total pages\n   */\n  calculateTotalPages: (total: number, limit: number): number => {\n    return Math.ceil(total / limit);\n  },\n\n  /**\n   * Calculate offset from page and limit\n   */\n  calculateOffset: (page: number, limit: number): number => {\n    return (page - 1) * limit;\n  },\n\n  /**\n   * Get pagination info for display\n   */\n  getPaginationInfo: (meta: PaginationMeta) => {\n    const start = paginationUtils.calculateOffset(meta.page, meta.limit) + 1;\n    const end = Math.min(start + meta.limit - 1, meta.total);\n    \n    return {\n      start,\n      end,\n      total: meta.total,\n      hasNext: meta.page < meta.totalPages,\n      hasPrev: meta.page > 1,\n    };\n  },\n};\n\n// ============================================================================\n// EXPORTS\n// ============================================================================\n\nexport default apiUtils;\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,yCAAyC;;;;;;;AAEzC;;AAcO,MAAM,iBAAiB;IACrB,WAAmB;IACnB,SAA4B;IAEnC,YAAY,KAAiB,CAAE;QAC7B,MAAM,WAAW,MAAM,QAAQ,EAAE;QACjC,MAAM,UAAU,MAAM,OAAO,CAAC,UAAU,WACpC,SAAS,OAAO,CAAC,IAAI,CAAC,QACtB,UAAU,WAAW,MAAM,OAAO,IAAI;QAE1C,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,UAAU,GAAG,MAAM,QAAQ,EAAE,UAAU;QAC5C,IAAI,CAAC,QAAQ,GAAG;IAClB;AACF;AAMO,MAAM,WAAW;IACtB;;GAEC,GACD,aAAa,CAAC;QACZ,IAAI,iBAAiB,8IAAA,CAAA,aAAU,EAAE;YAC/B,OAAO,IAAI,SAAS;QACtB;QAEA,IAAI,iBAAiB,OAAO;YAC1B,MAAM,WAAW,IAAI,SAAS,CAAC;YAC/B,SAAS,OAAO,GAAG,MAAM,OAAO;YAChC,OAAO;QACT;QAEA,MAAM,WAAW,IAAI,SAAS,CAAC;QAC/B,SAAS,OAAO,GAAG;QACnB,OAAO;IACT;IAEA;;GAEC,GACD,iBAAiB,CAAC;QAChB,MAAM,WAAW,SAAS,WAAW,CAAC;QACtC,OAAO,SAAS,OAAO;IACzB;IAEA;;GAEC,GACD,eAAe,CAAC,OAAgB;QAC9B,MAAM,WAAW,SAAS,WAAW,CAAC;QACtC,OAAO,SAAS,UAAU,KAAK;IACjC;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,mBAAmB,CAAC;QAClB,OAAO,SAAS,aAAa,CAAC,OAAO;IACvC;IAEA;;GAEC,GACD,wBAAwB,CAAC,MAAc,QAAkB,CAAC;YACxD;YACA;YACA,QAAQ,CAAC,OAAO,CAAC,IAAI;QACvB,CAAC;IAED;;GAEC,GACD,oBAAoB,CAAC;QACnB,MAAM,eAAoC,CAAC;QAE3C,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;gBACzD,YAAY,CAAC,IAAI,GAAG;YACtB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,CAAC;QACjB,MAAM,cAAc,SAAS,kBAAkB,CAAC;QAChD,MAAM,eAAe,IAAI;QAEzB,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC/C,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,aAAa,MAAM,CAAC,KAAK,OAAO;YACrD,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,OAAO;YAClC;QACF;QAEA,OAAO,aAAa,QAAQ;IAC9B;AACF;AAuBO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,qBAAqB,CAAC,OAAe;QACnC,OAAO,KAAK,IAAI,CAAC,QAAQ;IAC3B;IAEA;;GAEC,GACD,iBAAiB,CAAC,MAAc;QAC9B,OAAO,CAAC,OAAO,CAAC,IAAI;IACtB;IAEA;;GAEC,GACD,mBAAmB,CAAC;QAClB,MAAM,QAAQ,gBAAgB,eAAe,CAAC,KAAK,IAAI,EAAE,KAAK,KAAK,IAAI;QACvE,MAAM,MAAM,KAAK,GAAG,CAAC,QAAQ,KAAK,KAAK,GAAG,GAAG,KAAK,KAAK;QAEvD,OAAO;YACL;YACA;YACA,OAAO,KAAK,KAAK;YACjB,SAAS,KAAK,IAAI,GAAG,KAAK,UAAU;YACpC,SAAS,KAAK,IAAI,GAAG;QACvB;IACF;AACF;uCAMe"}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/auth/api/index.ts"], "sourcesContent": ["// APISportsGame CMS - Auth API Module\n// Authentication API calls\n\nimport axios, { AxiosInstance } from 'axios';\nimport { API_CONFIG, STORAGE_KEYS } from '@/shared/constants';\nimport { apiUtils } from '@/shared/utils/api';\nimport {\n  LoginRequest,\n  LoginResponse,\n  RegisterRequest,\n  RefreshTokenRequest,\n  RefreshTokenResponse,\n  SystemUser,\n  RegisteredUser,\n} from '../types';\n\n// ============================================================================\n// AUTH API ENDPOINTS\n// ============================================================================\n\nconst AUTH_ENDPOINTS = {\n  login: '/auth/login',\n  profile: '/auth/profile',\n  refresh: '/auth/refresh',\n  logout: '/auth/logout',\n  adminRegister: '/auth/admin/register',\n  userRegister: '/users/register',\n  userLogin: '/users/login',\n  verifyEmail: '/users/verify-email',\n} as const;\n\n// ============================================================================\n// AUTH API CLIENT\n// ============================================================================\n\nclass AuthApiClient {\n  private instance: AxiosInstance;\n\n  constructor() {\n    this.instance = axios.create({\n      baseURL: API_CONFIG.BASE_URL,\n      timeout: API_CONFIG.TIMEOUT,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor - add JWT token\n    this.instance.interceptors.request.use(\n      (config) => {\n        const token = this.getToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => Promise.reject(apiUtils.handleError(error))\n    );\n\n    // Response interceptor - handle errors\n    this.instance.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        // Handle 401 errors - token expired\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n          \n          try {\n            await this.refreshToken();\n            const token = this.getToken();\n            if (token) {\n              originalRequest.headers.Authorization = `Bearer ${token}`;\n              return this.instance(originalRequest);\n            }\n          } catch (refreshError) {\n            this.clearTokens();\n            // Redirect to login will be handled by auth store\n            throw apiUtils.handleError(refreshError);\n          }\n        }\n\n        throw apiUtils.handleError(error);\n      }\n    );\n  }\n\n  // ========================================================================\n  // TOKEN MANAGEMENT\n  // ========================================================================\n\n  private getToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\n    }\n    return null;\n  }\n\n  private setToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);\n    }\n  }\n\n  private getRefreshToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);\n    }\n    return null;\n  }\n\n  private setRefreshToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, token);\n    }\n  }\n\n  private clearTokens(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);\n      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);\n    }\n  }\n\n  // ========================================================================\n  // AUTH API METHODS\n  // ========================================================================\n\n  /**\n   * Login user (both SystemUser and RegisteredUser)\n   */\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    try {\n      const response = await this.instance.post<LoginResponse>(\n        AUTH_ENDPOINTS.login,\n        credentials\n      );\n\n      const { access_token, refresh_token } = response.data;\n      this.setToken(access_token);\n      this.setRefreshToken(refresh_token);\n\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Get current user profile\n   */\n  async getProfile(): Promise<SystemUser | RegisteredUser> {\n    try {\n      const response = await this.instance.get<SystemUser | RegisteredUser>(\n        AUTH_ENDPOINTS.profile\n      );\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Refresh access token\n   */\n  async refreshToken(): Promise<RefreshTokenResponse> {\n    try {\n      const refreshToken = this.getRefreshToken();\n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n\n      const response = await this.instance.post<RefreshTokenResponse>(\n        AUTH_ENDPOINTS.refresh,\n        { refresh_token: refreshToken }\n      );\n\n      const { access_token, refresh_token: newRefreshToken } = response.data;\n      this.setToken(access_token);\n      this.setRefreshToken(newRefreshToken);\n\n      return response.data;\n    } catch (error) {\n      this.clearTokens();\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  async logout(): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.logout);\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.warn('Logout API call failed:', error);\n    } finally {\n      this.clearTokens();\n    }\n  }\n\n  /**\n   * Register new system user (admin only)\n   */\n  async registerSystemUser(userData: RegisterRequest & { role: string }): Promise<SystemUser> {\n    try {\n      const response = await this.instance.post<SystemUser>(\n        AUTH_ENDPOINTS.adminRegister,\n        userData\n      );\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Register new registered user\n   */\n  async registerUser(userData: RegisterRequest): Promise<RegisteredUser> {\n    try {\n      const response = await this.instance.post<RegisteredUser>(\n        AUTH_ENDPOINTS.userRegister,\n        userData\n      );\n      return response.data;\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Verify email for registered user\n   */\n  async verifyEmail(token: string): Promise<void> {\n    try {\n      await this.instance.post(AUTH_ENDPOINTS.verifyEmail, { token });\n    } catch (error) {\n      throw apiUtils.handleError(error);\n    }\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated(): boolean {\n    return !!this.getToken();\n  }\n\n  /**\n   * Get current access token\n   */\n  getCurrentToken(): string | null {\n    return this.getToken();\n  }\n}\n\n// ============================================================================\n// EXPORT SINGLETON INSTANCE\n// ============================================================================\n\nexport const authApi = new AuthApiClient();\nexport default authApi;\n"], "names": [], "mappings": "AAAA,sCAAsC;AACtC,2BAA2B;;;;;AAG3B;AACA;AAFA;;;;AAaA,+EAA+E;AAC/E,qBAAqB;AACrB,+EAA+E;AAE/E,MAAM,iBAAiB;IACrB,OAAO;IACP,SAAS;IACT,SAAS;IACT,QAAQ;IACR,eAAe;IACf,cAAc;IACd,WAAW;IACX,aAAa;AACf;AAEA,+EAA+E;AAC/E,kBAAkB;AAClB,+EAA+E;AAE/E,MAAM;IACI,SAAwB;IAEhC,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC3B,SAAS,mIAAA,CAAA,aAAU,CAAC,QAAQ;YAC5B,SAAS,mIAAA,CAAA,aAAU,CAAC,OAAO;YAC3B,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEQ,oBAAoB;QAC1B,sCAAsC;QACtC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;YACC,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YAClD;YACA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAGjD,uCAAuC;QACvC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,OAAO;YACL,MAAM,kBAAkB,MAAM,MAAM;YAEpC,oCAAoC;YACpC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;gBAC7D,gBAAgB,MAAM,GAAG;gBAEzB,IAAI;oBACF,MAAM,IAAI,CAAC,YAAY;oBACvB,MAAM,QAAQ,IAAI,CAAC,QAAQ;oBAC3B,IAAI,OAAO;wBACT,gBAAgB,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;wBACzD,OAAO,IAAI,CAAC,QAAQ,CAAC;oBACvB;gBACF,EAAE,OAAO,cAAc;oBACrB,IAAI,CAAC,WAAW;oBAChB,kDAAkD;oBAClD,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;gBAC7B;YACF;YAEA,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IAEJ;IAEA,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAEnE,WAA0B;QAChC,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEQ,SAAS,KAAa,EAAQ;QACpC,uCAAmC;;QAEnC;IACF;IAEQ,kBAAiC;QACvC,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEQ,gBAAgB,KAAa,EAAQ;QAC3C,uCAAmC;;QAEnC;IACF;IAEQ,cAAoB;QAC1B,uCAAmC;;QAGnC;IACF;IAEA,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAE3E;;GAEC,GACD,MAAM,MAAM,WAAyB,EAA0B;QAC7D,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,KAAK,EACpB;YAGF,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,SAAS,IAAI;YACrD,IAAI,CAAC,QAAQ,CAAC;YACd,IAAI,CAAC,eAAe,CAAC;YAErB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,aAAmD;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,eAAe,OAAO;YAExB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,eAA8C;QAClD,IAAI;YACF,MAAM,eAAe,IAAI,CAAC,eAAe;YACzC,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,OAAO,EACtB;gBAAE,eAAe;YAAa;YAGhC,MAAM,EAAE,YAAY,EAAE,eAAe,eAAe,EAAE,GAAG,SAAS,IAAI;YACtE,IAAI,CAAC,QAAQ,CAAC;YACd,IAAI,CAAC,eAAe,CAAC;YAErB,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,WAAW;YAChB,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,SAAwB;QAC5B,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,MAAM;QAChD,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,QAAQ,IAAI,CAAC,2BAA2B;QAC1C,SAAU;YACR,IAAI,CAAC,WAAW;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,QAA4C,EAAuB;QAC1F,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,aAAa,EAC5B;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,QAAyB,EAA2B;QACrE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,eAAe,YAAY,EAC3B;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,MAAM,YAAY,KAAa,EAAiB;QAC9C,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,WAAW,EAAE;gBAAE;YAAM;QAC/D,EAAE,OAAO,OAAO;YACd,MAAM,6HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,kBAA2B;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ;IACxB;IAEA;;GAEC,GACD,kBAAiC;QAC/B,OAAO,IAAI,CAAC,QAAQ;IACtB;AACF;AAMO,MAAM,UAAU,IAAI;uCACZ"}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/auth/types/index.ts"], "sourcesContent": ["// APISportsGame CMS - Auth Module Types\n// Authentication related types\n\nimport { BaseEntity } from '@/shared/types/common';\n\n// ============================================================================\n// USER TYPES\n// ============================================================================\n\nexport interface SystemUser extends BaseEntity {\n  username: string;\n  email: string;\n  role: 'admin' | 'editor' | 'moderator';\n  isActive: boolean;\n  lastLoginAt: Date;\n}\n\nexport interface RegisteredUser extends BaseEntity {\n  username: string;\n  email: string;\n  tier: 'free' | 'premium' | 'enterprise';\n  isActive: boolean;\n  isEmailVerified: boolean;\n  apiCallsUsed: number;\n  apiCallsLimit: number | null;\n  subscriptionEndDate: Date | null;\n  lastLoginAt: Date;\n}\n\nexport type User = SystemUser | RegisteredUser;\n\n// ============================================================================\n// ROLE & TIER TYPES\n// ============================================================================\n\nexport type SystemRole = 'admin' | 'editor' | 'moderator';\nexport type RegisteredUserTier = 'free' | 'premium' | 'enterprise';\n\n// ============================================================================\n// JWT TOKEN TYPES\n// ============================================================================\n\nexport interface SystemUserToken {\n  sub: number;\n  username: string;\n  email: string;\n  role: SystemRole;\n  userType: 'system';\n  iat?: number;\n  exp?: number;\n}\n\nexport interface RegisteredUserToken {\n  sub: number;\n  username: string;\n  email: string;\n  tier: RegisteredUserTier;\n  userType: 'registered';\n  isEmailVerified: boolean;\n  iat?: number;\n  exp?: number;\n}\n\nexport type UserToken = SystemUserToken | RegisteredUserToken;\n\n// ============================================================================\n// AUTH REQUEST/RESPONSE TYPES\n// ============================================================================\n\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  access_token: string;\n  refresh_token: string;\n  user: SystemUser | RegisteredUser;\n}\n\nexport interface RegisterRequest {\n  username: string;\n  email: string;\n  password: string;\n}\n\nexport interface RefreshTokenRequest {\n  refresh_token: string;\n}\n\nexport interface RefreshTokenResponse {\n  access_token: string;\n  refresh_token: string;\n}\n\n// ============================================================================\n// AUTH STATE TYPES\n// ============================================================================\n\nexport interface AuthState {\n  user: SystemUser | RegisteredUser | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n}\n\nexport interface AuthActions {\n  login: (credentials: LoginRequest) => Promise<void>;\n  logout: () => void;\n  getProfile: () => Promise<void>;\n  refreshToken: () => Promise<void>;\n  clearError: () => void;\n}\n\n// ============================================================================\n// PERMISSION TYPES\n// ============================================================================\n\nexport interface PermissionState {\n  // User type checks\n  isSystemUser: boolean;\n  isRegisteredUser: boolean;\n\n  // Role checks (SystemUser)\n  isAdmin: boolean;\n  isEditor: boolean;\n  isModerator: boolean;\n\n  // Tier checks (RegisteredUser)\n  isFree: boolean;\n  isPremium: boolean;\n  isEnterprise: boolean;\n\n  // Permission checks\n  canManageUsers: boolean;\n  canManageLeagues: boolean;\n  canManageTeams: boolean;\n  canManageFixtures: boolean;\n  canSync: boolean;\n  canViewAnalytics: boolean;\n\n  // Current user\n  currentUser: SystemUser | RegisteredUser | null;\n}\n\n// ============================================================================\n// AUTH HOOK TYPES\n// ============================================================================\n\nexport interface UseAuthReturn extends AuthState, AuthActions { }\n\nexport interface UsePermissionsReturn extends PermissionState { }\n\n// ============================================================================\n// TYPE GUARDS\n// ============================================================================\n\nexport const isSystemUser = (user: SystemUser | RegisteredUser | null): user is SystemUser => {\n  return user !== null && 'role' in user;\n};\n\nexport const isRegisteredUser = (user: SystemUser | RegisteredUser | null): user is RegisteredUser => {\n  return user !== null && 'tier' in user;\n};\n\nexport const isSystemUserToken = (token: UserToken): token is SystemUserToken => {\n  return token.userType === 'system';\n};\n\nexport const isRegisteredUserToken = (token: UserToken): token is RegisteredUserToken => {\n  return token.userType === 'registered';\n};\n\n// ============================================================================\n// EXPORT ALL\n// ============================================================================\n\n// Remove circular export\n"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,+BAA+B;;;;;;;AA4JxB,MAAM,eAAe,CAAC;IAC3B,OAAO,SAAS,QAAQ,UAAU;AACpC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,SAAS,QAAQ,UAAU;AACpC;AAEO,MAAM,oBAAoB,CAAC;IAChC,OAAO,MAAM,QAAQ,KAAK;AAC5B;AAEO,MAAM,wBAAwB,CAAC;IACpC,OAAO,MAAM,QAAQ,KAAK;AAC5B,GAEA,+EAA+E;CAC/E,aAAa;CACb,+EAA+E;CAE/E,yBAAyB"}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts"], "sourcesContent": ["// APISportsGame CMS - Authentication Store\n// Zustand store cho dual authentication system\n\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { authApi } from '@/modules/auth/api';\nimport {\n  SystemUser,\n  RegisteredUser,\n  LoginRequest,\n  AuthState,\n  AuthActions,\n  UseAuthReturn,\n  UsePermissionsReturn,\n  isSystemUser,\n  isRegisteredUser,\n} from '@/modules/auth/types';\n\n// ============================================================================\n// AUTH STORE TYPES\n// ============================================================================\n\ninterface AuthStoreState extends AuthState, AuthActions {\n  // Helpers\n  isSystemUser: () => boolean;\n  isRegisteredUser: () => boolean;\n  hasRole: (role: string) => boolean;\n  hasTier: (tier: string) => boolean;\n}\n\n// ============================================================================\n// AUTH STORE IMPLEMENTATION\n// ============================================================================\n\nexport const useAuthStore = create<AuthStoreState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // ========================================================================\n      // LOGIN ACTION\n      // ========================================================================\n      login: async (credentials: LoginRequest) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const response = await authApi.login(credentials);\n\n          set({\n            user: response.user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Đăng nhập thất bại';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // LOGOUT ACTION\n      // ========================================================================\n      logout: () => {\n        try {\n          authApi.logout();\n        } catch (error) {\n          console.error('Logout error:', error);\n        } finally {\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n        }\n      },\n\n      // ========================================================================\n      // GET PROFILE ACTION\n      // ========================================================================\n      getProfile: async () => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const user = await authApi.getProfile();\n\n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Không thể lấy thông tin người dùng';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // CLEAR ERROR ACTION\n      // ========================================================================\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // ========================================================================\n      // HELPER METHODS\n      // ========================================================================\n      isSystemUser: () => {\n        const { user } = get();\n        return isSystemUser(user);\n      },\n\n      isRegisteredUser: () => {\n        const { user } = get();\n        return isRegisteredUser(user);\n      },\n\n      hasRole: (role: string) => {\n        const { user, isSystemUser } = get();\n        if (!isSystemUser() || !user) return false;\n\n        const systemUser = user as SystemUser;\n        return systemUser.role === role;\n      },\n\n      hasTier: (tier: string) => {\n        const { user, isRegisteredUser } = get();\n        if (!isRegisteredUser() || !user) return false;\n\n        const registeredUser = user as RegisteredUser;\n        return registeredUser.tier === tier;\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// ============================================================================\n// AUTH HOOKS\n// ============================================================================\n\n// Hook để check permissions\nexport const usePermissions = () => {\n  const { user, isSystemUser, isRegisteredUser, hasRole, hasTier } = useAuthStore();\n\n  return {\n    // User type checks\n    isSystemUser: isSystemUser(),\n    isRegisteredUser: isRegisteredUser(),\n\n    // Role checks (SystemUser)\n    isAdmin: hasRole('admin'),\n    isEditor: hasRole('editor'),\n    isModerator: hasRole('moderator'),\n\n    // Tier checks (RegisteredUser)\n    isFree: hasTier('free'),\n    isPremium: hasTier('premium'),\n    isEnterprise: hasTier('enterprise'),\n\n    // Permission checks\n    canManageUsers: hasRole('admin'),\n    canManageLeagues: hasRole('admin') || hasRole('editor'),\n    canManageTeams: hasRole('admin') || hasRole('editor'),\n    canManageFixtures: hasRole('admin') || hasRole('editor') || hasRole('moderator'),\n    canSync: hasRole('admin') || hasRole('editor'),\n    canViewAnalytics: hasRole('admin') || hasRole('editor'),\n\n    // Current user\n    currentUser: user,\n  };\n};\n\n// Hook để check authentication status\nexport const useAuth = () => {\n  const {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    getProfile,\n    clearError\n  } = useAuthStore();\n\n  return {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    getProfile,\n    clearError,\n  };\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,+CAA+C;;;;;;AAI/C;AACA;AAHA;AACA;;;;;AA8BO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,2EAA2E;QAC3E,eAAe;QACf,2EAA2E;QAC3E,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,sIAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAErC,IAAI;oBACF,MAAM,SAAS,IAAI;oBACnB,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,gBAAgB;QAChB,2EAA2E;QAC3E,QAAQ;YACN,IAAI;gBACF,sIAAA,CAAA,UAAO,CAAC,MAAM;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC,SAAU;gBACR,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,OAAO,MAAM,sIAAA,CAAA,UAAO,CAAC,UAAU;gBAErC,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,2EAA2E;QAC3E,iBAAiB;QACjB,2EAA2E;QAC3E,cAAc;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD,EAAE;QACtB;QAEA,kBAAkB;YAChB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE;QAC1B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG;YAC/B,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO;YAErC,MAAM,aAAa;YACnB,OAAO,WAAW,IAAI,KAAK;QAC7B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG;YACnC,IAAI,CAAC,sBAAsB,CAAC,MAAM,OAAO;YAEzC,MAAM,iBAAiB;YACvB,OAAO,eAAe,IAAI,KAAK;QACjC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AASG,MAAM,iBAAiB;IAC5B,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnE,OAAO;QACL,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAElB,2BAA2B;QAC3B,SAAS,QAAQ;QACjB,UAAU,QAAQ;QAClB,aAAa,QAAQ;QAErB,+BAA+B;QAC/B,QAAQ,QAAQ;QAChB,WAAW,QAAQ;QACnB,cAAc,QAAQ;QAEtB,oBAAoB;QACpB,gBAAgB,QAAQ;QACxB,kBAAkB,QAAQ,YAAY,QAAQ;QAC9C,gBAAgB,QAAQ,YAAY,QAAQ;QAC5C,mBAAmB,QAAQ,YAAY,QAAQ,aAAa,QAAQ;QACpE,SAAS,QAAQ,YAAY,QAAQ;QACrC,kBAAkB,QAAQ,YAAY,QAAQ;QAE9C,eAAe;QACf,aAAa;IACf;AACF;AAGO,MAAM,UAAU;IACrB,MAAM,EACJ,IAAI,EACJ,eAAe,EACf,SAAS,EACT,KAAK,EACL,KAAK,EACL,MAAM,EACN,UAAU,EACV,UAAU,EACX,GAAG;IAEJ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF"}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layouts/dashboard-layout.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Dashboard Layout\n// Main layout cho dashboard với navigation và authentication\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport {\n  Layout,\n  Menu,\n  Avatar,\n  Dropdown,\n  Typography,\n  Space,\n  Button,\n  Badge,\n  Spin,\n  Alert\n} from 'antd';\nimport {\n  DashboardOutlined,\n  UserOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  SyncOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n  BellOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  ApiOutlined\n} from '@ant-design/icons';\nimport { useAuth, usePermissions } from '@/stores/auth-store';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title, Text } = Typography;\n\n// ============================================================================\n// DASHBOARD LAYOUT COMPONENT\n// ============================================================================\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, isAuthenticated, isLoading, logout, getProfile } = useAuth();\n  const permissions = usePermissions();\n\n  const [collapsed, setCollapsed] = useState(false);\n  const [mounted, setMounted] = useState(false);\n\n  // ========================================================================\n  // EFFECTS\n  // ========================================================================\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  // Check authentication và load profile\n  useEffect(() => {\n    if (!isAuthenticated && !isLoading) {\n      router.push('/auth/login');\n      return;\n    }\n\n    if (isAuthenticated && !user) {\n      getProfile().catch(() => {\n        router.push('/auth/login');\n      });\n    }\n  }, [isAuthenticated, isLoading, user, router, getProfile]);\n\n  // ========================================================================\n  // MENU CONFIGURATION\n  // ========================================================================\n\n  const getMenuItems = () => {\n    const items = [\n      {\n        key: '/dashboard',\n        icon: <DashboardOutlined />,\n        label: 'Dashboard',\n      }\n    ];\n\n    // User Management - chỉ admin\n    if (permissions.canManageUsers) {\n      items.push({\n        key: '/dashboard/users',\n        icon: <UserOutlined />,\n        label: 'User Management',\n      });\n    }\n\n    // Sports Data Management\n    if (permissions.canManageLeagues) {\n      items.push({\n        key: '/dashboard/leagues',\n        icon: <TrophyOutlined />,\n        label: 'Quản lý Leagues',\n      });\n    }\n\n    if (permissions.canManageTeams) {\n      items.push({\n        key: '/dashboard/teams',\n        icon: <TeamOutlined />,\n        label: 'Quản lý Teams',\n      });\n    }\n\n    if (permissions.canManageFixtures) {\n      items.push({\n        key: '/dashboard/fixtures',\n        icon: <CalendarOutlined />,\n        label: 'Quản lý Fixtures',\n      });\n    }\n\n    // Analytics\n    if (permissions.canViewAnalytics) {\n      items.push({\n        key: '/dashboard/analytics',\n        icon: <BarChartOutlined />,\n        label: 'Analytics',\n      });\n    }\n\n    // Sync Operations\n    if (permissions.canSync) {\n      items.push({\n        key: '/dashboard/sync',\n        icon: <SyncOutlined />,\n        label: 'Sync Operations',\n      });\n    }\n\n    return items;\n  };\n\n  // ========================================================================\n  // USER DROPDOWN MENU\n  // ========================================================================\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: 'Thông tin cá nhân',\n      onClick: () => router.push('/dashboard/profile'),\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: 'Cài đặt',\n      onClick: () => router.push('/dashboard/settings'),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: 'Đăng xuất',\n      onClick: () => {\n        logout();\n        router.push('/auth/login');\n      },\n    },\n  ];\n\n  // ========================================================================\n  // HANDLERS\n  // ========================================================================\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    router.push(key);\n  };\n\n  const toggleCollapsed = () => {\n    setCollapsed(!collapsed);\n  };\n\n  // ========================================================================\n  // LOADING STATE\n  // ========================================================================\n\n  if (!mounted || isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Spin size=\"large\" tip=\"Đang tải...\" />\n      </div>\n    );\n  }\n\n  // ========================================================================\n  // UNAUTHENTICATED STATE\n  // ========================================================================\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Alert\n          message=\"Chưa đăng nhập\"\n          description=\"Vui lòng đăng nhập để tiếp tục\"\n          type=\"warning\"\n          showIcon\n        />\n      </div>\n    );\n  }\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <Layout className=\"min-h-screen\">\n      {/* Sidebar */}\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={collapsed}\n        theme=\"dark\"\n        width={256}\n        className=\"shadow-lg\"\n      >\n        {/* Logo */}\n        <div className=\"h-16 flex items-center justify-center border-b border-gray-700\">\n          <Space>\n            <ApiOutlined className=\"text-white text-xl\" />\n            {!collapsed && (\n              <Title level={4} className=\"text-white m-0\">\n                CMS\n              </Title>\n            )}\n          </Space>\n        </div>\n\n        {/* Navigation Menu */}\n        <Menu\n          theme=\"dark\"\n          mode=\"inline\"\n          selectedKeys={[pathname]}\n          items={getMenuItems()}\n          onClick={handleMenuClick}\n          className=\"border-r-0\"\n        />\n      </Sider>\n\n      {/* Main Layout */}\n      <Layout>\n        {/* Header */}\n        <Header className=\"bg-white shadow-sm px-4 flex items-center justify-between\">\n          {/* Left side */}\n          <Space>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={toggleCollapsed}\n              className=\"text-lg\"\n            />\n            <Title level={4} className=\"m-0\">\n              APISportsGame CMS\n            </Title>\n          </Space>\n\n          {/* Right side */}\n          <Space size=\"middle\">\n            {/* Notifications */}\n            <Badge count={0} showZero={false}>\n              <Button\n                type=\"text\"\n                icon={<BellOutlined />}\n                className=\"text-lg\"\n              />\n            </Badge>\n\n            {/* User Info */}\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space className=\"cursor-pointer hover:bg-gray-50 px-2 py-1 rounded\">\n                <Avatar\n                  size=\"small\"\n                  icon={<UserOutlined />}\n                  className=\"bg-blue-500\"\n                />\n                <div className=\"text-left\">\n                  <div className=\"text-sm font-medium\">\n                    {user?.username}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    {permissions.isSystemUser && `${(user as any)?.role}`}\n                    {permissions.isRegisteredUser && `${(user as any)?.tier}`}\n                  </div>\n                </div>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n\n        {/* Content */}\n        <Content className=\"p-6 bg-gray-50 overflow-auto\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n}\n\nexport default DashboardLayout;\n"], "names": [], "mappings": ";;;;;AAEA,uCAAuC;AACvC,6DAA6D;AAE7D;AACA;AA4BA;AA3BA;AAAA;AAYA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;AAYA;AAZA;AAAA;AAYA;AAAA;AAZA;AAYA;AAZA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAoCA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAU3B,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACvE,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,2EAA2E;IAC3E,UAAU;IACV,2EAA2E;IAE3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,WAAW;YAClC,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,mBAAmB,CAAC,MAAM;YAC5B,aAAa,KAAK,CAAC;gBACjB,OAAO,IAAI,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAiB;QAAW;QAAM;QAAQ;KAAW;IAEzD,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,eAAe;QACnB,MAAM,QAAQ;YACZ;gBACE,KAAK;gBACL,oBAAM,8OAAC,4NAAA,CAAA,oBAAiB;;;;;gBACxB,OAAO;YACT;SACD;QAED,8BAA8B;QAC9B,IAAI,YAAY,cAAc,EAAE;YAC9B,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,yBAAyB;QACzB,IAAI,YAAY,gBAAgB,EAAE;YAChC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;gBACrB,OAAO;YACT;QACF;QAEA,IAAI,YAAY,cAAc,EAAE;YAC9B,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,IAAI,YAAY,iBAAiB,EAAE;YACjC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gBACvB,OAAO;YACT;QACF;QAEA,YAAY;QACZ,IAAI,YAAY,gBAAgB,EAAE;YAChC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gBACvB,OAAO;YACT;QACF;QAEA,kBAAkB;QAClB,IAAI,YAAY,OAAO,EAAE;YACvB,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,SAAS;gBACP;gBACA,OAAO,IAAI,CAAC;YACd;QACF;KACD;IAED,2EAA2E;IAC3E,WAAW;IACX,2EAA2E;IAE3E,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,aAAa,CAAC;IAChB;IAEA,2EAA2E;IAC3E,gBAAgB;IAChB,2EAA2E;IAE3E,IAAI,CAAC,WAAW,WAAW;QACzB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,8KAAA,CAAA,OAAI;gBAAC,MAAK;gBAAQ,KAAI;;;;;;;;;;;IAG7B;IAEA,2EAA2E;IAC3E,wBAAwB;IACxB,2EAA2E;IAE3E,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;;;;;;;;;;;IAIhB;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,8OAAC,kLAAA,CAAA,SAAM;QAAC,WAAU;;0BAEhB,8OAAC;gBACC,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,OAAM;gBACN,OAAO;gBACP,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gMAAA,CAAA,QAAK;;8CACJ,8OAAC,gNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACtB,CAAC,2BACA,8OAAC;oCAAM,OAAO;oCAAG,WAAU;8CAAiB;;;;;;;;;;;;;;;;;kCAQlD,8OAAC,8KAAA,CAAA,OAAI;wBACH,OAAM;wBACN,MAAK;wBACL,cAAc;4BAAC;yBAAS;wBACxB,OAAO;wBACP,SAAS;wBACT,WAAU;;;;;;;;;;;;0BAKd,8OAAC,kLAAA,CAAA,SAAM;;kCAEL,8OAAC;wBAAO,WAAU;;0CAEhB,8OAAC,gMAAA,CAAA,QAAK;;kDACJ,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,0BAAY,8OAAC,8NAAA,CAAA,qBAAkB;;;;mEAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAEZ,8OAAC;wCAAM,OAAO;wCAAG,WAAU;kDAAM;;;;;;;;;;;;0CAMnC,8OAAC,gMAAA,CAAA,QAAK;gCAAC,MAAK;;kDAEV,8OAAC,gLAAA,CAAA,QAAK;wCAAC,OAAO;wCAAG,UAAU;kDACzB,cAAA,8OAAC,kMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4CACnB,WAAU;;;;;;;;;;;kDAKd,8OAAC,sLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAc;wCAC7B,WAAU;wCACV,KAAK;kDAEL,cAAA,8OAAC,gMAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,kLAAA,CAAA,SAAM;oDACL,MAAK;oDACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oDACnB,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,MAAM;;;;;;sEAET,8OAAC;4DAAI,WAAU;;gEACZ,YAAY,YAAY,IAAI,GAAI,MAAc,MAAM;gEACpD,YAAY,gBAAgB,IAAI,GAAI,MAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrE,8OAAC;wBAAQ,WAAU;kCAChB;;;;;;;;;;;;;;;;;;AAKX;uCAEe"}}, {"offset": {"line": 1389, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1395, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/shared/utils/date.ts"], "sourcesContent": ["// APISportsGame CMS - Shared Date Utilities\n// Centralized date handling với dayjs\n\nimport dayjs from 'dayjs';\nimport relativeTime from 'dayjs/plugin/relativeTime';\nimport utc from 'dayjs/plugin/utc';\nimport timezone from 'dayjs/plugin/timezone';\nimport customParseFormat from 'dayjs/plugin/customParseFormat';\n\n// ============================================================================\n// DAYJS CONFIGURATION\n// ============================================================================\n\n// Extend dayjs với required plugins\ndayjs.extend(relativeTime);\ndayjs.extend(utc);\ndayjs.extend(timezone);\ndayjs.extend(customParseFormat);\n\n// ============================================================================\n// DATE UTILITIES\n// ============================================================================\n\nexport const dateUtils = {\n  /**\n   * Format date to relative time (e.g., \"2 hours ago\")\n   */\n  fromNow: (date: string | Date) => {\n    return dayjs(date).fromNow();\n  },\n\n  /**\n   * Format date to standard format\n   */\n  format: (date: string | Date, format = 'YYYY-MM-DD HH:mm:ss') => {\n    return dayjs(date).format(format);\n  },\n\n  /**\n   * Format date for display\n   */\n  formatDisplay: (date: string | Date) => {\n    return dayjs(date).format('MMM DD, YYYY');\n  },\n\n  /**\n   * Format date with time for display\n   */\n  formatDateTime: (date: string | Date) => {\n    return dayjs(date).format('MMM DD, YYYY HH:mm');\n  },\n\n  /**\n   * Check if date is today\n   */\n  isToday: (date: string | Date) => {\n    return dayjs(date).isSame(dayjs(), 'day');\n  },\n\n  /**\n   * Check if date is yesterday\n   */\n  isYesterday: (date: string | Date) => {\n    return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day');\n  },\n\n  /**\n   * Get start of day\n   */\n  startOfDay: (date?: string | Date) => {\n    return dayjs(date).startOf('day');\n  },\n\n  /**\n   * Get end of day\n   */\n  endOfDay: (date?: string | Date) => {\n    return dayjs(date).endOf('day');\n  },\n\n  /**\n   * Convert to ISO string\n   */\n  toISOString: (date: string | Date) => {\n    return dayjs(date).toISOString();\n  },\n\n  /**\n   * Parse date for form inputs\n   */\n  parseForForm: (date: string | Date | null) => {\n    if (!date) return null;\n    return dayjs(date);\n  },\n\n  /**\n   * Get current timestamp\n   */\n  now: () => {\n    return dayjs();\n  },\n\n  /**\n   * Add time to date\n   */\n  add: (date: string | Date, amount: number, unit: dayjs.ManipulateType) => {\n    return dayjs(date).add(amount, unit);\n  },\n\n  /**\n   * Subtract time from date\n   */\n  subtract: (date: string | Date, amount: number, unit: dayjs.ManipulateType) => {\n    return dayjs(date).subtract(amount, unit);\n  },\n\n  /**\n   * Check if date is before another date\n   */\n  isBefore: (date1: string | Date, date2: string | Date) => {\n    return dayjs(date1).isBefore(dayjs(date2));\n  },\n\n  /**\n   * Check if date is after another date\n   */\n  isAfter: (date1: string | Date, date2: string | Date) => {\n    return dayjs(date1).isAfter(dayjs(date2));\n  },\n\n  /**\n   * Get difference between dates\n   */\n  diff: (date1: string | Date, date2: string | Date, unit?: dayjs.QUnitType) => {\n    return dayjs(date1).diff(dayjs(date2), unit);\n  },\n};\n\n// ============================================================================\n// COMMON DATE FORMATS\n// ============================================================================\n\nexport const DATE_FORMATS = {\n  DISPLAY: 'MMM DD, YYYY',\n  DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',\n  ISO: 'YYYY-MM-DD',\n  ISO_WITH_TIME: 'YYYY-MM-DD HH:mm:ss',\n  TIME_ONLY: 'HH:mm',\n  MONTH_YEAR: 'MMM YYYY',\n  FULL: 'dddd, MMMM DD, YYYY',\n} as const;\n\n// ============================================================================\n// EXPORTS\n// ============================================================================\n\nexport { dayjs };\nexport default dateUtils;\n"], "names": [], "mappings": "AAAA,4CAA4C;AAC5C,sCAAsC;;;;;;AAEtC;AACA;AACA;AACA;AACA;;;;;;AAEA,+EAA+E;AAC/E,sBAAsB;AACtB,+EAA+E;AAE/E,oCAAoC;AACpC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,+IAAA,CAAA,UAAY;AACzB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,sIAAA,CAAA,UAAG;AAChB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,2IAAA,CAAA,UAAQ;AACrB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,oJAAA,CAAA,UAAiB;AAMvB,MAAM,YAAY;IACvB;;GAEC,GACD,SAAS,CAAC;QACR,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,OAAO;IAC5B;IAEA;;GAEC,GACD,QAAQ,CAAC,MAAqB,SAAS,qBAAqB;QAC1D,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;IAC5B;IAEA;;GAEC,GACD,eAAe,CAAC;QACd,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;IAC5B;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;IAC5B;IAEA;;GAEC,GACD,SAAS,CAAC;QACR,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,KAAK;IACrC;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,IAAI,QAAQ,CAAC,GAAG,QAAQ;IACxD;IAEA;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,OAAO,CAAC;IAC7B;IAEA;;GAEC,GACD,UAAU,CAAC;QACT,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,KAAK,CAAC;IAC3B;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,WAAW;IAChC;IAEA;;GAEC,GACD,cAAc,CAAC;QACb,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE;IACf;IAEA;;GAEC,GACD,KAAK;QACH,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD;IACb;IAEA;;GAEC,GACD,KAAK,CAAC,MAAqB,QAAgB;QACzC,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,GAAG,CAAC,QAAQ;IACjC;IAEA;;GAEC,GACD,UAAU,CAAC,MAAqB,QAAgB;QAC9C,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,QAAQ,CAAC,QAAQ;IACtC;IAEA;;GAEC,GACD,UAAU,CAAC,OAAsB;QAC/B,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,OAAO,QAAQ,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE;IACrC;IAEA;;GAEC,GACD,SAAS,CAAC,OAAsB;QAC9B,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,OAAO,OAAO,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE;IACpC;IAEA;;GAEC,GACD,MAAM,CAAC,OAAsB,OAAsB;QACjD,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,OAAO,IAAI,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,QAAQ;IACzC;AACF;AAMO,MAAM,eAAe;IAC1B,SAAS;IACT,mBAAmB;IACnB,KAAK;IACL,eAAe;IACf,WAAW;IACX,YAAY;IACZ,MAAM;AACR;;uCAOe"}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1533, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/users/components/user-table.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - User Table Component\n// Reusable table cho SystemUser và RegisteredUser management\n\nimport React, { useState } from 'react';\nimport {\n  Table,\n  Tag,\n  Space,\n  Button,\n  Dropdown,\n  Avatar,\n  Typography,\n  Tooltip,\n  Modal,\n  message,\n  Input,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Card\n} from 'antd';\nimport {\n  UserOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  MoreOutlined,\n  SearchOutlined,\n  FilterOutlined,\n  PlusOutlined,\n  ExportOutlined,\n  ReloadOutlined\n} from '@ant-design/icons';\nimport { User, UserFilters, UserTableProps } from '../types';\nimport { dateUtils } from '@/shared/utils/date';\nimport { isSystemUser, isRegisteredUser } from '@/modules/auth/types';\n\nconst { Text } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\n\n// UserTableProps is now imported from types\n\n// ============================================================================\n// USER TABLE COMPONENT\n// ============================================================================\n\nexport function UserTable({\n  users,\n  loading = false,\n  userType,\n  onEdit,\n  onDelete,\n  onAdd,\n  onRefresh,\n  onExport,\n  pagination,\n  filters = {},\n  onFiltersChange\n}: UserTableProps) {\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [showFilters, setShowFilters] = useState(false);\n\n  // ========================================================================\n  // HELPER FUNCTIONS\n  // ========================================================================\n\n  // Type guards are now imported from auth types\n\n  // ========================================================================\n  // COLUMN DEFINITIONS\n  // ========================================================================\n\n  const getColumns = () => {\n    const baseColumns = [\n      {\n        title: 'User',\n        dataIndex: 'username',\n        key: 'username',\n        render: (username: string, record: User) => (\n          <Space>\n            <Avatar size=\"small\" icon={<UserOutlined />} />\n            <div>\n              <div className=\"font-medium\">{username}</div>\n              <Text type=\"secondary\" className=\"text-xs\">\n                {record.email}\n              </Text>\n            </div>\n          </Space>\n        ),\n        sorter: true,\n      },\n      {\n        title: userType === 'system' ? 'Role' : 'Tier',\n        dataIndex: userType === 'system' ? 'role' : 'tier',\n        key: userType === 'system' ? 'role' : 'tier',\n        render: (value: string) => {\n          const colors = {\n            // System roles\n            admin: 'red',\n            editor: 'blue',\n            moderator: 'green',\n            // User tiers\n            free: 'default',\n            premium: 'gold',\n            enterprise: 'purple',\n          };\n          return (\n            <Tag color={colors[value as keyof typeof colors] || 'default'}>\n              {value.toUpperCase()}\n            </Tag>\n          );\n        },\n        filters: userType === 'system'\n          ? [\n            { text: 'Admin', value: 'admin' },\n            { text: 'Editor', value: 'editor' },\n            { text: 'Moderator', value: 'moderator' },\n          ]\n          : [\n            { text: 'Free', value: 'free' },\n            { text: 'Premium', value: 'premium' },\n            { text: 'Enterprise', value: 'enterprise' },\n          ],\n      },\n      {\n        title: 'Status',\n        dataIndex: 'isActive',\n        key: 'isActive',\n        render: (isActive: boolean, record: User) => (\n          <Space direction=\"vertical\" size=\"small\">\n            <Tag color={isActive ? 'success' : 'error'}>\n              {isActive ? 'Active' : 'Inactive'}\n            </Tag>\n            {isRegisteredUser(record) && (\n              <Tag color={record.isEmailVerified ? 'success' : 'warning'} className=\"text-xs\">\n                {record.isEmailVerified ? 'Verified' : 'Unverified'}\n              </Tag>\n            )}\n          </Space>\n        ),\n        filters: [\n          { text: 'Active', value: true },\n          { text: 'Inactive', value: false },\n        ],\n      }\n    ];\n\n    // Add API usage column for RegisteredUser\n    if (userType === 'registered') {\n      baseColumns.push({\n        title: 'API Usage',\n        key: 'apiUsage',\n        render: (_, record: User) => {\n          if (!isRegisteredUser(record)) return null;\n          const percentage = record.apiCallsLimit\n            ? (record.apiCallsUsed / record.apiCallsLimit) * 100\n            : 0;\n\n          return (\n            <div>\n              <div className=\"text-sm\">\n                {record.apiCallsUsed.toLocaleString()} / {record.apiCallsLimit?.toLocaleString() || '∞'}\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-1 mt-1\">\n                <div\n                  className={`h-1 rounded-full ${percentage > 90 ? 'bg-red-500' :\n                    percentage > 70 ? 'bg-yellow-500' : 'bg-green-500'\n                    }`}\n                  style={{ width: `${Math.min(percentage, 100)}%` }}\n                />\n              </div>\n            </div>\n          );\n        },\n      });\n    }\n\n    // Add common columns\n    baseColumns.push(\n      {\n        title: 'Last Login',\n        dataIndex: 'lastLoginAt',\n        key: 'lastLoginAt',\n        render: (date: string) => (\n          <Tooltip title={dateUtils.format(date)}>\n            <Text type=\"secondary\" className=\"text-sm\">\n              {dateUtils.fromNow(date)}\n            </Text>\n          </Tooltip>\n        ),\n        sorter: true,\n      },\n      {\n        title: 'Created',\n        dataIndex: 'createdAt',\n        key: 'createdAt',\n        render: (date: string) => (\n          <Text type=\"secondary\" className=\"text-sm\">\n            {dateUtils.formatDisplay(date)}\n          </Text>\n        ),\n        sorter: true,\n      },\n      {\n        title: 'Actions',\n        key: 'actions',\n        width: 120,\n        render: (_, record: User) => (\n          <Space>\n            <Tooltip title=\"Edit\">\n              <Button\n                type=\"text\"\n                size=\"small\"\n                icon={<EditOutlined />}\n                onClick={() => onEdit?.(record)}\n              />\n            </Tooltip>\n            <Dropdown\n              menu={{\n                items: [\n                  {\n                    key: 'edit',\n                    label: 'Edit User',\n                    icon: <EditOutlined />,\n                    onClick: () => onEdit?.(record),\n                  },\n                  {\n                    key: 'delete',\n                    label: 'Delete User',\n                    icon: <DeleteOutlined />,\n                    danger: true,\n                    onClick: () => handleDelete(record),\n                  },\n                ],\n              }}\n              trigger={['click']}\n            >\n              <Button type=\"text\" size=\"small\" icon={<MoreOutlined />} />\n            </Dropdown>\n          </Space>\n        ),\n      }\n    );\n\n    return baseColumns;\n  };\n\n  // ========================================================================\n  // EVENT HANDLERS\n  // ========================================================================\n\n  const handleDelete = (user: User) => {\n    Modal.confirm({\n      title: 'Delete User',\n      content: `Are you sure you want to delete user \"${user.username}\"?`,\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: () => {\n        onDelete?.(user);\n        message.success('User deleted successfully');\n      },\n    });\n  };\n\n  const handleBulkDelete = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('Please select users to delete');\n      return;\n    }\n\n    Modal.confirm({\n      title: 'Delete Selected Users',\n      content: `Are you sure you want to delete ${selectedRowKeys.length} selected users?`,\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: () => {\n        // Handle bulk delete\n        setSelectedRowKeys([]);\n        message.success(`${selectedRowKeys.length} users deleted successfully`);\n      },\n    });\n  };\n\n  const handleFilterChange = (key: keyof UserFilters, value: any) => {\n    const newFilters = { ...filters, [key]: value };\n    onFiltersChange?.(newFilters);\n  };\n\n  // ========================================================================\n  // ROW SELECTION\n  // ========================================================================\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: setSelectedRowKeys,\n    getCheckboxProps: (record: User) => ({\n      disabled: false,\n      name: record.username,\n    }),\n  };\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Header Actions */}\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Search\n                placeholder={`Search ${userType} users...`}\n                allowClear\n                style={{ width: 300 }}\n                value={filters.search}\n                onChange={(e) => handleFilterChange('search', e.target.value)}\n                onSearch={(value) => handleFilterChange('search', value)}\n              />\n              <Button\n                icon={<FilterOutlined />}\n                onClick={() => setShowFilters(!showFilters)}\n              >\n                Filters\n              </Button>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              {selectedRowKeys.length > 0 && (\n                <Button\n                  danger\n                  icon={<DeleteOutlined />}\n                  onClick={handleBulkDelete}\n                >\n                  Delete ({selectedRowKeys.length})\n                </Button>\n              )}\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={onRefresh}\n                loading={loading}\n              >\n                Refresh\n              </Button>\n              <Button\n                icon={<ExportOutlined />}\n                onClick={onExport}\n              >\n                Export\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={onAdd}\n              >\n                Add {userType === 'system' ? 'System User' : 'User'}\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        {/* Advanced Filters */}\n        {showFilters && (\n          <div className=\"mt-4 p-4 bg-gray-50 rounded\">\n            <Row gutter={16}>\n              <Col span={6}>\n                <Select\n                  placeholder={userType === 'system' ? 'Select Role' : 'Select Tier'}\n                  allowClear\n                  style={{ width: '100%' }}\n                  value={userType === 'system' ? filters.role : filters.tier}\n                  onChange={(value) =>\n                    handleFilterChange(userType === 'system' ? 'role' : 'tier', value)\n                  }\n                >\n                  {userType === 'system' ? (\n                    <>\n                      <Option value=\"admin\">Admin</Option>\n                      <Option value=\"editor\">Editor</Option>\n                      <Option value=\"moderator\">Moderator</Option>\n                    </>\n                  ) : (\n                    <>\n                      <Option value=\"free\">Free</Option>\n                      <Option value=\"premium\">Premium</Option>\n                      <Option value=\"enterprise\">Enterprise</Option>\n                    </>\n                  )}\n                </Select>\n              </Col>\n              <Col span={6}>\n                <Select\n                  placeholder=\"Select Status\"\n                  allowClear\n                  style={{ width: '100%' }}\n                  value={filters.isActive}\n                  onChange={(value) => handleFilterChange('isActive', value)}\n                >\n                  <Option value={true}>Active</Option>\n                  <Option value={false}>Inactive</Option>\n                </Select>\n              </Col>\n              <Col span={6}>\n                <DatePicker\n                  placeholder=\"Created From\"\n                  style={{ width: '100%' }}\n                  onChange={(date) => handleFilterChange('createdFrom', date?.toISOString())}\n                />\n              </Col>\n              <Col span={6}>\n                <DatePicker\n                  placeholder=\"Created To\"\n                  style={{ width: '100%' }}\n                  onChange={(date) => handleFilterChange('createdTo', date?.toISOString())}\n                />\n              </Col>\n            </Row>\n          </div>\n        )}\n      </Card>\n\n      {/* Data Table */}\n      <Card>\n        <Table\n          rowSelection={rowSelection}\n          columns={getColumns()}\n          dataSource={users}\n          loading={loading}\n          pagination={pagination ? {\n            current: pagination.current,\n            pageSize: pagination.pageSize,\n            total: pagination.total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `${range[0]}-${range[1]} of ${total} users`,\n            onChange: pagination.onChange,\n          } : false}\n          rowKey=\"id\"\n          scroll={{ x: 1200 }}\n          size=\"middle\"\n        />\n      </Card>\n    </div>\n  );\n}\n\nexport default UserTable;\n"], "names": [], "mappings": ";;;;;AAEA,2CAA2C;AAC3C,6DAA6D;AAE7D;AA+BA;AACA;AA/BA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAlBA;AAAA;AA8BA;AA9BA;AAkBA;AAlBA;AAkBA;AAAA;AAlBA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAAA;AAAA;AAAA;AAlBA;AAAA;AANA;;;;;;;AAuCA,MAAM,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,gLAAA,CAAA,QAAK;AACxB,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AAQlB,SAAS,UAAU,EACxB,KAAK,EACL,UAAU,KAAK,EACf,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACL,SAAS,EACT,QAAQ,EACR,UAAU,EACV,UAAU,CAAC,CAAC,EACZ,eAAe,EACA;IACf,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAE3E,+CAA+C;IAE/C,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,aAAa;QACjB,MAAM,cAAc;YAClB;gBACE,OAAO;gBACP,WAAW;gBACX,KAAK;gBACL,QAAQ,CAAC,UAAkB,uBACzB,8OAAC,gMAAA,CAAA,QAAK;;0CACJ,8OAAC,kLAAA,CAAA,SAAM;gCAAC,MAAK;gCAAQ,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;0CACxC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAe;;;;;;kDAC9B,8OAAC;wCAAK,MAAK;wCAAY,WAAU;kDAC9B,OAAO,KAAK;;;;;;;;;;;;;;;;;;gBAKrB,QAAQ;YACV;YACA;gBACE,OAAO,aAAa,WAAW,SAAS;gBACxC,WAAW,aAAa,WAAW,SAAS;gBAC5C,KAAK,aAAa,WAAW,SAAS;gBACtC,QAAQ,CAAC;oBACP,MAAM,SAAS;wBACb,eAAe;wBACf,OAAO;wBACP,QAAQ;wBACR,WAAW;wBACX,aAAa;wBACb,MAAM;wBACN,SAAS;wBACT,YAAY;oBACd;oBACA,qBACE,8OAAC,4KAAA,CAAA,MAAG;wBAAC,OAAO,MAAM,CAAC,MAA6B,IAAI;kCACjD,MAAM,WAAW;;;;;;gBAGxB;gBACA,SAAS,aAAa,WAClB;oBACA;wBAAE,MAAM;wBAAS,OAAO;oBAAQ;oBAChC;wBAAE,MAAM;wBAAU,OAAO;oBAAS;oBAClC;wBAAE,MAAM;wBAAa,OAAO;oBAAY;iBACzC,GACC;oBACA;wBAAE,MAAM;wBAAQ,OAAO;oBAAO;oBAC9B;wBAAE,MAAM;wBAAW,OAAO;oBAAU;oBACpC;wBAAE,MAAM;wBAAc,OAAO;oBAAa;iBAC3C;YACL;YACA;gBACE,OAAO;gBACP,WAAW;gBACX,KAAK;gBACL,QAAQ,CAAC,UAAmB,uBAC1B,8OAAC,gMAAA,CAAA,QAAK;wBAAC,WAAU;wBAAW,MAAK;;0CAC/B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,OAAO,WAAW,YAAY;0CAChC,WAAW,WAAW;;;;;;4BAExB,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE,yBAChB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,OAAO,OAAO,eAAe,GAAG,YAAY;gCAAW,WAAU;0CACnE,OAAO,eAAe,GAAG,aAAa;;;;;;;;;;;;gBAK/C,SAAS;oBACP;wBAAE,MAAM;wBAAU,OAAO;oBAAK;oBAC9B;wBAAE,MAAM;wBAAY,OAAO;oBAAM;iBAClC;YACH;SACD;QAED,0CAA0C;QAC1C,IAAI,aAAa,cAAc;YAC7B,YAAY,IAAI,CAAC;gBACf,OAAO;gBACP,KAAK;gBACL,QAAQ,CAAC,GAAG;oBACV,IAAI,CAAC,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,OAAO;oBACtC,MAAM,aAAa,OAAO,aAAa,GACnC,AAAC,OAAO,YAAY,GAAG,OAAO,aAAa,GAAI,MAC/C;oBAEJ,qBACE,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,YAAY,CAAC,cAAc;oCAAG;oCAAI,OAAO,aAAa,EAAE,oBAAoB;;;;;;;0CAEtF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAW,CAAC,iBAAiB,EAAE,aAAa,KAAK,eAC/C,aAAa,KAAK,kBAAkB,gBAClC;oCACJ,OAAO;wCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC;oCAAC;;;;;;;;;;;;;;;;;gBAK1D;YACF;QACF;QAEA,qBAAqB;QACrB,YAAY,IAAI,CACd;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,qBACP,8OAAC,oLAAA,CAAA,UAAO;oBAAC,OAAO,8IAAA,CAAA,YAAS,CAAC,MAAM,CAAC;8BAC/B,cAAA,8OAAC;wBAAK,MAAK;wBAAY,WAAU;kCAC9B,8IAAA,CAAA,YAAS,CAAC,OAAO,CAAC;;;;;;;;;;;YAIzB,QAAQ;QACV,GACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,qBACP,8OAAC;oBAAK,MAAK;oBAAY,WAAU;8BAC9B,8IAAA,CAAA,YAAS,CAAC,aAAa,CAAC;;;;;;YAG7B,QAAQ;QACV,GACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,8OAAC,gMAAA,CAAA,QAAK;;sCACJ,8OAAC,oLAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gCACnB,SAAS,IAAM,SAAS;;;;;;;;;;;sCAG5B,8OAAC,sLAAA,CAAA,WAAQ;4BACP,MAAM;gCACJ,OAAO;oCACL;wCACE,KAAK;wCACL,OAAO;wCACP,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,SAAS,IAAM,SAAS;oCAC1B;oCACA;wCACE,KAAK;wCACL,OAAO;wCACP,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,QAAQ;wCACR,SAAS,IAAM,aAAa;oCAC9B;iCACD;4BACH;4BACA,SAAS;gCAAC;6BAAQ;sCAElB,cAAA,8OAAC,kMAAA,CAAA,SAAM;gCAAC,MAAK;gCAAO,MAAK;gCAAQ,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;QAI5D;QAGF,OAAO;IACT;IAEA,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,eAAe,CAAC;QACpB,gLAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,sCAAsC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;YACnE,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,WAAW;gBACX,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,gLAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,gCAAgC,EAAE,gBAAgB,MAAM,CAAC,gBAAgB,CAAC;YACpF,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,qBAAqB;gBACrB,mBAAmB,EAAE;gBACrB,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,gBAAgB,MAAM,CAAC,2BAA2B,CAAC;YACxE;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC,KAAwB;QAClD,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,CAAC,IAAI,EAAE;QAAM;QAC9C,kBAAkB;IACpB;IAEA,2EAA2E;IAC3E,gBAAgB;IAChB,2EAA2E;IAE3E,MAAM,eAAe;QACnB;QACA,UAAU;QACV,kBAAkB,CAAC,SAAiB,CAAC;gBACnC,UAAU;gBACV,MAAM,OAAO,QAAQ;YACvB,CAAC;IACH;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,8KAAA,CAAA,OAAI;;kCACH,8OAAC,4KAAA,CAAA,MAAG;wBAAC,SAAQ;wBAAgB,OAAM;;0CACjC,8OAAC,4KAAA,CAAA,MAAG;0CACF,cAAA,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC;4CACC,aAAa,CAAC,OAAO,EAAE,SAAS,SAAS,CAAC;4CAC1C,UAAU;4CACV,OAAO;gDAAE,OAAO;4CAAI;4CACpB,OAAO,QAAQ,MAAM;4CACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;4CAC5D,UAAU,CAAC,QAAU,mBAAmB,UAAU;;;;;;sDAEpD,8OAAC,kMAAA,CAAA,SAAM;4CACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS,IAAM,eAAe,CAAC;sDAChC;;;;;;;;;;;;;;;;;0CAKL,8OAAC,4KAAA,CAAA,MAAG;0CACF,cAAA,8OAAC,gMAAA,CAAA,QAAK;;wCACH,gBAAgB,MAAM,GAAG,mBACxB,8OAAC,kMAAA,CAAA,SAAM;4CACL,MAAM;4CACN,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS;;gDACV;gDACU,gBAAgB,MAAM;gDAAC;;;;;;;sDAGpC,8OAAC,kMAAA,CAAA,SAAM;4CACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS;4CACT,SAAS;sDACV;;;;;;sDAGD,8OAAC,kMAAA,CAAA,SAAM;4CACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS;sDACV;;;;;;sDAGD,8OAAC,kMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4CACnB,SAAS;;gDACV;gDACM,aAAa,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;oBAOpD,6BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAa,aAAa,WAAW,gBAAgB;wCACrD,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAO;wCACvB,OAAO,aAAa,WAAW,QAAQ,IAAI,GAAG,QAAQ,IAAI;wCAC1D,UAAU,CAAC,QACT,mBAAmB,aAAa,WAAW,SAAS,QAAQ;kDAG7D,aAAa,yBACZ;;8DACE,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;;yEAG5B;;8DACE,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAa;;;;;;;;;;;;;;;;;;8CAKnC,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAO;wCACvB,OAAO,QAAQ,QAAQ;wCACvB,UAAU,CAAC,QAAU,mBAAmB,YAAY;;0DAEpD,8OAAC;gDAAO,OAAO;0DAAM;;;;;;0DACrB,8OAAC;gDAAO,OAAO;0DAAO;;;;;;;;;;;;;;;;;8CAG1B,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,8LAAA,CAAA,aAAU;wCACT,aAAY;wCACZ,OAAO;4CAAE,OAAO;wCAAO;wCACvB,UAAU,CAAC,OAAS,mBAAmB,eAAe,MAAM;;;;;;;;;;;8CAGhE,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,8LAAA,CAAA,aAAU;wCACT,aAAY;wCACZ,OAAO;4CAAE,OAAO;wCAAO;wCACvB,UAAU,CAAC,OAAS,mBAAmB,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStE,8OAAC,8KAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,cAAc;oBACd,SAAS;oBACT,YAAY;oBACZ,SAAS;oBACT,YAAY,aAAa;wBACvB,SAAS,WAAW,OAAO;wBAC3B,UAAU,WAAW,QAAQ;wBAC7B,OAAO,WAAW,KAAK;wBACvB,iBAAiB;wBACjB,iBAAiB;wBACjB,WAAW,CAAC,OAAO,QACjB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC;wBAC7C,UAAU,WAAW,QAAQ;oBAC/B,IAAI;oBACJ,QAAO;oBACP,QAAQ;wBAAE,GAAG;oBAAK;oBAClB,MAAK;;;;;;;;;;;;;;;;;AAKf;uCAEe"}}, {"offset": {"line": 2299, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2305, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/users/components/user-form.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - User Form Component\n// Reusable form cho create/edit SystemUser và RegisteredUser\n\nimport React, { useEffect } from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Switch,\n  Button,\n  Space,\n  Card,\n  Row,\n  Col,\n  Typography,\n  Divider,\n  InputNumber,\n  DatePicker,\n  Alert\n} from 'antd';\nimport {\n  UserOutlined,\n  MailOutlined,\n  LockOutlined,\n  SaveOutlined,\n  CloseOutlined\n} from '@ant-design/icons';\nimport { User, UserFormProps } from '../types';\nimport { dateUtils } from '@/shared/utils/date';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\n// UserFormProps is now imported from types\n\n// ============================================================================\n// USER FORM COMPONENT\n// ============================================================================\n\nexport function UserForm({\n  userType,\n  mode,\n  initialValues,\n  loading = false,\n  onSubmit,\n  onCancel\n}: UserFormProps) {\n  const [form] = Form.useForm();\n\n  // ========================================================================\n  // EFFECTS\n  // ========================================================================\n\n  useEffect(() => {\n    if (initialValues) {\n      // Convert dates for form\n      const formValues = {\n        ...initialValues,\n        subscriptionEndDate: dateUtils.parseForForm(initialValues.subscriptionEndDate),\n      };\n      form.setFieldsValue(formValues);\n    }\n  }, [initialValues, form]);\n\n  // ========================================================================\n  // FORM VALIDATION RULES\n  // ========================================================================\n\n  const validationRules = {\n    username: [\n      { required: true, message: 'Username is required' },\n      { min: 3, message: 'Username must be at least 3 characters' },\n      { max: 50, message: 'Username must not exceed 50 characters' },\n      { pattern: /^[a-zA-Z0-9_]+$/, message: 'Username can only contain letters, numbers, and underscores' }\n    ],\n    email: [\n      { required: true, message: 'Email is required' },\n      { type: 'email' as const, message: 'Please enter a valid email' }\n    ],\n    password: mode === 'create' ? [\n      { required: true, message: 'Password is required' },\n      { min: 6, message: 'Password must be at least 6 characters' },\n      { max: 100, message: 'Password must not exceed 100 characters' }\n    ] : [\n      { min: 6, message: 'Password must be at least 6 characters' },\n      { max: 100, message: 'Password must not exceed 100 characters' }\n    ],\n    role: [\n      { required: true, message: 'Role is required' }\n    ],\n    tier: [\n      { required: true, message: 'Tier is required' }\n    ]\n  };\n\n  // ========================================================================\n  // EVENT HANDLERS\n  // ========================================================================\n\n  const handleSubmit = async (values: any) => {\n    try {\n      // Process form values\n      const processedValues = {\n        ...values,\n        subscriptionEndDate: values.subscriptionEndDate\n          ? dateUtils.toISOString(values.subscriptionEndDate)\n          : null,\n      };\n\n      // Remove password if empty in edit mode\n      if (mode === 'edit' && !processedValues.password) {\n        delete processedValues.password;\n      }\n\n      onSubmit(processedValues);\n    } catch (error) {\n      console.error('Form submission error:', error);\n    }\n  };\n\n  const handleReset = () => {\n    form.resetFields();\n  };\n\n  // ========================================================================\n  // RENDER HELPERS\n  // ========================================================================\n\n  const renderSystemUserFields = () => (\n    <>\n      <Col span={24}>\n        <Form.Item\n          name=\"role\"\n          label=\"Role\"\n          rules={validationRules.role}\n        >\n          <Select placeholder=\"Select user role\">\n            <Option value=\"admin\">\n              <Space>\n                <span>👑</span>\n                <div>\n                  <div>Admin</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Full system access\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n            <Option value=\"editor\">\n              <Space>\n                <span>✏️</span>\n                <div>\n                  <div>Editor</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Content management\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n            <Option value=\"moderator\">\n              <Space>\n                <span>🛡️</span>\n                <div>\n                  <div>Moderator</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Content moderation\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n          </Select>\n        </Form.Item>\n      </Col>\n    </>\n  );\n\n  const renderRegisteredUserFields = () => (\n    <>\n      <Col span={12}>\n        <Form.Item\n          name=\"tier\"\n          label=\"Subscription Tier\"\n          rules={validationRules.tier}\n        >\n          <Select placeholder=\"Select subscription tier\">\n            <Option value=\"free\">\n              <Space>\n                <span>🆓</span>\n                <div>\n                  <div>Free</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Basic features\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n            <Option value=\"premium\">\n              <Space>\n                <span>⭐</span>\n                <div>\n                  <div>Premium</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Enhanced features\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n            <Option value=\"enterprise\">\n              <Space>\n                <span>🏢</span>\n                <div>\n                  <div>Enterprise</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Full features\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n          </Select>\n        </Form.Item>\n      </Col>\n\n      <Col span={12}>\n        <Form.Item\n          name=\"isEmailVerified\"\n          label=\"Email Verified\"\n          valuePropName=\"checked\"\n        >\n          <Switch />\n        </Form.Item>\n      </Col>\n\n      <Col span={12}>\n        <Form.Item\n          name=\"apiCallsUsed\"\n          label=\"API Calls Used\"\n        >\n          <InputNumber\n            min={0}\n            style={{ width: '100%' }}\n            formatter={value => `${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n            parser={value => value!.replace(/\\$\\s?|(,*)/g, '')}\n          />\n        </Form.Item>\n      </Col>\n\n      <Col span={12}>\n        <Form.Item\n          name=\"apiCallsLimit\"\n          label=\"API Calls Limit\"\n        >\n          <InputNumber\n            min={0}\n            style={{ width: '100%' }}\n            formatter={value => `${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n            parser={value => value!.replace(/\\$\\s?|(,*)/g, '')}\n            placeholder=\"Leave empty for unlimited\"\n          />\n        </Form.Item>\n      </Col>\n\n      <Col span={24}>\n        <Form.Item\n          name=\"subscriptionEndDate\"\n          label=\"Subscription End Date\"\n        >\n          <DatePicker\n            style={{ width: '100%' }}\n            showTime\n            format=\"YYYY-MM-DD HH:mm:ss\"\n            placeholder=\"Select subscription end date\"\n          />\n        </Form.Item>\n      </Col>\n    </>\n  );\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <Title level={4}>\n          {mode === 'create' ? 'Create' : 'Edit'} {userType === 'system' ? 'System User' : 'Registered User'}\n        </Title>\n        <Text type=\"secondary\">\n          {mode === 'create'\n            ? `Add a new ${userType} user to the system`\n            : `Update ${userType} user information`\n          }\n        </Text>\n      </div>\n\n      {/* Info Alert */}\n      <Alert\n        message={`${userType === 'system' ? 'System User' : 'Registered User'} Information`}\n        description={\n          userType === 'system'\n            ? 'System users have administrative access to the CMS. Choose roles carefully.'\n            : 'Registered users are API consumers with different subscription tiers and usage limits.'\n        }\n        type=\"info\"\n        showIcon\n        className=\"mb-6\"\n      />\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleSubmit}\n        autoComplete=\"off\"\n        size=\"large\"\n      >\n        <Row gutter={16}>\n          {/* Basic Information */}\n          <Col span={24}>\n            <Divider orientation=\"left\">Basic Information</Divider>\n          </Col>\n\n          <Col span={12}>\n            <Form.Item\n              name=\"username\"\n              label=\"Username\"\n              rules={validationRules.username}\n            >\n              <Input\n                prefix={<UserOutlined />}\n                placeholder=\"Enter username\"\n                disabled={mode === 'edit'}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col span={12}>\n            <Form.Item\n              name=\"email\"\n              label=\"Email\"\n              rules={validationRules.email}\n            >\n              <Input\n                prefix={<MailOutlined />}\n                placeholder=\"Enter email address\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col span={24}>\n            <Form.Item\n              name=\"password\"\n              label={mode === 'create' ? 'Password' : 'New Password (leave empty to keep current)'}\n              rules={validationRules.password}\n            >\n              <Input.Password\n                prefix={<LockOutlined />}\n                placeholder={mode === 'create' ? 'Enter password' : 'Enter new password'}\n              />\n            </Form.Item>\n          </Col>\n\n          {/* Role/Tier Specific Fields */}\n          <Col span={24}>\n            <Divider orientation=\"left\">\n              {userType === 'system' ? 'Role & Permissions' : 'Subscription & Usage'}\n            </Divider>\n          </Col>\n\n          {userType === 'system' ? renderSystemUserFields() : renderRegisteredUserFields()}\n\n          {/* Status */}\n          <Col span={24}>\n            <Divider orientation=\"left\">Status</Divider>\n          </Col>\n\n          <Col span={12}>\n            <Form.Item\n              name=\"isActive\"\n              label=\"Active Status\"\n              valuePropName=\"checked\"\n              initialValue={true}\n            >\n              <Switch />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        {/* Form Actions */}\n        <Divider />\n        <Row justify=\"end\">\n          <Col>\n            <Space>\n              <Button onClick={handleReset}>\n                Reset\n              </Button>\n              <Button onClick={onCancel}>\n                <CloseOutlined />\n                Cancel\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                icon={<SaveOutlined />}\n              >\n                {mode === 'create' ? 'Create User' : 'Update User'}\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Form>\n    </Card>\n  );\n}\n\nexport default UserForm;\n"], "names": [], "mappings": ";;;;;AAEA,0CAA0C;AAC1C,6DAA6D;AAE7D;AAyBA;AAxBA;AAAA;AAAA;AAAA;AAwBA;AAxBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AAAA;AAhBA;AAgBA;AAAA;AAtBA;;;;;;AAgCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,gLAAA,CAAA,QAAK;AAQnB,SAAS,SAAS,EACvB,QAAQ,EACR,IAAI,EACJ,aAAa,EACb,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACM;IACd,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,2EAA2E;IAC3E,UAAU;IACV,2EAA2E;IAE3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe;YACjB,yBAAyB;YACzB,MAAM,aAAa;gBACjB,GAAG,aAAa;gBAChB,qBAAqB,8IAAA,CAAA,YAAS,CAAC,YAAY,CAAC,cAAc,mBAAmB;YAC/E;YACA,KAAK,cAAc,CAAC;QACtB;IACF,GAAG;QAAC;QAAe;KAAK;IAExB,2EAA2E;IAC3E,wBAAwB;IACxB,2EAA2E;IAE3E,MAAM,kBAAkB;QACtB,UAAU;YACR;gBAAE,UAAU;gBAAM,SAAS;YAAuB;YAClD;gBAAE,KAAK;gBAAG,SAAS;YAAyC;YAC5D;gBAAE,KAAK;gBAAI,SAAS;YAAyC;YAC7D;gBAAE,SAAS;gBAAmB,SAAS;YAA8D;SACtG;QACD,OAAO;YACL;gBAAE,UAAU;gBAAM,SAAS;YAAoB;YAC/C;gBAAE,MAAM;gBAAkB,SAAS;YAA6B;SACjE;QACD,UAAU,SAAS,WAAW;YAC5B;gBAAE,UAAU;gBAAM,SAAS;YAAuB;YAClD;gBAAE,KAAK;gBAAG,SAAS;YAAyC;YAC5D;gBAAE,KAAK;gBAAK,SAAS;YAA0C;SAChE,GAAG;YACF;gBAAE,KAAK;gBAAG,SAAS;YAAyC;YAC5D;gBAAE,KAAK;gBAAK,SAAS;YAA0C;SAChE;QACD,MAAM;YACJ;gBAAE,UAAU;gBAAM,SAAS;YAAmB;SAC/C;QACD,MAAM;YACJ;gBAAE,UAAU;gBAAM,SAAS;YAAmB;SAC/C;IACH;IAEA,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,sBAAsB;YACtB,MAAM,kBAAkB;gBACtB,GAAG,MAAM;gBACT,qBAAqB,OAAO,mBAAmB,GAC3C,8IAAA,CAAA,YAAS,CAAC,WAAW,CAAC,OAAO,mBAAmB,IAChD;YACN;YAEA,wCAAwC;YACxC,IAAI,SAAS,UAAU,CAAC,gBAAgB,QAAQ,EAAE;gBAChD,OAAO,gBAAgB,QAAQ;YACjC;YAEA,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,cAAc;QAClB,KAAK,WAAW;IAClB;IAEA,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,yBAAyB,kBAC7B;sBACE,cAAA,8OAAC,4KAAA,CAAA,MAAG;gBAAC,MAAM;0BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAM;oBACN,OAAO,gBAAgB,IAAI;8BAE3B,cAAA,8OAAC,kLAAA,CAAA,SAAM;wBAAC,aAAY;;0CAClB,8OAAC;gCAAO,OAAM;0CACZ,cAAA,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC;sDAAK;;;;;;sDACN,8OAAC;;8DACC,8OAAC;8DAAI;;;;;;8DACL,8OAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMjD,8OAAC;gCAAO,OAAM;0CACZ,cAAA,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC;sDAAK;;;;;;sDACN,8OAAC;;8DACC,8OAAC;8DAAI;;;;;;8DACL,8OAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMjD,8OAAC;gCAAO,OAAM;0CACZ,cAAA,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC;sDAAK;;;;;;sDACN,8OAAC;;8DACC,8OAAC;8DAAI;;;;;;8DACL,8OAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAY3D,MAAM,6BAA6B,kBACjC;;8BACE,8OAAC,4KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;wBACN,OAAO,gBAAgB,IAAI;kCAE3B,cAAA,8OAAC,kLAAA,CAAA,SAAM;4BAAC,aAAY;;8CAClB,8OAAC;oCAAO,OAAM;8CACZ,cAAA,8OAAC,gMAAA,CAAA,QAAK;;0DACJ,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMjD,8OAAC;oCAAO,OAAM;8CACZ,cAAA,8OAAC,gMAAA,CAAA,QAAK;;0DACJ,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMjD,8OAAC;oCAAO,OAAM;8CACZ,cAAA,8OAAC,gMAAA,CAAA,QAAK;;0DACJ,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUvD,8OAAC,4KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;wBACN,eAAc;kCAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;;;;;;;;;;;;;;;8BAIX,8OAAC,4KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;kCAEN,cAAA,8OAAC,gMAAA,CAAA,cAAW;4BACV,KAAK;4BACL,OAAO;gCAAE,OAAO;4BAAO;4BACvB,WAAW,CAAA,QAAS,GAAG,OAAO,CAAC,OAAO,CAAC,yBAAyB;4BAChE,QAAQ,CAAA,QAAS,MAAO,OAAO,CAAC,eAAe;;;;;;;;;;;;;;;;8BAKrD,8OAAC,4KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;kCAEN,cAAA,8OAAC,gMAAA,CAAA,cAAW;4BACV,KAAK;4BACL,OAAO;gCAAE,OAAO;4BAAO;4BACvB,WAAW,CAAA,QAAS,GAAG,OAAO,CAAC,OAAO,CAAC,yBAAyB;4BAChE,QAAQ,CAAA,QAAS,MAAO,OAAO,CAAC,eAAe;4BAC/C,aAAY;;;;;;;;;;;;;;;;8BAKlB,8OAAC,4KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;kCAEN,cAAA,8OAAC,8LAAA,CAAA,aAAU;4BACT,OAAO;gCAAE,OAAO;4BAAO;4BACvB,QAAQ;4BACR,QAAO;4BACP,aAAY;;;;;;;;;;;;;;;;;;IAOtB,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,8OAAC,8KAAA,CAAA,OAAI;;0BACH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;;4BACX,SAAS,WAAW,WAAW;4BAAO;4BAAE,aAAa,WAAW,gBAAgB;;;;;;;kCAEnF,8OAAC;wBAAK,MAAK;kCACR,SAAS,WACN,CAAC,UAAU,EAAE,SAAS,mBAAmB,CAAC,GAC1C,CAAC,OAAO,EAAE,SAAS,iBAAiB,CAAC;;;;;;;;;;;;0BAM7C,8OAAC,gLAAA,CAAA,QAAK;gBACJ,SAAS,GAAG,aAAa,WAAW,gBAAgB,kBAAkB,YAAY,CAAC;gBACnF,aACE,aAAa,WACT,gFACA;gBAEN,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;0BAGZ,8OAAC,8KAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,UAAU;gBACV,cAAa;gBACb,MAAK;;kCAEL,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CAEX,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,oLAAA,CAAA,UAAO;oCAAC,aAAY;8CAAO;;;;;;;;;;;0CAG9B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO,gBAAgB,QAAQ;8CAE/B,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,UAAU,SAAS;;;;;;;;;;;;;;;;0CAKzB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO,gBAAgB,KAAK;8CAE5B,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;;;;;;;;;;;;;;;;0CAKlB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAO,SAAS,WAAW,aAAa;oCACxC,OAAO,gBAAgB,QAAQ;8CAE/B,cAAA,8OAAC,gLAAA,CAAA,QAAK,CAAC,QAAQ;wCACb,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,aAAa,SAAS,WAAW,mBAAmB;;;;;;;;;;;;;;;;0CAM1D,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,oLAAA,CAAA,UAAO;oCAAC,aAAY;8CAClB,aAAa,WAAW,uBAAuB;;;;;;;;;;;4BAInD,aAAa,WAAW,2BAA2B;0CAGpD,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,oLAAA,CAAA,UAAO;oCAAC,aAAY;8CAAO;;;;;;;;;;;0CAG9B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,eAAc;oCACd,cAAc;8CAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;kCAMb,8OAAC,oLAAA,CAAA,UAAO;;;;;kCACR,8OAAC,4KAAA,CAAA,MAAG;wBAAC,SAAQ;kCACX,cAAA,8OAAC,4KAAA,CAAA,MAAG;sCACF,cAAA,8OAAC,gMAAA,CAAA,QAAK;;kDACJ,8OAAC,kMAAA,CAAA,SAAM;wCAAC,SAAS;kDAAa;;;;;;kDAG9B,8OAAC,kMAAA,CAAA,SAAM;wCAAC,SAAS;;0DACf,8OAAC,oNAAA,CAAA,gBAAa;;;;;4CAAG;;;;;;;kDAGnB,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAS;wCACT,SAAS;wCACT,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;kDAElB,SAAS,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;uCAEe"}}, {"offset": {"line": 3181, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3187, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/dashboard/users/page.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - User Management Page\n// Main page cho quản lý SystemUser và RegisteredUser\n\nimport React, { useState } from 'react';\nimport {\n  Tabs,\n  Typography,\n  Space,\n  Card,\n  Statistic,\n  Row,\n  Col,\n  Modal,\n  message,\n  Button\n} from 'antd';\nimport {\n  UserOutlined,\n  TeamOutlined,\n  CrownOutlined,\n  ApiOutlined,\n  PlusOutlined\n} from '@ant-design/icons';\nimport { DashboardLayout } from '@/components/layouts/dashboard-layout';\nimport { UserTable } from '@/modules/users/components/user-table';\nimport { UserForm } from '@/modules/users/components/user-form';\nimport { usePermissions } from '@/stores/auth-store';\nimport { User, UserFilters } from '@/modules/users/types';\nimport { SystemUser, RegisteredUser } from '@/modules/auth/types';\n\nconst { Title, Text } = Typography;\n\n// ============================================================================\n// MOCK DATA - sẽ được thay thế bằng real API calls\n// ============================================================================\n\nconst mockSystemUsers: SystemUser[] = [\n  {\n    id: 1,\n    username: 'admin',\n    email: '<EMAIL>',\n    role: 'admin',\n    isActive: true,\n    lastLoginAt: new Date('2024-01-15T10:30:00Z'),\n    createdAt: new Date('2024-01-01T00:00:00Z'),\n  },\n  {\n    id: 2,\n    username: 'editor1',\n    email: '<EMAIL>',\n    role: 'editor',\n    isActive: true,\n    lastLoginAt: new Date('2024-01-15T09:15:00Z'),\n    createdAt: new Date('2024-01-05T00:00:00Z'),\n  },\n  {\n    id: 3,\n    username: 'moderator1',\n    email: '<EMAIL>',\n    role: 'moderator',\n    isActive: false,\n    lastLoginAt: new Date('2024-01-10T14:20:00Z'),\n    createdAt: new Date('2024-01-10T00:00:00Z'),\n  },\n];\n\nconst mockRegisteredUsers: RegisteredUser[] = [\n  {\n    id: 1,\n    username: 'developer1',\n    email: '<EMAIL>',\n    tier: 'premium',\n    isActive: true,\n    isEmailVerified: true,\n    apiCallsUsed: 15000,\n    apiCallsLimit: 50000,\n    subscriptionEndDate: new Date('2024-12-31T23:59:59Z'),\n    lastLoginAt: new Date('2024-01-15T11:00:00Z'),\n    createdAt: new Date('2024-01-01T00:00:00Z'),\n  },\n  {\n    id: 2,\n    username: 'startup_user',\n    email: '<EMAIL>',\n    tier: 'enterprise',\n    isActive: true,\n    isEmailVerified: true,\n    apiCallsUsed: 75000,\n    apiCallsLimit: 200000,\n    subscriptionEndDate: new Date('2024-06-30T23:59:59Z'),\n    lastLoginAt: new Date('2024-01-15T08:30:00Z'),\n    createdAt: new Date('2023-12-15T00:00:00Z'),\n  },\n  {\n    id: 3,\n    username: 'free_user',\n    email: '<EMAIL>',\n    tier: 'free',\n    isActive: true,\n    isEmailVerified: false,\n    apiCallsUsed: 950,\n    apiCallsLimit: 1000,\n    subscriptionEndDate: null,\n    lastLoginAt: new Date('2024-01-14T16:45:00Z'),\n    createdAt: new Date('2024-01-14T00:00:00Z'),\n  },\n];\n\n// ============================================================================\n// USER MANAGEMENT PAGE COMPONENT\n// ============================================================================\n\nexport default function UserManagementPage() {\n  const permissions = usePermissions();\n\n  // State management\n  const [activeTab, setActiveTab] = useState('system');\n  const [loading, setLoading] = useState(false);\n  const [showForm, setShowForm] = useState(false);\n  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\n\n  // Filters\n  const [systemUserFilters, setSystemUserFilters] = useState<UserFilters>({});\n  const [registeredUserFilters, setRegisteredUserFilters] = useState<UserFilters>({});\n\n  // Pagination\n  const [systemUserPagination, setSystemUserPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: mockSystemUsers.length,\n  });\n\n  const [registeredUserPagination, setRegisteredUserPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: mockRegisteredUsers.length,\n  });\n\n  // ========================================================================\n  // PERMISSION CHECK\n  // ========================================================================\n\n  if (!permissions.canManageUsers) {\n    return (\n      <DashboardLayout>\n        <Card>\n          <div className=\"text-center py-8\">\n            <UserOutlined className=\"text-4xl text-gray-400 mb-4\" />\n            <Title level={3}>Access Denied</Title>\n            <Text type=\"secondary\">\n              You don't have permission to manage users.\n            </Text>\n          </div>\n        </Card>\n      </DashboardLayout>\n    );\n  }\n\n  // ========================================================================\n  // EVENT HANDLERS\n  // ========================================================================\n\n  const handleAddUser = (userType: 'system' | 'registered') => {\n    setFormMode('create');\n    setSelectedUser(null);\n    setActiveTab(userType);\n    setShowForm(true);\n  };\n\n  const handleEditUser = (user: User) => {\n    setFormMode('edit');\n    setSelectedUser(user);\n    setShowForm(true);\n  };\n\n  const handleDeleteUser = (user: User) => {\n    // Mock delete - sẽ được thay thế bằng API call\n    message.success(`User \"${user.username}\" deleted successfully`);\n  };\n\n  const handleFormSubmit = async (values: any) => {\n    setLoading(true);\n    try {\n      // Mock API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      message.success(\n        `User ${formMode === 'create' ? 'created' : 'updated'} successfully`\n      );\n      setShowForm(false);\n      setSelectedUser(null);\n    } catch (error) {\n      message.error('Operation failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRefresh = () => {\n    setLoading(true);\n    // Mock refresh\n    setTimeout(() => {\n      setLoading(false);\n      message.success('Data refreshed');\n    }, 1000);\n  };\n\n  const handleExport = () => {\n    message.info('Export functionality will be implemented');\n  };\n\n  // ========================================================================\n  // STATISTICS\n  // ========================================================================\n\n  const systemUserStats = {\n    total: mockSystemUsers.length,\n    active: mockSystemUsers.filter(u => u.isActive).length,\n    admins: mockSystemUsers.filter(u => u.role === 'admin').length,\n    editors: mockSystemUsers.filter(u => u.role === 'editor').length,\n  };\n\n  const registeredUserStats = {\n    total: mockRegisteredUsers.length,\n    active: mockRegisteredUsers.filter(u => u.isActive).length,\n    verified: mockRegisteredUsers.filter(u => u.isEmailVerified).length,\n    premium: mockRegisteredUsers.filter(u => u.tier === 'premium' || u.tier === 'enterprise').length,\n  };\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Page Header */}\n        <div>\n          <Title level={2} className=\"mb-2\">\n            User Management\n          </Title>\n          <Text type=\"secondary\" className=\"text-base\">\n            Manage system users and registered API users\n          </Text>\n        </div>\n\n        {/* Statistics Cards */}\n        <Row gutter={16}>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Total System Users\"\n                value={systemUserStats.total}\n                prefix={<CrownOutlined />}\n                valueStyle={{ color: '#1890ff' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Active System Users\"\n                value={systemUserStats.active}\n                prefix={<UserOutlined />}\n                valueStyle={{ color: '#52c41a' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Total Registered Users\"\n                value={registeredUserStats.total}\n                prefix={<TeamOutlined />}\n                valueStyle={{ color: '#722ed1' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Verified Users\"\n                value={registeredUserStats.verified}\n                prefix={<ApiOutlined />}\n                valueStyle={{ color: '#faad14' }}\n              />\n            </Card>\n          </Col>\n        </Row>\n\n        {/* User Management Tabs */}\n        <Card>\n          <Tabs\n            activeKey={activeTab}\n            onChange={setActiveTab}\n            tabBarExtraContent={\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => handleAddUser(activeTab as 'system' | 'registered')}\n              >\n                Add {activeTab === 'system' ? 'System User' : 'User'}\n              </Button>\n            }\n            items={[\n              {\n                key: 'system',\n                label: (\n                  <Space>\n                    <CrownOutlined />\n                    System Users ({systemUserStats.total})\n                  </Space>\n                ),\n                children: (\n                  <UserTable\n                    users={mockSystemUsers}\n                    loading={loading}\n                    userType=\"system\"\n                    onEdit={handleEditUser}\n                    onDelete={handleDeleteUser}\n                    onAdd={() => handleAddUser('system')}\n                    onRefresh={handleRefresh}\n                    onExport={handleExport}\n                    pagination={{\n                      ...systemUserPagination,\n                      onChange: (page, pageSize) => {\n                        setSystemUserPagination({ ...systemUserPagination, current: page, pageSize });\n                      },\n                    }}\n                    filters={systemUserFilters}\n                    onFiltersChange={setSystemUserFilters}\n                  />\n                ),\n              },\n              {\n                key: 'registered',\n                label: (\n                  <Space>\n                    <TeamOutlined />\n                    Registered Users ({registeredUserStats.total})\n                  </Space>\n                ),\n                children: (\n                  <UserTable\n                    users={mockRegisteredUsers}\n                    loading={loading}\n                    userType=\"registered\"\n                    onEdit={handleEditUser}\n                    onDelete={handleDeleteUser}\n                    onAdd={() => handleAddUser('registered')}\n                    onRefresh={handleRefresh}\n                    onExport={handleExport}\n                    pagination={{\n                      ...registeredUserPagination,\n                      onChange: (page, pageSize) => {\n                        setRegisteredUserPagination({ ...registeredUserPagination, current: page, pageSize });\n                      },\n                    }}\n                    filters={registeredUserFilters}\n                    onFiltersChange={setRegisteredUserFilters}\n                  />\n                ),\n              },\n            ]}\n          />\n        </Card>\n\n        {/* User Form Modal */}\n        <Modal\n          title={null}\n          open={showForm}\n          onCancel={() => setShowForm(false)}\n          footer={null}\n          width={800}\n          destroyOnHidden\n        >\n          <UserForm\n            userType={activeTab as 'system' | 'registered'}\n            mode={formMode}\n            initialValues={selectedUser || undefined}\n            loading={loading}\n            onSubmit={handleFormSubmit}\n            onCancel={() => setShowForm(false)}\n          />\n        </Modal>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,2CAA2C;AAC3C,qDAAqD;AAErD;AAoBA;AACA;AACA;AACA;AAtBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAAA;AAAA;AAAA;AAZA;AAAA;AAYA;AAZA;AAAA;AANA;;;;;;;;;AAgCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,+EAA+E;AAC/E,mDAAmD;AACnD,+EAA+E;AAE/E,MAAM,kBAAgC;IACpC;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;CACD;AAED,MAAM,sBAAwC;IAC5C;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,qBAAqB,IAAI,KAAK;QAC9B,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,qBAAqB,IAAI,KAAK;QAC9B,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,qBAAqB;QACrB,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;CACD;AAMc,SAAS;IACtB,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEjC,mBAAmB;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,UAAU;IACV,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,CAAC;IACzE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,CAAC;IAEjF,aAAa;IACb,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/D,SAAS;QACT,UAAU;QACV,OAAO,gBAAgB,MAAM;IAC/B;IAEA,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvE,SAAS;QACT,UAAU;QACV,OAAO,oBAAoB,MAAM;IACnC;IAEA,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAE3E,IAAI,CAAC,YAAY,cAAc,EAAE;QAC/B,qBACE,8OAAC,oJAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,8KAAA,CAAA,OAAI;0BACH,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;sCACxB,8OAAC;4BAAM,OAAO;sCAAG;;;;;;sCACjB,8OAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;;;;;;;;;;;IAOjC;IAEA,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,gBAAgB,CAAC;QACrB,YAAY;QACZ,gBAAgB;QAChB,aAAa;QACb,YAAY;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY;QACZ,gBAAgB;QAChB,YAAY;IACd;IAEA,MAAM,mBAAmB,CAAC;QACxB,+CAA+C;QAC/C,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,QAAQ,CAAC,sBAAsB,CAAC;IAChE;IAEA,MAAM,mBAAmB,OAAO;QAC9B,WAAW;QACX,IAAI;YACF,gBAAgB;YAChB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,oLAAA,CAAA,UAAO,CAAC,OAAO,CACb,CAAC,KAAK,EAAE,aAAa,WAAW,YAAY,UAAU,aAAa,CAAC;YAEtE,YAAY;YACZ,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,WAAW;QACX,eAAe;QACf,WAAW;YACT,WAAW;YACX,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB,GAAG;IACL;IAEA,MAAM,eAAe;QACnB,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;IACf;IAEA,2EAA2E;IAC3E,aAAa;IACb,2EAA2E;IAE3E,MAAM,kBAAkB;QACtB,OAAO,gBAAgB,MAAM;QAC7B,QAAQ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;QACtD,QAAQ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;QAC9D,SAAS,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;IAClE;IAEA,MAAM,sBAAsB;QAC1B,OAAO,oBAAoB,MAAM;QACjC,QAAQ,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;QAC1D,UAAU,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,EAAE,MAAM;QACnE,SAAS,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,IAAI,KAAK,cAAc,MAAM;IAClG;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,8OAAC,oJAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;;sCACC,8OAAC;4BAAM,OAAO;4BAAG,WAAU;sCAAO;;;;;;sCAGlC,8OAAC;4BAAK,MAAK;4BAAY,WAAU;sCAAY;;;;;;;;;;;;8BAM/C,8OAAC,4KAAA,CAAA,MAAG;oBAAC,QAAQ;;sCACX,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,gBAAgB,KAAK;oCAC5B,sBAAQ,8OAAC,oNAAA,CAAA,gBAAa;;;;;oCACtB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;sCAIrC,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,gBAAgB,MAAM;oCAC7B,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACrB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;sCAIrC,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,oBAAoB,KAAK;oCAChC,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACrB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;sCAIrC,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,oBAAoB,QAAQ;oCACnC,sBAAQ,8OAAC,gNAAA,CAAA,cAAW;;;;;oCACpB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;;;;;;;8BAOvC,8OAAC,8KAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,8KAAA,CAAA,OAAI;wBACH,WAAW;wBACX,UAAU;wBACV,kCACE,8OAAC,kMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM,cAAc;;gCAC9B;gCACM,cAAc,WAAW,gBAAgB;;;;;;;wBAGlD,OAAO;4BACL;gCACE,KAAK;gCACL,qBACE,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC,oNAAA,CAAA,gBAAa;;;;;wCAAG;wCACF,gBAAgB,KAAK;wCAAC;;;;;;;gCAGzC,wBACE,8OAAC,uJAAA,CAAA,YAAS;oCACR,OAAO;oCACP,SAAS;oCACT,UAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,OAAO,IAAM,cAAc;oCAC3B,WAAW;oCACX,UAAU;oCACV,YAAY;wCACV,GAAG,oBAAoB;wCACvB,UAAU,CAAC,MAAM;4CACf,wBAAwB;gDAAE,GAAG,oBAAoB;gDAAE,SAAS;gDAAM;4CAAS;wCAC7E;oCACF;oCACA,SAAS;oCACT,iBAAiB;;;;;;4BAGvB;4BACA;gCACE,KAAK;gCACL,qBACE,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCAAG;wCACG,oBAAoB,KAAK;wCAAC;;;;;;;gCAGjD,wBACE,8OAAC,uJAAA,CAAA,YAAS;oCACR,OAAO;oCACP,SAAS;oCACT,UAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,OAAO,IAAM,cAAc;oCAC3B,WAAW;oCACX,UAAU;oCACV,YAAY;wCACV,GAAG,wBAAwB;wCAC3B,UAAU,CAAC,MAAM;4CACf,4BAA4B;gDAAE,GAAG,wBAAwB;gDAAE,SAAS;gDAAM;4CAAS;wCACrF;oCACF;oCACA,SAAS;oCACT,iBAAiB;;;;;;4BAGvB;yBACD;;;;;;;;;;;8BAKL,8OAAC,gLAAA,CAAA,QAAK;oBACJ,OAAO;oBACP,MAAM;oBACN,UAAU,IAAM,YAAY;oBAC5B,QAAQ;oBACR,OAAO;oBACP,eAAe;8BAEf,cAAA,8OAAC,sJAAA,CAAA,WAAQ;wBACP,UAAU;wBACV,MAAM;wBACN,eAAe,gBAAgB;wBAC/B,SAAS;wBACT,UAAU;wBACV,UAAU,IAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;AAMxC"}}, {"offset": {"line": 3750, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}