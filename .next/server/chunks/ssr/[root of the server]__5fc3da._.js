module.exports = {

"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/shared/constants/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// APISportsGame CMS - Shared Constants
// Application-wide constants
// ============================================================================
// API CONFIGURATION
// ============================================================================
__turbopack_esm__({
    "API_CONFIG": (()=>API_CONFIG),
    "API_LIMITS": (()=>API_LIMITS),
    "DATE_FORMATS": (()=>DATE_FORMATS),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "PAGINATION": (()=>PAGINATION),
    "PERMISSIONS": (()=>PERMISSIONS),
    "QUERY_KEYS": (()=>QUERY_KEYS),
    "ROLE_COLORS": (()=>ROLE_COLORS),
    "ROUTES": (()=>ROUTES),
    "STATUS_COLORS": (()=>STATUS_COLORS),
    "STORAGE_KEYS": (()=>STORAGE_KEYS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "TIER_COLORS": (()=>TIER_COLORS),
    "UI": (()=>UI),
    "USER_ROLES": (()=>USER_ROLES),
    "USER_TIERS": (()=>USER_TIERS),
    "VALIDATION": (()=>VALIDATION),
    "default": (()=>__TURBOPACK__default__export__)
});
const API_CONFIG = {
    BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',
    TIMEOUT: 10000,
    RETRY_ATTEMPTS: 3
};
const PAGINATION = {
    DEFAULT_PAGE: 1,
    DEFAULT_PAGE_SIZE: 10,
    PAGE_SIZE_OPTIONS: [
        10,
        20,
        50,
        100
    ],
    MAX_PAGE_SIZE: 100
};
const VALIDATION = {
    USERNAME: {
        MIN_LENGTH: 3,
        MAX_LENGTH: 50,
        PATTERN: /^[a-zA-Z0-9_]+$/
    },
    PASSWORD: {
        MIN_LENGTH: 6,
        MAX_LENGTH: 100
    },
    EMAIL: {
        PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    },
    SEARCH: {
        MIN_LENGTH: 2,
        MAX_LENGTH: 100
    }
};
const UI = {
    DEBOUNCE_DELAY: 300,
    ANIMATION_DURATION: 200,
    NOTIFICATION_DURATION: 4000,
    MODAL_WIDTH: {
        SMALL: 400,
        MEDIUM: 600,
        LARGE: 800,
        EXTRA_LARGE: 1000
    }
};
const STATUS_COLORS = {
    SUCCESS: '#52c41a',
    ERROR: '#ff4d4f',
    WARNING: '#faad14',
    INFO: '#1890ff',
    PROCESSING: '#722ed1',
    DEFAULT: '#d9d9d9'
};
const USER_ROLES = {
    ADMIN: 'admin',
    EDITOR: 'editor',
    MODERATOR: 'moderator'
};
const USER_TIERS = {
    FREE: 'free',
    PREMIUM: 'premium',
    ENTERPRISE: 'enterprise'
};
const ROLE_COLORS = {
    [USER_ROLES.ADMIN]: 'red',
    [USER_ROLES.EDITOR]: 'blue',
    [USER_ROLES.MODERATOR]: 'green'
};
const TIER_COLORS = {
    [USER_TIERS.FREE]: 'default',
    [USER_TIERS.PREMIUM]: 'gold',
    [USER_TIERS.ENTERPRISE]: 'purple'
};
const API_LIMITS = {
    FREE_TIER: 1000,
    PREMIUM_TIER: 50000,
    ENTERPRISE_TIER: 200000,
    UNLIMITED: null
};
const DATE_FORMATS = {
    DISPLAY: 'MMM DD, YYYY',
    DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',
    ISO: 'YYYY-MM-DD',
    ISO_WITH_TIME: 'YYYY-MM-DD HH:mm:ss',
    TIME_ONLY: 'HH:mm',
    MONTH_YEAR: 'MMM YYYY',
    FULL: 'dddd, MMMM DD, YYYY'
};
const ROUTES = {
    HOME: '/',
    LOGIN: '/auth/login',
    DASHBOARD: '/dashboard',
    USERS: '/dashboard/users',
    LEAGUES: '/dashboard/leagues',
    TEAMS: '/dashboard/teams',
    FIXTURES: '/dashboard/fixtures',
    ANALYTICS: '/dashboard/analytics',
    SYNC: '/dashboard/sync',
    PROFILE: '/dashboard/profile',
    SETTINGS: '/dashboard/settings'
};
const STORAGE_KEYS = {
    ACCESS_TOKEN: 'access_token',
    REFRESH_TOKEN: 'refresh_token',
    USER_PREFERENCES: 'user_preferences',
    THEME: 'theme',
    LANGUAGE: 'language'
};
const ERROR_MESSAGES = {
    NETWORK_ERROR: 'Network error. Please check your connection.',
    UNAUTHORIZED: 'You are not authorized to perform this action.',
    FORBIDDEN: 'Access denied. Insufficient permissions.',
    NOT_FOUND: 'The requested resource was not found.',
    VALIDATION_ERROR: 'Please check your input and try again.',
    SERVER_ERROR: 'Internal server error. Please try again later.',
    UNKNOWN_ERROR: 'An unknown error occurred.'
};
const SUCCESS_MESSAGES = {
    CREATED: 'Created successfully',
    UPDATED: 'Updated successfully',
    DELETED: 'Deleted successfully',
    SAVED: 'Saved successfully',
    SYNCED: 'Synced successfully',
    EXPORTED: 'Exported successfully'
};
const QUERY_KEYS = {
    AUTH: [
        'auth'
    ],
    USERS: [
        'users'
    ],
    LEAGUES: [
        'leagues'
    ],
    TEAMS: [
        'teams'
    ],
    FIXTURES: [
        'fixtures'
    ],
    DASHBOARD: [
        'dashboard'
    ],
    SYNC: [
        'sync'
    ]
};
const PERMISSIONS = {
    USERS: {
        VIEW: 'users:view',
        CREATE: 'users:create',
        UPDATE: 'users:update',
        DELETE: 'users:delete'
    },
    LEAGUES: {
        VIEW: 'leagues:view',
        CREATE: 'leagues:create',
        UPDATE: 'leagues:update',
        DELETE: 'leagues:delete'
    },
    TEAMS: {
        VIEW: 'teams:view',
        CREATE: 'teams:create',
        UPDATE: 'teams:update',
        DELETE: 'teams:delete'
    },
    FIXTURES: {
        VIEW: 'fixtures:view',
        CREATE: 'fixtures:create',
        UPDATE: 'fixtures:update',
        DELETE: 'fixtures:delete'
    },
    SYNC: {
        VIEW: 'sync:view',
        EXECUTE: 'sync:execute'
    },
    ANALYTICS: {
        VIEW: 'analytics:view'
    }
};
const __TURBOPACK__default__export__ = {
    API_CONFIG,
    PAGINATION,
    VALIDATION,
    UI,
    STATUS_COLORS,
    USER_ROLES,
    USER_TIERS,
    ROLE_COLORS,
    TIER_COLORS,
    API_LIMITS,
    DATE_FORMATS,
    ROUTES,
    STORAGE_KEYS,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES,
    QUERY_KEYS,
    PERMISSIONS
};
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/shared/utils/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// APISportsGame CMS - Shared API Utilities
// Common API utilities và error handling
__turbopack_esm__({
    "ApiError": (()=>ApiError),
    "apiUtils": (()=>apiUtils),
    "default": (()=>__TURBOPACK__default__export__),
    "paginationUtils": (()=>paginationUtils)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/node_modules/axios/index.js [app-ssr] (ecmascript) <locals>");
;
class ApiError extends Error {
    statusCode;
    response;
    constructor(error){
        const response = error.response?.data;
        const message = Array.isArray(response?.message) ? response.message.join(', ') : response?.message || error.message || 'An error occurred';
        super(message);
        this.name = 'ApiError';
        this.statusCode = error.response?.status || 500;
        this.response = response;
    }
}
const apiUtils = {
    /**
   * Handle API errors consistently
   */ handleError: (error)=>{
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["AxiosError"]) {
            return new ApiError(error);
        }
        if (error instanceof Error) {
            const apiError = new ApiError({});
            apiError.message = error.message;
            return apiError;
        }
        const apiError = new ApiError({});
        apiError.message = 'Unknown error occurred';
        return apiError;
    },
    /**
   * Extract error message for display
   */ getErrorMessage: (error)=>{
        const apiError = apiUtils.handleError(error);
        return apiError.message;
    },
    /**
   * Check if error is specific status code
   */ isErrorStatus: (error, statusCode)=>{
        const apiError = apiUtils.handleError(error);
        return apiError.statusCode === statusCode;
    },
    /**
   * Check if error is unauthorized
   */ isUnauthorized: (error)=>{
        return apiUtils.isErrorStatus(error, 401);
    },
    /**
   * Check if error is forbidden
   */ isForbidden: (error)=>{
        return apiUtils.isErrorStatus(error, 403);
    },
    /**
   * Check if error is not found
   */ isNotFound: (error)=>{
        return apiUtils.isErrorStatus(error, 404);
    },
    /**
   * Check if error is validation error
   */ isValidationError: (error)=>{
        return apiUtils.isErrorStatus(error, 400);
    },
    /**
   * Format pagination params
   */ formatPaginationParams: (page, limit)=>({
            page,
            limit,
            offset: (page - 1) * limit
        }),
    /**
   * Format filter params (remove undefined values)
   */ formatFilterParams: (filters)=>{
        const cleanFilters = {};
        Object.entries(filters).forEach(([key, value])=>{
            if (value !== undefined && value !== null && value !== '') {
                cleanFilters[key] = value;
            }
        });
        return cleanFilters;
    },
    /**
   * Build query string from params
   */ buildQueryString: (params)=>{
        const cleanParams = apiUtils.formatFilterParams(params);
        const searchParams = new URLSearchParams();
        Object.entries(cleanParams).forEach(([key, value])=>{
            if (Array.isArray(value)) {
                value.forEach((v)=>searchParams.append(key, String(v)));
            } else {
                searchParams.append(key, String(value));
            }
        });
        return searchParams.toString();
    }
};
const paginationUtils = {
    /**
   * Calculate total pages
   */ calculateTotalPages: (total, limit)=>{
        return Math.ceil(total / limit);
    },
    /**
   * Calculate offset from page and limit
   */ calculateOffset: (page, limit)=>{
        return (page - 1) * limit;
    },
    /**
   * Get pagination info for display
   */ getPaginationInfo: (meta)=>{
        const start = paginationUtils.calculateOffset(meta.page, meta.limit) + 1;
        const end = Math.min(start + meta.limit - 1, meta.total);
        return {
            start,
            end,
            total: meta.total,
            hasNext: meta.page < meta.totalPages,
            hasPrev: meta.page > 1
        };
    }
};
const __TURBOPACK__default__export__ = apiUtils;
}}),
"[project]/src/modules/auth/api/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// APISportsGame CMS - Auth API Module
// Authentication API calls
__turbopack_esm__({
    "authApi": (()=>authApi),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/shared/constants/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/shared/utils/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
;
;
;
// ============================================================================
// AUTH API ENDPOINTS
// ============================================================================
const AUTH_ENDPOINTS = {
    login: '/auth/login',
    profile: '/auth/profile',
    refresh: '/auth/refresh',
    logout: '/auth/logout',
    adminRegister: '/auth/admin/register',
    userRegister: '/users/register',
    userLogin: '/users/login',
    verifyEmail: '/users/verify-email'
};
// ============================================================================
// AUTH API CLIENT
// ============================================================================
class AuthApiClient {
    instance;
    constructor(){
        this.instance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].BASE_URL,
            timeout: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].TIMEOUT,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        this.setupInterceptors();
    }
    setupInterceptors() {
        // Request interceptor - add JWT token
        this.instance.interceptors.request.use((config)=>{
            const token = this.getToken();
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }
            return config;
        }, (error)=>Promise.reject(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiUtils"].handleError(error)));
        // Response interceptor - handle errors
        this.instance.interceptors.response.use((response)=>response, async (error)=>{
            const originalRequest = error.config;
            // Handle 401 errors - token expired
            if (error.response?.status === 401 && !originalRequest._retry) {
                originalRequest._retry = true;
                try {
                    await this.refreshToken();
                    const token = this.getToken();
                    if (token) {
                        originalRequest.headers.Authorization = `Bearer ${token}`;
                        return this.instance(originalRequest);
                    }
                } catch (refreshError) {
                    this.clearTokens();
                    // Redirect to login will be handled by auth store
                    throw __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiUtils"].handleError(refreshError);
                }
            }
            throw __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiUtils"].handleError(error);
        });
    }
    // ========================================================================
    // TOKEN MANAGEMENT
    // ========================================================================
    getToken() {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return null;
    }
    setToken(token) {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
    getRefreshToken() {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return null;
    }
    setRefreshToken(token) {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
    clearTokens() {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
    // ========================================================================
    // AUTH API METHODS
    // ========================================================================
    /**
   * Login user (both SystemUser and RegisteredUser)
   */ async login(credentials) {
        try {
            const response = await this.instance.post(AUTH_ENDPOINTS.login, credentials);
            const { access_token, refresh_token } = response.data;
            this.setToken(access_token);
            this.setRefreshToken(refresh_token);
            return response.data;
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiUtils"].handleError(error);
        }
    }
    /**
   * Get current user profile
   */ async getProfile() {
        try {
            const response = await this.instance.get(AUTH_ENDPOINTS.profile);
            return response.data;
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiUtils"].handleError(error);
        }
    }
    /**
   * Refresh access token
   */ async refreshToken() {
        try {
            const refreshToken = this.getRefreshToken();
            if (!refreshToken) {
                throw new Error('No refresh token available');
            }
            const response = await this.instance.post(AUTH_ENDPOINTS.refresh, {
                refresh_token: refreshToken
            });
            const { access_token, refresh_token: newRefreshToken } = response.data;
            this.setToken(access_token);
            this.setRefreshToken(newRefreshToken);
            return response.data;
        } catch (error) {
            this.clearTokens();
            throw __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiUtils"].handleError(error);
        }
    }
    /**
   * Logout user
   */ async logout() {
        try {
            await this.instance.post(AUTH_ENDPOINTS.logout);
        } catch (error) {
            // Continue with logout even if API call fails
            console.warn('Logout API call failed:', error);
        } finally{
            this.clearTokens();
        }
    }
    /**
   * Register new system user (admin only)
   */ async registerSystemUser(userData) {
        try {
            const response = await this.instance.post(AUTH_ENDPOINTS.adminRegister, userData);
            return response.data;
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiUtils"].handleError(error);
        }
    }
    /**
   * Register new registered user
   */ async registerUser(userData) {
        try {
            const response = await this.instance.post(AUTH_ENDPOINTS.userRegister, userData);
            return response.data;
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiUtils"].handleError(error);
        }
    }
    /**
   * Verify email for registered user
   */ async verifyEmail(token) {
        try {
            await this.instance.post(AUTH_ENDPOINTS.verifyEmail, {
                token
            });
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiUtils"].handleError(error);
        }
    }
    /**
   * Check if user is authenticated
   */ isAuthenticated() {
        return !!this.getToken();
    }
    /**
   * Get current access token
   */ getCurrentToken() {
        return this.getToken();
    }
}
const authApi = new AuthApiClient();
const __TURBOPACK__default__export__ = authApi;
}}),
"[project]/src/modules/auth/types/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// APISportsGame CMS - Auth Module Types
// Authentication related types
__turbopack_esm__({
    "isRegisteredUser": (()=>isRegisteredUser),
    "isRegisteredUserToken": (()=>isRegisteredUserToken),
    "isSystemUser": (()=>isSystemUser),
    "isSystemUserToken": (()=>isSystemUserToken)
});
const isSystemUser = (user)=>{
    return user !== null && 'role' in user;
};
const isRegisteredUser = (user)=>{
    return user !== null && 'tier' in user;
};
const isSystemUserToken = (token)=>{
    return token.userType === 'system';
};
const isRegisteredUserToken = (token)=>{
    return token.userType === 'registered';
}; // ============================================================================
 // EXPORT ALL
 // ============================================================================
 // Remove circular export
}}),
"[project]/src/stores/auth-store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// APISportsGame CMS - Authentication Store
// Zustand store cho dual authentication system
__turbopack_esm__({
    "useAuth": (()=>useAuth),
    "useAuthStore": (()=>useAuthStore),
    "usePermissions": (()=>usePermissions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$auth$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/modules/auth/api/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$auth$2f$types$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/modules/auth/types/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
;
;
const useAuthStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // Initial state
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        // ========================================================================
        // LOGIN ACTION
        // ========================================================================
        login: async (credentials)=>{
            set({
                isLoading: true,
                error: null
            });
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$auth$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authApi"].login(credentials);
                set({
                    user: response.user,
                    isAuthenticated: true,
                    isLoading: false,
                    error: null
                });
            } catch (error) {
                const errorMessage = error.response?.data?.message || error.message || 'Đăng nhập thất bại';
                set({
                    user: null,
                    isAuthenticated: false,
                    isLoading: false,
                    error: errorMessage
                });
                throw error;
            }
        },
        // ========================================================================
        // LOGOUT ACTION
        // ========================================================================
        logout: ()=>{
            try {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$auth$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authApi"].logout();
            } catch (error) {
                console.error('Logout error:', error);
            } finally{
                set({
                    user: null,
                    isAuthenticated: false,
                    isLoading: false,
                    error: null
                });
            }
        },
        // ========================================================================
        // GET PROFILE ACTION
        // ========================================================================
        getProfile: async ()=>{
            set({
                isLoading: true,
                error: null
            });
            try {
                const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$auth$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authApi"].getProfile();
                set({
                    user,
                    isAuthenticated: true,
                    isLoading: false,
                    error: null
                });
            } catch (error) {
                const errorMessage = error.response?.data?.message || error.message || 'Không thể lấy thông tin người dùng';
                set({
                    user: null,
                    isAuthenticated: false,
                    isLoading: false,
                    error: errorMessage
                });
                throw error;
            }
        },
        // ========================================================================
        // CLEAR ERROR ACTION
        // ========================================================================
        clearError: ()=>{
            set({
                error: null
            });
        },
        // ========================================================================
        // HELPER METHODS
        // ========================================================================
        isSystemUser: ()=>{
            const { user } = get();
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$auth$2f$types$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isSystemUser"])(user);
        },
        isRegisteredUser: ()=>{
            const { user } = get();
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$auth$2f$types$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isRegisteredUser"])(user);
        },
        hasRole: (role)=>{
            const { user, isSystemUser } = get();
            if (!isSystemUser() || !user) return false;
            const systemUser = user;
            return systemUser.role === role;
        },
        hasTier: (tier)=>{
            const { user, isRegisteredUser } = get();
            if (!isRegisteredUser() || !user) return false;
            const registeredUser = user;
            return registeredUser.tier === tier;
        }
    }), {
    name: 'auth-storage',
    partialize: (state)=>({
            user: state.user,
            isAuthenticated: state.isAuthenticated
        })
}));
const usePermissions = ()=>{
    const { user, isSystemUser, isRegisteredUser, hasRole, hasTier } = useAuthStore();
    return {
        // User type checks
        isSystemUser: isSystemUser(),
        isRegisteredUser: isRegisteredUser(),
        // Role checks (SystemUser)
        isAdmin: hasRole('admin'),
        isEditor: hasRole('editor'),
        isModerator: hasRole('moderator'),
        // Tier checks (RegisteredUser)
        isFree: hasTier('free'),
        isPremium: hasTier('premium'),
        isEnterprise: hasTier('enterprise'),
        // Permission checks
        canManageUsers: hasRole('admin'),
        canManageLeagues: hasRole('admin') || hasRole('editor'),
        canManageTeams: hasRole('admin') || hasRole('editor'),
        canManageFixtures: hasRole('admin') || hasRole('editor') || hasRole('moderator'),
        canSync: hasRole('admin') || hasRole('editor'),
        canViewAnalytics: hasRole('admin') || hasRole('editor'),
        // Current user
        currentUser: user
    };
};
const useAuth = ()=>{
    const { user, isAuthenticated, isLoading, error, login, logout, getProfile, clearError } = useAuthStore();
    return {
        user,
        isAuthenticated,
        isLoading,
        error,
        login,
        logout,
        getProfile,
        clearError
    };
};
}}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
// APISportsGame CMS - Home Page
// Redirect to appropriate page based on authentication status
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$spin$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Spin$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/spin/index.js [app-ssr] (ecmascript) <export default as Spin>");
'use client';
;
;
;
;
;
function Home() {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const { isAuthenticated, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isLoading) {
            if (isAuthenticated) {
                router.push('/dashboard');
            } else {
                router.push('/auth/login');
            }
        }
    }, [
        isAuthenticated,
        isLoading,
        router
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen flex items-center justify-center",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$spin$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Spin$3e$__["Spin"], {
            size: "large",
            tip: "Đang tải..."
        }, void 0, false, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 27,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 26,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__5fc3da._.js.map