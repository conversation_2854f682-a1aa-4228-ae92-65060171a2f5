{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts"], "sourcesContent": ["// APISportsGame CMS - Authentication Store\n// Zustand store cho dual authentication system\n\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { authApi } from '@/modules/auth/api';\nimport {\n  SystemUser,\n  RegisteredUser,\n  LoginRequest,\n  AuthState,\n  AuthActions,\n  UseAuthReturn,\n  UsePermissionsReturn,\n  isSystemUser,\n  isRegisteredUser,\n} from '@/modules/auth/types';\n\n// ============================================================================\n// AUTH STORE TYPES\n// ============================================================================\n\ninterface AuthStoreState extends AuthState, AuthActions {\n  // Helpers\n  isSystemUser: () => boolean;\n  isRegisteredUser: () => boolean;\n  hasRole: (role: string) => boolean;\n  hasTier: (tier: string) => boolean;\n}\n\n// ============================================================================\n// AUTH STORE IMPLEMENTATION\n// ============================================================================\n\nexport const useAuthStore = create<AuthStoreState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // ========================================================================\n      // LOGIN ACTION\n      // ========================================================================\n      login: async (credentials: LoginRequest) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const response = await apiClient.login(credentials);\n\n          set({\n            user: response.user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Đăng nhập thất bại';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // LOGOUT ACTION\n      // ========================================================================\n      logout: () => {\n        try {\n          apiClient.logoutUser();\n        } catch (error) {\n          console.error('Logout error:', error);\n        } finally {\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n        }\n      },\n\n      // ========================================================================\n      // GET PROFILE ACTION\n      // ========================================================================\n      getProfile: async () => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const user = await apiClient.getProfile();\n\n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Không thể lấy thông tin người dùng';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // CLEAR ERROR ACTION\n      // ========================================================================\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // ========================================================================\n      // HELPER METHODS\n      // ========================================================================\n      isSystemUser: () => {\n        const { user } = get();\n        return user !== null && 'role' in user;\n      },\n\n      isRegisteredUser: () => {\n        const { user } = get();\n        return user !== null && 'tier' in user;\n      },\n\n      hasRole: (role: string) => {\n        const { user, isSystemUser } = get();\n        if (!isSystemUser() || !user) return false;\n\n        const systemUser = user as SystemUser;\n        return systemUser.role === role;\n      },\n\n      hasTier: (tier: string) => {\n        const { user, isRegisteredUser } = get();\n        if (!isRegisteredUser() || !user) return false;\n\n        const registeredUser = user as RegisteredUser;\n        return registeredUser.tier === tier;\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// ============================================================================\n// AUTH HOOKS\n// ============================================================================\n\n// Hook để check permissions\nexport const usePermissions = () => {\n  const { user, isSystemUser, isRegisteredUser, hasRole, hasTier } = useAuthStore();\n\n  return {\n    // User type checks\n    isSystemUser: isSystemUser(),\n    isRegisteredUser: isRegisteredUser(),\n\n    // Role checks (SystemUser)\n    isAdmin: hasRole('admin'),\n    isEditor: hasRole('editor'),\n    isModerator: hasRole('moderator'),\n\n    // Tier checks (RegisteredUser)\n    isFree: hasTier('free'),\n    isPremium: hasTier('premium'),\n    isEnterprise: hasTier('enterprise'),\n\n    // Permission checks\n    canManageUsers: hasRole('admin'),\n    canManageLeagues: hasRole('admin') || hasRole('editor'),\n    canManageTeams: hasRole('admin') || hasRole('editor'),\n    canManageFixtures: hasRole('admin') || hasRole('editor') || hasRole('moderator'),\n    canSync: hasRole('admin') || hasRole('editor'),\n    canViewAnalytics: hasRole('admin') || hasRole('editor'),\n\n    // Current user\n    currentUser: user,\n  };\n};\n\n// Hook để check authentication status\nexport const useAuth = () => {\n  const {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    getProfile,\n    clearError\n  } = useAuthStore();\n\n  return {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    getProfile,\n    clearError,\n  };\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,+CAA+C;;;;;;AAE/C;AACA;;;AA8BO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,2EAA2E;QAC3E,eAAe;QACf,2EAA2E;QAC3E,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,UAAU,KAAK,CAAC;gBAEvC,IAAI;oBACF,MAAM,SAAS,IAAI;oBACnB,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,gBAAgB;QAChB,2EAA2E;QAC3E,QAAQ;YACN,IAAI;gBACF,UAAU,UAAU;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC,SAAU;gBACR,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,OAAO,MAAM,UAAU,UAAU;gBAEvC,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,2EAA2E;QAC3E,iBAAiB;QACjB,2EAA2E;QAC3E,cAAc;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,SAAS,QAAQ,UAAU;QACpC;QAEA,kBAAkB;YAChB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,SAAS,QAAQ,UAAU;QACpC;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG;YAC/B,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO;YAErC,MAAM,aAAa;YACnB,OAAO,WAAW,IAAI,KAAK;QAC7B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG;YACnC,IAAI,CAAC,sBAAsB,CAAC,MAAM,OAAO;YAEzC,MAAM,iBAAiB;YACvB,OAAO,eAAe,IAAI,KAAK;QACjC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AASG,MAAM,iBAAiB;IAC5B,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnE,OAAO;QACL,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAElB,2BAA2B;QAC3B,SAAS,QAAQ;QACjB,UAAU,QAAQ;QAClB,aAAa,QAAQ;QAErB,+BAA+B;QAC/B,QAAQ,QAAQ;QAChB,WAAW,QAAQ;QACnB,cAAc,QAAQ;QAEtB,oBAAoB;QACpB,gBAAgB,QAAQ;QACxB,kBAAkB,QAAQ,YAAY,QAAQ;QAC9C,gBAAgB,QAAQ,YAAY,QAAQ;QAC5C,mBAAmB,QAAQ,YAAY,QAAQ,aAAa,QAAQ;QACpE,SAAS,QAAQ,YAAY,QAAQ;QACrC,kBAAkB,QAAQ,YAAY,QAAQ;QAE9C,eAAe;QACf,aAAa;IACf;AACF;AAGO,MAAM,UAAU;IACrB,MAAM,EACJ,IAAI,EACJ,eAAe,EACf,SAAS,EACT,KAAK,EACL,KAAK,EACL,MAAM,EACN,UAAU,EACV,UAAU,EACX,GAAG;IAEJ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF"}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/page.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Home Page\n// Redirect to appropriate page based on authentication status\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Spin } from 'antd';\nimport { useAuth } from '@/stores/auth-store';\n\nexport default function Home() {\n  const router = useRouter();\n  const { isAuthenticated, isLoading } = useAuth();\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (isAuthenticated) {\n        router.push('/dashboard');\n      } else {\n        router.push('/auth/login');\n      }\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <Spin size=\"large\" tip=\"Đang tải...\" />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,gCAAgC;AAChC,8DAA8D;AAE9D;AACA;AAEA;AADA;AAPA;;;;;;AAUe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,8KAAA,CAAA,OAAI;YAAC,MAAK;YAAQ,KAAI;;;;;;;;;;;AAG7B"}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}