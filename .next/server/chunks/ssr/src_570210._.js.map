{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/shared/utils/date.ts"], "sourcesContent": ["// APISportsGame CMS - Shared Date Utilities\n// Centralized date handling với dayjs\n\nimport dayjs from 'dayjs';\nimport relativeTime from 'dayjs/plugin/relativeTime';\nimport utc from 'dayjs/plugin/utc';\nimport timezone from 'dayjs/plugin/timezone';\nimport customParseFormat from 'dayjs/plugin/customParseFormat';\n\n// ============================================================================\n// DAYJS CONFIGURATION\n// ============================================================================\n\n// Extend dayjs với required plugins\ndayjs.extend(relativeTime);\ndayjs.extend(utc);\ndayjs.extend(timezone);\ndayjs.extend(customParseFormat);\n\n// ============================================================================\n// DATE UTILITIES\n// ============================================================================\n\nexport const dateUtils = {\n  /**\n   * Format date to relative time (e.g., \"2 hours ago\")\n   */\n  fromNow: (date: string | Date) => {\n    return dayjs(date).fromNow();\n  },\n\n  /**\n   * Format date to standard format\n   */\n  format: (date: string | Date, format = 'YYYY-MM-DD HH:mm:ss') => {\n    return dayjs(date).format(format);\n  },\n\n  /**\n   * Format date for display\n   */\n  formatDisplay: (date: string | Date) => {\n    return dayjs(date).format('MMM DD, YYYY');\n  },\n\n  /**\n   * Format date with time for display\n   */\n  formatDateTime: (date: string | Date) => {\n    return dayjs(date).format('MMM DD, YYYY HH:mm');\n  },\n\n  /**\n   * Check if date is today\n   */\n  isToday: (date: string | Date) => {\n    return dayjs(date).isSame(dayjs(), 'day');\n  },\n\n  /**\n   * Check if date is yesterday\n   */\n  isYesterday: (date: string | Date) => {\n    return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day');\n  },\n\n  /**\n   * Get start of day\n   */\n  startOfDay: (date?: string | Date) => {\n    return dayjs(date).startOf('day');\n  },\n\n  /**\n   * Get end of day\n   */\n  endOfDay: (date?: string | Date) => {\n    return dayjs(date).endOf('day');\n  },\n\n  /**\n   * Convert to ISO string\n   */\n  toISOString: (date: string | Date) => {\n    return dayjs(date).toISOString();\n  },\n\n  /**\n   * Parse date for form inputs\n   */\n  parseForForm: (date: string | Date | null) => {\n    if (!date) return null;\n    return dayjs(date);\n  },\n\n  /**\n   * Get current timestamp\n   */\n  now: () => {\n    return dayjs();\n  },\n\n  /**\n   * Add time to date\n   */\n  add: (date: string | Date, amount: number, unit: dayjs.ManipulateType) => {\n    return dayjs(date).add(amount, unit);\n  },\n\n  /**\n   * Subtract time from date\n   */\n  subtract: (date: string | Date, amount: number, unit: dayjs.ManipulateType) => {\n    return dayjs(date).subtract(amount, unit);\n  },\n\n  /**\n   * Check if date is before another date\n   */\n  isBefore: (date1: string | Date, date2: string | Date) => {\n    return dayjs(date1).isBefore(dayjs(date2));\n  },\n\n  /**\n   * Check if date is after another date\n   */\n  isAfter: (date1: string | Date, date2: string | Date) => {\n    return dayjs(date1).isAfter(dayjs(date2));\n  },\n\n  /**\n   * Get difference between dates\n   */\n  diff: (date1: string | Date, date2: string | Date, unit?: dayjs.QUnitType) => {\n    return dayjs(date1).diff(dayjs(date2), unit);\n  },\n};\n\n// ============================================================================\n// COMMON DATE FORMATS\n// ============================================================================\n\nexport const DATE_FORMATS = {\n  DISPLAY: 'MMM DD, YYYY',\n  DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',\n  ISO: 'YYYY-MM-DD',\n  ISO_WITH_TIME: 'YYYY-MM-DD HH:mm:ss',\n  TIME_ONLY: 'HH:mm',\n  MONTH_YEAR: 'MMM YYYY',\n  FULL: 'dddd, MMMM DD, YYYY',\n} as const;\n\n// ============================================================================\n// EXPORTS\n// ============================================================================\n\nexport { dayjs };\nexport default dateUtils;\n"], "names": [], "mappings": "AAAA,4CAA4C;AAC5C,sCAAsC;;;;;;AAEtC;AACA;AACA;AACA;AACA;;;;;;AAEA,+EAA+E;AAC/E,sBAAsB;AACtB,+EAA+E;AAE/E,oCAAoC;AACpC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,+IAAA,CAAA,UAAY;AACzB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,sIAAA,CAAA,UAAG;AAChB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,2IAAA,CAAA,UAAQ;AACrB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,oJAAA,CAAA,UAAiB;AAMvB,MAAM,YAAY;IACvB;;GAEC,GACD,SAAS,CAAC;QACR,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,OAAO;IAC5B;IAEA;;GAEC,GACD,QAAQ,CAAC,MAAqB,SAAS,qBAAqB;QAC1D,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;IAC5B;IAEA;;GAEC,GACD,eAAe,CAAC;QACd,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;IAC5B;IAEA;;GAEC,GACD,gBAAgB,CAAC;QACf,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;IAC5B;IAEA;;GAEC,GACD,SAAS,CAAC;QACR,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,KAAK;IACrC;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,IAAI,QAAQ,CAAC,GAAG,QAAQ;IACxD;IAEA;;GAEC,GACD,YAAY,CAAC;QACX,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,OAAO,CAAC;IAC7B;IAEA;;GAEC,GACD,UAAU,CAAC;QACT,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,KAAK,CAAC;IAC3B;IAEA;;GAEC,GACD,aAAa,CAAC;QACZ,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,WAAW;IAChC;IAEA;;GAEC,GACD,cAAc,CAAC;QACb,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE;IACf;IAEA;;GAEC,GACD,KAAK;QACH,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD;IACb;IAEA;;GAEC,GACD,KAAK,CAAC,MAAqB,QAAgB;QACzC,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,GAAG,CAAC,QAAQ;IACjC;IAEA;;GAEC,GACD,UAAU,CAAC,MAAqB,QAAgB;QAC9C,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,QAAQ,CAAC,QAAQ;IACtC;IAEA;;GAEC,GACD,UAAU,CAAC,OAAsB;QAC/B,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,OAAO,QAAQ,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE;IACrC;IAEA;;GAEC,GACD,SAAS,CAAC,OAAsB;QAC9B,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,OAAO,OAAO,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE;IACpC;IAEA;;GAEC,GACD,MAAM,CAAC,OAAsB,OAAsB;QACjD,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,OAAO,IAAI,CAAC,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,QAAQ;IACzC;AACF;AAMO,MAAM,eAAe;IAC1B,SAAS;IACT,mBAAmB;IACnB,KAAK;IACL,eAAe;IACf,WAAW;IACX,YAAY;IACZ,MAAM;AACR;;uCAOe"}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/auth/types/index.ts"], "sourcesContent": ["// APISportsGame CMS - Auth Module Types\n// Authentication related types\n\nimport { BaseEntity } from '@/shared/types/common';\n\n// ============================================================================\n// USER TYPES\n// ============================================================================\n\nexport interface SystemUser extends BaseEntity {\n  username: string;\n  email: string;\n  role: 'admin' | 'editor' | 'moderator';\n  isActive: boolean;\n  lastLoginAt: Date;\n}\n\nexport interface RegisteredUser extends BaseEntity {\n  username: string;\n  email: string;\n  tier: 'free' | 'premium' | 'enterprise';\n  isActive: boolean;\n  isEmailVerified: boolean;\n  apiCallsUsed: number;\n  apiCallsLimit: number | null;\n  subscriptionEndDate: Date | null;\n  lastLoginAt: Date;\n}\n\nexport type User = SystemUser | RegisteredUser;\n\n// ============================================================================\n// ROLE & TIER TYPES\n// ============================================================================\n\nexport type SystemRole = 'admin' | 'editor' | 'moderator';\nexport type RegisteredUserTier = 'free' | 'premium' | 'enterprise';\n\n// ============================================================================\n// JWT TOKEN TYPES\n// ============================================================================\n\nexport interface SystemUserToken {\n  sub: number;\n  username: string;\n  email: string;\n  role: SystemRole;\n  userType: 'system';\n  iat?: number;\n  exp?: number;\n}\n\nexport interface RegisteredUserToken {\n  sub: number;\n  username: string;\n  email: string;\n  tier: RegisteredUserTier;\n  userType: 'registered';\n  isEmailVerified: boolean;\n  iat?: number;\n  exp?: number;\n}\n\nexport type UserToken = SystemUserToken | RegisteredUserToken;\n\n// ============================================================================\n// AUTH REQUEST/RESPONSE TYPES\n// ============================================================================\n\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  access_token: string;\n  refresh_token: string;\n  user: SystemUser | RegisteredUser;\n}\n\nexport interface RegisterRequest {\n  username: string;\n  email: string;\n  password: string;\n}\n\nexport interface RefreshTokenRequest {\n  refresh_token: string;\n}\n\nexport interface RefreshTokenResponse {\n  access_token: string;\n  refresh_token: string;\n}\n\n// ============================================================================\n// AUTH STATE TYPES\n// ============================================================================\n\nexport interface AuthState {\n  user: SystemUser | RegisteredUser | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n}\n\nexport interface AuthActions {\n  login: (credentials: LoginRequest) => Promise<void>;\n  logout: () => void;\n  getProfile: () => Promise<void>;\n  refreshToken: () => Promise<void>;\n  clearError: () => void;\n}\n\n// ============================================================================\n// PERMISSION TYPES\n// ============================================================================\n\nexport interface PermissionState {\n  // User type checks\n  isSystemUser: boolean;\n  isRegisteredUser: boolean;\n\n  // Role checks (SystemUser)\n  isAdmin: boolean;\n  isEditor: boolean;\n  isModerator: boolean;\n\n  // Tier checks (RegisteredUser)\n  isFree: boolean;\n  isPremium: boolean;\n  isEnterprise: boolean;\n\n  // Permission checks\n  canManageUsers: boolean;\n  canManageLeagues: boolean;\n  canManageTeams: boolean;\n  canManageFixtures: boolean;\n  canSync: boolean;\n  canViewAnalytics: boolean;\n\n  // Current user\n  currentUser: SystemUser | RegisteredUser | null;\n}\n\n// ============================================================================\n// AUTH HOOK TYPES\n// ============================================================================\n\nexport interface UseAuthReturn extends AuthState, AuthActions { }\n\nexport interface UsePermissionsReturn extends PermissionState { }\n\n// ============================================================================\n// TYPE GUARDS\n// ============================================================================\n\nexport const isSystemUser = (user: SystemUser | RegisteredUser | null): user is SystemUser => {\n  return user !== null && 'role' in user;\n};\n\nexport const isRegisteredUser = (user: SystemUser | RegisteredUser | null): user is RegisteredUser => {\n  return user !== null && 'tier' in user;\n};\n\nexport const isSystemUserToken = (token: UserToken): token is SystemUserToken => {\n  return token.userType === 'system';\n};\n\nexport const isRegisteredUserToken = (token: UserToken): token is RegisteredUserToken => {\n  return token.userType === 'registered';\n};\n\n// ============================================================================\n// EXPORT ALL\n// ============================================================================\n\n// Remove circular export\n"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,+BAA+B;;;;;;;AA4JxB,MAAM,eAAe,CAAC;IAC3B,OAAO,SAAS,QAAQ,UAAU;AACpC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,SAAS,QAAQ,UAAU;AACpC;AAEO,MAAM,oBAAoB,CAAC;IAChC,OAAO,MAAM,QAAQ,KAAK;AAC5B;AAEO,MAAM,wBAAwB,CAAC;IACpC,OAAO,MAAM,QAAQ,KAAK;AAC5B,GAEA,+EAA+E;CAC/E,aAAa;CACb,+EAA+E;CAE/E,yBAAyB"}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/users/components/user-table.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - User Table Component\n// Reusable table cho SystemUser và RegisteredUser management\n\nimport React, { useState } from 'react';\nimport {\n  Table,\n  Tag,\n  Space,\n  Button,\n  Dropdown,\n  Avatar,\n  Typography,\n  Tooltip,\n  Modal,\n  message,\n  Input,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Card\n} from 'antd';\nimport {\n  UserOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  MoreOutlined,\n  SearchOutlined,\n  FilterOutlined,\n  PlusOutlined,\n  ExportOutlined,\n  ReloadOutlined\n} from '@ant-design/icons';\nimport { User, UserFilters, UserTableProps } from '../types';\nimport { dateUtils } from '@/shared/utils/date';\nimport { isSystemUser, isRegisteredUser } from '@/modules/auth/types';\n\nconst { Text } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\n\n// UserTableProps is now imported from types\n\n// ============================================================================\n// USER TABLE COMPONENT\n// ============================================================================\n\nexport function UserTable({\n  users,\n  loading = false,\n  userType,\n  onEdit,\n  onDelete,\n  onAdd,\n  onRefresh,\n  onExport,\n  pagination,\n  filters = {},\n  onFiltersChange\n}: UserTableProps) {\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [showFilters, setShowFilters] = useState(false);\n\n  // ========================================================================\n  // HELPER FUNCTIONS\n  // ========================================================================\n\n  // Type guards are now imported from auth types\n\n  // ========================================================================\n  // COLUMN DEFINITIONS\n  // ========================================================================\n\n  const getColumns = () => {\n    const baseColumns = [\n      {\n        title: 'User',\n        dataIndex: 'username',\n        key: 'username',\n        render: (username: string, record: User) => (\n          <Space>\n            <Avatar size=\"small\" icon={<UserOutlined />} />\n            <div>\n              <div className=\"font-medium\">{username}</div>\n              <Text type=\"secondary\" className=\"text-xs\">\n                {record.email}\n              </Text>\n            </div>\n          </Space>\n        ),\n        sorter: true,\n      },\n      {\n        title: userType === 'system' ? 'Role' : 'Tier',\n        dataIndex: userType === 'system' ? 'role' : 'tier',\n        key: userType === 'system' ? 'role' : 'tier',\n        render: (value: string) => {\n          const colors = {\n            // System roles\n            admin: 'red',\n            editor: 'blue',\n            moderator: 'green',\n            // User tiers\n            free: 'default',\n            premium: 'gold',\n            enterprise: 'purple',\n          };\n          return (\n            <Tag color={colors[value as keyof typeof colors] || 'default'}>\n              {value.toUpperCase()}\n            </Tag>\n          );\n        },\n        filters: userType === 'system'\n          ? [\n            { text: 'Admin', value: 'admin' },\n            { text: 'Editor', value: 'editor' },\n            { text: 'Moderator', value: 'moderator' },\n          ]\n          : [\n            { text: 'Free', value: 'free' },\n            { text: 'Premium', value: 'premium' },\n            { text: 'Enterprise', value: 'enterprise' },\n          ],\n      },\n      {\n        title: 'Status',\n        dataIndex: 'isActive',\n        key: 'isActive',\n        render: (isActive: boolean, record: User) => (\n          <Space direction=\"vertical\" size=\"small\">\n            <Tag color={isActive ? 'success' : 'error'}>\n              {isActive ? 'Active' : 'Inactive'}\n            </Tag>\n            {isRegisteredUser(record) && (\n              <Tag color={record.isEmailVerified ? 'success' : 'warning'} className=\"text-xs\">\n                {record.isEmailVerified ? 'Verified' : 'Unverified'}\n              </Tag>\n            )}\n          </Space>\n        ),\n        filters: [\n          { text: 'Active', value: true },\n          { text: 'Inactive', value: false },\n        ],\n      }\n    ];\n\n    // Add API usage column for RegisteredUser\n    if (userType === 'registered') {\n      baseColumns.push({\n        title: 'API Usage',\n        key: 'apiUsage',\n        render: (_, record: User) => {\n          if (!isRegisteredUser(record)) return null;\n          const percentage = record.apiCallsLimit\n            ? (record.apiCallsUsed / record.apiCallsLimit) * 100\n            : 0;\n\n          return (\n            <div>\n              <div className=\"text-sm\">\n                {record.apiCallsUsed.toLocaleString()} / {record.apiCallsLimit?.toLocaleString() || '∞'}\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-1 mt-1\">\n                <div\n                  className={`h-1 rounded-full ${percentage > 90 ? 'bg-red-500' :\n                    percentage > 70 ? 'bg-yellow-500' : 'bg-green-500'\n                    }`}\n                  style={{ width: `${Math.min(percentage, 100)}%` }}\n                />\n              </div>\n            </div>\n          );\n        },\n      });\n    }\n\n    // Add common columns\n    baseColumns.push(\n      {\n        title: 'Last Login',\n        dataIndex: 'lastLoginAt',\n        key: 'lastLoginAt',\n        render: (date: string) => (\n          <Tooltip title={dateUtils.format(date)}>\n            <Text type=\"secondary\" className=\"text-sm\">\n              {dateUtils.fromNow(date)}\n            </Text>\n          </Tooltip>\n        ),\n        sorter: true,\n      },\n      {\n        title: 'Created',\n        dataIndex: 'createdAt',\n        key: 'createdAt',\n        render: (date: string) => (\n          <Text type=\"secondary\" className=\"text-sm\">\n            {dateUtils.formatDisplay(date)}\n          </Text>\n        ),\n        sorter: true,\n      },\n      {\n        title: 'Actions',\n        key: 'actions',\n        width: 120,\n        render: (_, record: User) => (\n          <Space>\n            <Tooltip title=\"Edit\">\n              <Button\n                type=\"text\"\n                size=\"small\"\n                icon={<EditOutlined />}\n                onClick={() => onEdit?.(record)}\n              />\n            </Tooltip>\n            <Dropdown\n              menu={{\n                items: [\n                  {\n                    key: 'edit',\n                    label: 'Edit User',\n                    icon: <EditOutlined />,\n                    onClick: () => onEdit?.(record),\n                  },\n                  {\n                    key: 'delete',\n                    label: 'Delete User',\n                    icon: <DeleteOutlined />,\n                    danger: true,\n                    onClick: () => handleDelete(record),\n                  },\n                ],\n              }}\n              trigger={['click']}\n            >\n              <Button type=\"text\" size=\"small\" icon={<MoreOutlined />} />\n            </Dropdown>\n          </Space>\n        ),\n      }\n    );\n\n    return baseColumns;\n  };\n\n  // ========================================================================\n  // EVENT HANDLERS\n  // ========================================================================\n\n  const handleDelete = (user: User) => {\n    Modal.confirm({\n      title: 'Delete User',\n      content: `Are you sure you want to delete user \"${user.username}\"?`,\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: () => {\n        onDelete?.(user);\n        message.success('User deleted successfully');\n      },\n    });\n  };\n\n  const handleBulkDelete = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('Please select users to delete');\n      return;\n    }\n\n    Modal.confirm({\n      title: 'Delete Selected Users',\n      content: `Are you sure you want to delete ${selectedRowKeys.length} selected users?`,\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: () => {\n        // Handle bulk delete\n        setSelectedRowKeys([]);\n        message.success(`${selectedRowKeys.length} users deleted successfully`);\n      },\n    });\n  };\n\n  const handleFilterChange = (key: keyof UserFilters, value: any) => {\n    const newFilters = { ...filters, [key]: value };\n    onFiltersChange?.(newFilters);\n  };\n\n  // ========================================================================\n  // ROW SELECTION\n  // ========================================================================\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: setSelectedRowKeys,\n    getCheckboxProps: (record: User) => ({\n      disabled: false,\n      name: record.username,\n    }),\n  };\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Header Actions */}\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Search\n                placeholder={`Search ${userType} users...`}\n                allowClear\n                style={{ width: 300 }}\n                value={filters.search}\n                onChange={(e) => handleFilterChange('search', e.target.value)}\n                onSearch={(value) => handleFilterChange('search', value)}\n              />\n              <Button\n                icon={<FilterOutlined />}\n                onClick={() => setShowFilters(!showFilters)}\n              >\n                Filters\n              </Button>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              {selectedRowKeys.length > 0 && (\n                <Button\n                  danger\n                  icon={<DeleteOutlined />}\n                  onClick={handleBulkDelete}\n                >\n                  Delete ({selectedRowKeys.length})\n                </Button>\n              )}\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={onRefresh}\n                loading={loading}\n              >\n                Refresh\n              </Button>\n              <Button\n                icon={<ExportOutlined />}\n                onClick={onExport}\n              >\n                Export\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={onAdd}\n              >\n                Add {userType === 'system' ? 'System User' : 'User'}\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        {/* Advanced Filters */}\n        {showFilters && (\n          <div className=\"mt-4 p-4 bg-gray-50 rounded\">\n            <Row gutter={16}>\n              <Col span={6}>\n                <Select\n                  placeholder={userType === 'system' ? 'Select Role' : 'Select Tier'}\n                  allowClear\n                  style={{ width: '100%' }}\n                  value={userType === 'system' ? filters.role : filters.tier}\n                  onChange={(value) =>\n                    handleFilterChange(userType === 'system' ? 'role' : 'tier', value)\n                  }\n                >\n                  {userType === 'system' ? (\n                    <>\n                      <Option value=\"admin\">Admin</Option>\n                      <Option value=\"editor\">Editor</Option>\n                      <Option value=\"moderator\">Moderator</Option>\n                    </>\n                  ) : (\n                    <>\n                      <Option value=\"free\">Free</Option>\n                      <Option value=\"premium\">Premium</Option>\n                      <Option value=\"enterprise\">Enterprise</Option>\n                    </>\n                  )}\n                </Select>\n              </Col>\n              <Col span={6}>\n                <Select\n                  placeholder=\"Select Status\"\n                  allowClear\n                  style={{ width: '100%' }}\n                  value={filters.isActive}\n                  onChange={(value) => handleFilterChange('isActive', value)}\n                >\n                  <Option value={true}>Active</Option>\n                  <Option value={false}>Inactive</Option>\n                </Select>\n              </Col>\n              <Col span={6}>\n                <DatePicker\n                  placeholder=\"Created From\"\n                  style={{ width: '100%' }}\n                  onChange={(date) => handleFilterChange('createdFrom', date?.toISOString())}\n                />\n              </Col>\n              <Col span={6}>\n                <DatePicker\n                  placeholder=\"Created To\"\n                  style={{ width: '100%' }}\n                  onChange={(date) => handleFilterChange('createdTo', date?.toISOString())}\n                />\n              </Col>\n            </Row>\n          </div>\n        )}\n      </Card>\n\n      {/* Data Table */}\n      <Card>\n        <Table\n          rowSelection={rowSelection}\n          columns={getColumns()}\n          dataSource={users}\n          loading={loading}\n          pagination={pagination ? {\n            current: pagination.current,\n            pageSize: pagination.pageSize,\n            total: pagination.total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `${range[0]}-${range[1]} of ${total} users`,\n            onChange: pagination.onChange,\n          } : false}\n          rowKey=\"id\"\n          scroll={{ x: 1200 }}\n          size=\"middle\"\n        />\n      </Card>\n    </div>\n  );\n}\n\nexport default UserTable;\n"], "names": [], "mappings": ";;;;;AAEA,2CAA2C;AAC3C,6DAA6D;AAE7D;AA+BA;AACA;AA/BA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAlBA;AAAA;AA8BA;AA9BA;AAkBA;AAlBA;AAkBA;AAAA;AAlBA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAAA;AAAA;AAAA;AAlBA;AAAA;AANA;;;;;;;AAuCA,MAAM,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,gLAAA,CAAA,QAAK;AACxB,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AAQlB,SAAS,UAAU,EACxB,KAAK,EACL,UAAU,KAAK,EACf,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACL,SAAS,EACT,QAAQ,EACR,UAAU,EACV,UAAU,CAAC,CAAC,EACZ,eAAe,EACA;IACf,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAE3E,+CAA+C;IAE/C,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,aAAa;QACjB,MAAM,cAAc;YAClB;gBACE,OAAO;gBACP,WAAW;gBACX,KAAK;gBACL,QAAQ,CAAC,UAAkB,uBACzB,8OAAC,gMAAA,CAAA,QAAK;;0CACJ,8OAAC,kLAAA,CAAA,SAAM;gCAAC,MAAK;gCAAQ,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;0CACxC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAe;;;;;;kDAC9B,8OAAC;wCAAK,MAAK;wCAAY,WAAU;kDAC9B,OAAO,KAAK;;;;;;;;;;;;;;;;;;gBAKrB,QAAQ;YACV;YACA;gBACE,OAAO,aAAa,WAAW,SAAS;gBACxC,WAAW,aAAa,WAAW,SAAS;gBAC5C,KAAK,aAAa,WAAW,SAAS;gBACtC,QAAQ,CAAC;oBACP,MAAM,SAAS;wBACb,eAAe;wBACf,OAAO;wBACP,QAAQ;wBACR,WAAW;wBACX,aAAa;wBACb,MAAM;wBACN,SAAS;wBACT,YAAY;oBACd;oBACA,qBACE,8OAAC,4KAAA,CAAA,MAAG;wBAAC,OAAO,MAAM,CAAC,MAA6B,IAAI;kCACjD,MAAM,WAAW;;;;;;gBAGxB;gBACA,SAAS,aAAa,WAClB;oBACA;wBAAE,MAAM;wBAAS,OAAO;oBAAQ;oBAChC;wBAAE,MAAM;wBAAU,OAAO;oBAAS;oBAClC;wBAAE,MAAM;wBAAa,OAAO;oBAAY;iBACzC,GACC;oBACA;wBAAE,MAAM;wBAAQ,OAAO;oBAAO;oBAC9B;wBAAE,MAAM;wBAAW,OAAO;oBAAU;oBACpC;wBAAE,MAAM;wBAAc,OAAO;oBAAa;iBAC3C;YACL;YACA;gBACE,OAAO;gBACP,WAAW;gBACX,KAAK;gBACL,QAAQ,CAAC,UAAmB,uBAC1B,8OAAC,gMAAA,CAAA,QAAK;wBAAC,WAAU;wBAAW,MAAK;;0CAC/B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,OAAO,WAAW,YAAY;0CAChC,WAAW,WAAW;;;;;;4BAExB,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE,yBAChB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,OAAO,OAAO,eAAe,GAAG,YAAY;gCAAW,WAAU;0CACnE,OAAO,eAAe,GAAG,aAAa;;;;;;;;;;;;gBAK/C,SAAS;oBACP;wBAAE,MAAM;wBAAU,OAAO;oBAAK;oBAC9B;wBAAE,MAAM;wBAAY,OAAO;oBAAM;iBAClC;YACH;SACD;QAED,0CAA0C;QAC1C,IAAI,aAAa,cAAc;YAC7B,YAAY,IAAI,CAAC;gBACf,OAAO;gBACP,KAAK;gBACL,QAAQ,CAAC,GAAG;oBACV,IAAI,CAAC,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,OAAO;oBACtC,MAAM,aAAa,OAAO,aAAa,GACnC,AAAC,OAAO,YAAY,GAAG,OAAO,aAAa,GAAI,MAC/C;oBAEJ,qBACE,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,YAAY,CAAC,cAAc;oCAAG;oCAAI,OAAO,aAAa,EAAE,oBAAoB;;;;;;;0CAEtF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAW,CAAC,iBAAiB,EAAE,aAAa,KAAK,eAC/C,aAAa,KAAK,kBAAkB,gBAClC;oCACJ,OAAO;wCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC;oCAAC;;;;;;;;;;;;;;;;;gBAK1D;YACF;QACF;QAEA,qBAAqB;QACrB,YAAY,IAAI,CACd;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,qBACP,8OAAC,oLAAA,CAAA,UAAO;oBAAC,OAAO,8IAAA,CAAA,YAAS,CAAC,MAAM,CAAC;8BAC/B,cAAA,8OAAC;wBAAK,MAAK;wBAAY,WAAU;kCAC9B,8IAAA,CAAA,YAAS,CAAC,OAAO,CAAC;;;;;;;;;;;YAIzB,QAAQ;QACV,GACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,qBACP,8OAAC;oBAAK,MAAK;oBAAY,WAAU;8BAC9B,8IAAA,CAAA,YAAS,CAAC,aAAa,CAAC;;;;;;YAG7B,QAAQ;QACV,GACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,8OAAC,gMAAA,CAAA,QAAK;;sCACJ,8OAAC,oLAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gCACnB,SAAS,IAAM,SAAS;;;;;;;;;;;sCAG5B,8OAAC,sLAAA,CAAA,WAAQ;4BACP,MAAM;gCACJ,OAAO;oCACL;wCACE,KAAK;wCACL,OAAO;wCACP,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,SAAS,IAAM,SAAS;oCAC1B;oCACA;wCACE,KAAK;wCACL,OAAO;wCACP,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,QAAQ;wCACR,SAAS,IAAM,aAAa;oCAC9B;iCACD;4BACH;4BACA,SAAS;gCAAC;6BAAQ;sCAElB,cAAA,8OAAC,kMAAA,CAAA,SAAM;gCAAC,MAAK;gCAAO,MAAK;gCAAQ,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;QAI5D;QAGF,OAAO;IACT;IAEA,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,eAAe,CAAC;QACpB,gLAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,sCAAsC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;YACnE,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,WAAW;gBACX,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,gLAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,gCAAgC,EAAE,gBAAgB,MAAM,CAAC,gBAAgB,CAAC;YACpF,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,qBAAqB;gBACrB,mBAAmB,EAAE;gBACrB,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,gBAAgB,MAAM,CAAC,2BAA2B,CAAC;YACxE;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC,KAAwB;QAClD,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,CAAC,IAAI,EAAE;QAAM;QAC9C,kBAAkB;IACpB;IAEA,2EAA2E;IAC3E,gBAAgB;IAChB,2EAA2E;IAE3E,MAAM,eAAe;QACnB;QACA,UAAU;QACV,kBAAkB,CAAC,SAAiB,CAAC;gBACnC,UAAU;gBACV,MAAM,OAAO,QAAQ;YACvB,CAAC;IACH;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,8KAAA,CAAA,OAAI;;kCACH,8OAAC,4KAAA,CAAA,MAAG;wBAAC,SAAQ;wBAAgB,OAAM;;0CACjC,8OAAC,4KAAA,CAAA,MAAG;0CACF,cAAA,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC;4CACC,aAAa,CAAC,OAAO,EAAE,SAAS,SAAS,CAAC;4CAC1C,UAAU;4CACV,OAAO;gDAAE,OAAO;4CAAI;4CACpB,OAAO,QAAQ,MAAM;4CACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;4CAC5D,UAAU,CAAC,QAAU,mBAAmB,UAAU;;;;;;sDAEpD,8OAAC,kMAAA,CAAA,SAAM;4CACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS,IAAM,eAAe,CAAC;sDAChC;;;;;;;;;;;;;;;;;0CAKL,8OAAC,4KAAA,CAAA,MAAG;0CACF,cAAA,8OAAC,gMAAA,CAAA,QAAK;;wCACH,gBAAgB,MAAM,GAAG,mBACxB,8OAAC,kMAAA,CAAA,SAAM;4CACL,MAAM;4CACN,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS;;gDACV;gDACU,gBAAgB,MAAM;gDAAC;;;;;;;sDAGpC,8OAAC,kMAAA,CAAA,SAAM;4CACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS;4CACT,SAAS;sDACV;;;;;;sDAGD,8OAAC,kMAAA,CAAA,SAAM;4CACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS;sDACV;;;;;;sDAGD,8OAAC,kMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4CACnB,SAAS;;gDACV;gDACM,aAAa,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;oBAOpD,6BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAa,aAAa,WAAW,gBAAgB;wCACrD,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAO;wCACvB,OAAO,aAAa,WAAW,QAAQ,IAAI,GAAG,QAAQ,IAAI;wCAC1D,UAAU,CAAC,QACT,mBAAmB,aAAa,WAAW,SAAS,QAAQ;kDAG7D,aAAa,yBACZ;;8DACE,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;;yEAG5B;;8DACE,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAa;;;;;;;;;;;;;;;;;;8CAKnC,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAO;wCACvB,OAAO,QAAQ,QAAQ;wCACvB,UAAU,CAAC,QAAU,mBAAmB,YAAY;;0DAEpD,8OAAC;gDAAO,OAAO;0DAAM;;;;;;0DACrB,8OAAC;gDAAO,OAAO;0DAAO;;;;;;;;;;;;;;;;;8CAG1B,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,8LAAA,CAAA,aAAU;wCACT,aAAY;wCACZ,OAAO;4CAAE,OAAO;wCAAO;wCACvB,UAAU,CAAC,OAAS,mBAAmB,eAAe,MAAM;;;;;;;;;;;8CAGhE,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,8LAAA,CAAA,aAAU;wCACT,aAAY;wCACZ,OAAO;4CAAE,OAAO;wCAAO;wCACvB,UAAU,CAAC,OAAS,mBAAmB,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStE,8OAAC,8KAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,cAAc;oBACd,SAAS;oBACT,YAAY;oBACZ,SAAS;oBACT,YAAY,aAAa;wBACvB,SAAS,WAAW,OAAO;wBAC3B,UAAU,WAAW,QAAQ;wBAC7B,OAAO,WAAW,KAAK;wBACvB,iBAAiB;wBACjB,iBAAiB;wBACjB,WAAW,CAAC,OAAO,QACjB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC;wBAC7C,UAAU,WAAW,QAAQ;oBAC/B,IAAI;oBACJ,QAAO;oBACP,QAAQ;wBAAE,GAAG;oBAAK;oBAClB,MAAK;;;;;;;;;;;;;;;;;AAKf;uCAEe"}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/modules/users/components/user-form.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - User Form Component\n// Reusable form cho create/edit SystemUser và RegisteredUser\n\nimport React, { useEffect } from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Switch,\n  Button,\n  Space,\n  Card,\n  Row,\n  Col,\n  Typography,\n  Divider,\n  InputNumber,\n  DatePicker,\n  Alert\n} from 'antd';\nimport {\n  UserOutlined,\n  MailOutlined,\n  LockOutlined,\n  SaveOutlined,\n  CloseOutlined\n} from '@ant-design/icons';\nimport { User, UserFormProps } from '../types';\nimport { dateUtils } from '@/shared/utils/date';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\n// UserFormProps is now imported from types\n\n// ============================================================================\n// USER FORM COMPONENT\n// ============================================================================\n\nexport function UserForm({\n  userType,\n  mode,\n  initialValues,\n  loading = false,\n  onSubmit,\n  onCancel\n}: UserFormProps) {\n  const [form] = Form.useForm();\n\n  // ========================================================================\n  // EFFECTS\n  // ========================================================================\n\n  useEffect(() => {\n    if (initialValues) {\n      // Convert dates for form\n      const formValues = {\n        ...initialValues,\n        subscriptionEndDate: dateUtils.parseForForm(initialValues.subscriptionEndDate),\n      };\n      form.setFieldsValue(formValues);\n    }\n  }, [initialValues, form]);\n\n  // ========================================================================\n  // FORM VALIDATION RULES\n  // ========================================================================\n\n  const validationRules = {\n    username: [\n      { required: true, message: 'Username is required' },\n      { min: 3, message: 'Username must be at least 3 characters' },\n      { max: 50, message: 'Username must not exceed 50 characters' },\n      { pattern: /^[a-zA-Z0-9_]+$/, message: 'Username can only contain letters, numbers, and underscores' }\n    ],\n    email: [\n      { required: true, message: 'Email is required' },\n      { type: 'email' as const, message: 'Please enter a valid email' }\n    ],\n    password: mode === 'create' ? [\n      { required: true, message: 'Password is required' },\n      { min: 6, message: 'Password must be at least 6 characters' },\n      { max: 100, message: 'Password must not exceed 100 characters' }\n    ] : [\n      { min: 6, message: 'Password must be at least 6 characters' },\n      { max: 100, message: 'Password must not exceed 100 characters' }\n    ],\n    role: [\n      { required: true, message: 'Role is required' }\n    ],\n    tier: [\n      { required: true, message: 'Tier is required' }\n    ]\n  };\n\n  // ========================================================================\n  // EVENT HANDLERS\n  // ========================================================================\n\n  const handleSubmit = async (values: any) => {\n    try {\n      // Process form values\n      const processedValues = {\n        ...values,\n        subscriptionEndDate: values.subscriptionEndDate\n          ? dateUtils.toISOString(values.subscriptionEndDate)\n          : null,\n      };\n\n      // Remove password if empty in edit mode\n      if (mode === 'edit' && !processedValues.password) {\n        delete processedValues.password;\n      }\n\n      onSubmit(processedValues);\n    } catch (error) {\n      console.error('Form submission error:', error);\n    }\n  };\n\n  const handleReset = () => {\n    form.resetFields();\n  };\n\n  // ========================================================================\n  // RENDER HELPERS\n  // ========================================================================\n\n  const renderSystemUserFields = () => (\n    <>\n      <Col span={24}>\n        <Form.Item\n          name=\"role\"\n          label=\"Role\"\n          rules={validationRules.role}\n        >\n          <Select placeholder=\"Select user role\">\n            <Option value=\"admin\">\n              <Space>\n                <span>👑</span>\n                <div>\n                  <div>Admin</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Full system access\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n            <Option value=\"editor\">\n              <Space>\n                <span>✏️</span>\n                <div>\n                  <div>Editor</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Content management\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n            <Option value=\"moderator\">\n              <Space>\n                <span>🛡️</span>\n                <div>\n                  <div>Moderator</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Content moderation\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n          </Select>\n        </Form.Item>\n      </Col>\n    </>\n  );\n\n  const renderRegisteredUserFields = () => (\n    <>\n      <Col span={12}>\n        <Form.Item\n          name=\"tier\"\n          label=\"Subscription Tier\"\n          rules={validationRules.tier}\n        >\n          <Select placeholder=\"Select subscription tier\">\n            <Option value=\"free\">\n              <Space>\n                <span>🆓</span>\n                <div>\n                  <div>Free</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Basic features\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n            <Option value=\"premium\">\n              <Space>\n                <span>⭐</span>\n                <div>\n                  <div>Premium</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Enhanced features\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n            <Option value=\"enterprise\">\n              <Space>\n                <span>🏢</span>\n                <div>\n                  <div>Enterprise</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Full features\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n          </Select>\n        </Form.Item>\n      </Col>\n\n      <Col span={12}>\n        <Form.Item\n          name=\"isEmailVerified\"\n          label=\"Email Verified\"\n          valuePropName=\"checked\"\n        >\n          <Switch />\n        </Form.Item>\n      </Col>\n\n      <Col span={12}>\n        <Form.Item\n          name=\"apiCallsUsed\"\n          label=\"API Calls Used\"\n        >\n          <InputNumber\n            min={0}\n            style={{ width: '100%' }}\n            formatter={value => `${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n            parser={value => value!.replace(/\\$\\s?|(,*)/g, '')}\n          />\n        </Form.Item>\n      </Col>\n\n      <Col span={12}>\n        <Form.Item\n          name=\"apiCallsLimit\"\n          label=\"API Calls Limit\"\n        >\n          <InputNumber\n            min={0}\n            style={{ width: '100%' }}\n            formatter={value => `${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n            parser={value => value!.replace(/\\$\\s?|(,*)/g, '')}\n            placeholder=\"Leave empty for unlimited\"\n          />\n        </Form.Item>\n      </Col>\n\n      <Col span={24}>\n        <Form.Item\n          name=\"subscriptionEndDate\"\n          label=\"Subscription End Date\"\n        >\n          <DatePicker\n            style={{ width: '100%' }}\n            showTime\n            format=\"YYYY-MM-DD HH:mm:ss\"\n            placeholder=\"Select subscription end date\"\n          />\n        </Form.Item>\n      </Col>\n    </>\n  );\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <Title level={4}>\n          {mode === 'create' ? 'Create' : 'Edit'} {userType === 'system' ? 'System User' : 'Registered User'}\n        </Title>\n        <Text type=\"secondary\">\n          {mode === 'create'\n            ? `Add a new ${userType} user to the system`\n            : `Update ${userType} user information`\n          }\n        </Text>\n      </div>\n\n      {/* Info Alert */}\n      <Alert\n        message={`${userType === 'system' ? 'System User' : 'Registered User'} Information`}\n        description={\n          userType === 'system'\n            ? 'System users have administrative access to the CMS. Choose roles carefully.'\n            : 'Registered users are API consumers with different subscription tiers and usage limits.'\n        }\n        type=\"info\"\n        showIcon\n        className=\"mb-6\"\n      />\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleSubmit}\n        autoComplete=\"off\"\n        size=\"large\"\n      >\n        <Row gutter={16}>\n          {/* Basic Information */}\n          <Col span={24}>\n            <Divider orientation=\"left\">Basic Information</Divider>\n          </Col>\n\n          <Col span={12}>\n            <Form.Item\n              name=\"username\"\n              label=\"Username\"\n              rules={validationRules.username}\n            >\n              <Input\n                prefix={<UserOutlined />}\n                placeholder=\"Enter username\"\n                disabled={mode === 'edit'}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col span={12}>\n            <Form.Item\n              name=\"email\"\n              label=\"Email\"\n              rules={validationRules.email}\n            >\n              <Input\n                prefix={<MailOutlined />}\n                placeholder=\"Enter email address\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col span={24}>\n            <Form.Item\n              name=\"password\"\n              label={mode === 'create' ? 'Password' : 'New Password (leave empty to keep current)'}\n              rules={validationRules.password}\n            >\n              <Input.Password\n                prefix={<LockOutlined />}\n                placeholder={mode === 'create' ? 'Enter password' : 'Enter new password'}\n              />\n            </Form.Item>\n          </Col>\n\n          {/* Role/Tier Specific Fields */}\n          <Col span={24}>\n            <Divider orientation=\"left\">\n              {userType === 'system' ? 'Role & Permissions' : 'Subscription & Usage'}\n            </Divider>\n          </Col>\n\n          {userType === 'system' ? renderSystemUserFields() : renderRegisteredUserFields()}\n\n          {/* Status */}\n          <Col span={24}>\n            <Divider orientation=\"left\">Status</Divider>\n          </Col>\n\n          <Col span={12}>\n            <Form.Item\n              name=\"isActive\"\n              label=\"Active Status\"\n              valuePropName=\"checked\"\n              initialValue={true}\n            >\n              <Switch />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        {/* Form Actions */}\n        <Divider />\n        <Row justify=\"end\">\n          <Col>\n            <Space>\n              <Button onClick={handleReset}>\n                Reset\n              </Button>\n              <Button onClick={onCancel}>\n                <CloseOutlined />\n                Cancel\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                icon={<SaveOutlined />}\n              >\n                {mode === 'create' ? 'Create User' : 'Update User'}\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Form>\n    </Card>\n  );\n}\n\nexport default UserForm;\n"], "names": [], "mappings": ";;;;;AAEA,0CAA0C;AAC1C,6DAA6D;AAE7D;AAyBA;AAxBA;AAAA;AAAA;AAAA;AAwBA;AAxBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AAAA;AAhBA;AAgBA;AAAA;AAtBA;;;;;;AAgCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,gLAAA,CAAA,QAAK;AAQnB,SAAS,SAAS,EACvB,QAAQ,EACR,IAAI,EACJ,aAAa,EACb,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACM;IACd,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,2EAA2E;IAC3E,UAAU;IACV,2EAA2E;IAE3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe;YACjB,yBAAyB;YACzB,MAAM,aAAa;gBACjB,GAAG,aAAa;gBAChB,qBAAqB,8IAAA,CAAA,YAAS,CAAC,YAAY,CAAC,cAAc,mBAAmB;YAC/E;YACA,KAAK,cAAc,CAAC;QACtB;IACF,GAAG;QAAC;QAAe;KAAK;IAExB,2EAA2E;IAC3E,wBAAwB;IACxB,2EAA2E;IAE3E,MAAM,kBAAkB;QACtB,UAAU;YACR;gBAAE,UAAU;gBAAM,SAAS;YAAuB;YAClD;gBAAE,KAAK;gBAAG,SAAS;YAAyC;YAC5D;gBAAE,KAAK;gBAAI,SAAS;YAAyC;YAC7D;gBAAE,SAAS;gBAAmB,SAAS;YAA8D;SACtG;QACD,OAAO;YACL;gBAAE,UAAU;gBAAM,SAAS;YAAoB;YAC/C;gBAAE,MAAM;gBAAkB,SAAS;YAA6B;SACjE;QACD,UAAU,SAAS,WAAW;YAC5B;gBAAE,UAAU;gBAAM,SAAS;YAAuB;YAClD;gBAAE,KAAK;gBAAG,SAAS;YAAyC;YAC5D;gBAAE,KAAK;gBAAK,SAAS;YAA0C;SAChE,GAAG;YACF;gBAAE,KAAK;gBAAG,SAAS;YAAyC;YAC5D;gBAAE,KAAK;gBAAK,SAAS;YAA0C;SAChE;QACD,MAAM;YACJ;gBAAE,UAAU;gBAAM,SAAS;YAAmB;SAC/C;QACD,MAAM;YACJ;gBAAE,UAAU;gBAAM,SAAS;YAAmB;SAC/C;IACH;IAEA,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,sBAAsB;YACtB,MAAM,kBAAkB;gBACtB,GAAG,MAAM;gBACT,qBAAqB,OAAO,mBAAmB,GAC3C,8IAAA,CAAA,YAAS,CAAC,WAAW,CAAC,OAAO,mBAAmB,IAChD;YACN;YAEA,wCAAwC;YACxC,IAAI,SAAS,UAAU,CAAC,gBAAgB,QAAQ,EAAE;gBAChD,OAAO,gBAAgB,QAAQ;YACjC;YAEA,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,cAAc;QAClB,KAAK,WAAW;IAClB;IAEA,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,yBAAyB,kBAC7B;sBACE,cAAA,8OAAC,4KAAA,CAAA,MAAG;gBAAC,MAAM;0BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAM;oBACN,OAAO,gBAAgB,IAAI;8BAE3B,cAAA,8OAAC,kLAAA,CAAA,SAAM;wBAAC,aAAY;;0CAClB,8OAAC;gCAAO,OAAM;0CACZ,cAAA,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC;sDAAK;;;;;;sDACN,8OAAC;;8DACC,8OAAC;8DAAI;;;;;;8DACL,8OAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMjD,8OAAC;gCAAO,OAAM;0CACZ,cAAA,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC;sDAAK;;;;;;sDACN,8OAAC;;8DACC,8OAAC;8DAAI;;;;;;8DACL,8OAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMjD,8OAAC;gCAAO,OAAM;0CACZ,cAAA,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC;sDAAK;;;;;;sDACN,8OAAC;;8DACC,8OAAC;8DAAI;;;;;;8DACL,8OAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAY3D,MAAM,6BAA6B,kBACjC;;8BACE,8OAAC,4KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;wBACN,OAAO,gBAAgB,IAAI;kCAE3B,cAAA,8OAAC,kLAAA,CAAA,SAAM;4BAAC,aAAY;;8CAClB,8OAAC;oCAAO,OAAM;8CACZ,cAAA,8OAAC,gMAAA,CAAA,QAAK;;0DACJ,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMjD,8OAAC;oCAAO,OAAM;8CACZ,cAAA,8OAAC,gMAAA,CAAA,QAAK;;0DACJ,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMjD,8OAAC;oCAAO,OAAM;8CACZ,cAAA,8OAAC,gMAAA,CAAA,QAAK;;0DACJ,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUvD,8OAAC,4KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;wBACN,eAAc;kCAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;;;;;;;;;;;;;;;8BAIX,8OAAC,4KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;kCAEN,cAAA,8OAAC,gMAAA,CAAA,cAAW;4BACV,KAAK;4BACL,OAAO;gCAAE,OAAO;4BAAO;4BACvB,WAAW,CAAA,QAAS,GAAG,OAAO,CAAC,OAAO,CAAC,yBAAyB;4BAChE,QAAQ,CAAA,QAAS,MAAO,OAAO,CAAC,eAAe;;;;;;;;;;;;;;;;8BAKrD,8OAAC,4KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;kCAEN,cAAA,8OAAC,gMAAA,CAAA,cAAW;4BACV,KAAK;4BACL,OAAO;gCAAE,OAAO;4BAAO;4BACvB,WAAW,CAAA,QAAS,GAAG,OAAO,CAAC,OAAO,CAAC,yBAAyB;4BAChE,QAAQ,CAAA,QAAS,MAAO,OAAO,CAAC,eAAe;4BAC/C,aAAY;;;;;;;;;;;;;;;;8BAKlB,8OAAC,4KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;kCAEN,cAAA,8OAAC,8LAAA,CAAA,aAAU;4BACT,OAAO;gCAAE,OAAO;4BAAO;4BACvB,QAAQ;4BACR,QAAO;4BACP,aAAY;;;;;;;;;;;;;;;;;;IAOtB,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,8OAAC,8KAAA,CAAA,OAAI;;0BACH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;;4BACX,SAAS,WAAW,WAAW;4BAAO;4BAAE,aAAa,WAAW,gBAAgB;;;;;;;kCAEnF,8OAAC;wBAAK,MAAK;kCACR,SAAS,WACN,CAAC,UAAU,EAAE,SAAS,mBAAmB,CAAC,GAC1C,CAAC,OAAO,EAAE,SAAS,iBAAiB,CAAC;;;;;;;;;;;;0BAM7C,8OAAC,gLAAA,CAAA,QAAK;gBACJ,SAAS,GAAG,aAAa,WAAW,gBAAgB,kBAAkB,YAAY,CAAC;gBACnF,aACE,aAAa,WACT,gFACA;gBAEN,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;0BAGZ,8OAAC,8KAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,UAAU;gBACV,cAAa;gBACb,MAAK;;kCAEL,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CAEX,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,oLAAA,CAAA,UAAO;oCAAC,aAAY;8CAAO;;;;;;;;;;;0CAG9B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO,gBAAgB,QAAQ;8CAE/B,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,UAAU,SAAS;;;;;;;;;;;;;;;;0CAKzB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO,gBAAgB,KAAK;8CAE5B,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCACJ,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;;;;;;;;;;;;;;;;0CAKlB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAO,SAAS,WAAW,aAAa;oCACxC,OAAO,gBAAgB,QAAQ;8CAE/B,cAAA,8OAAC,gLAAA,CAAA,QAAK,CAAC,QAAQ;wCACb,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACrB,aAAa,SAAS,WAAW,mBAAmB;;;;;;;;;;;;;;;;0CAM1D,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,oLAAA,CAAA,UAAO;oCAAC,aAAY;8CAClB,aAAa,WAAW,uBAAuB;;;;;;;;;;;4BAInD,aAAa,WAAW,2BAA2B;0CAGpD,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,oLAAA,CAAA,UAAO;oCAAC,aAAY;8CAAO;;;;;;;;;;;0CAG9B,8OAAC,4KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,eAAc;oCACd,cAAc;8CAEd,cAAA,8OAAC,kLAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;kCAMb,8OAAC,oLAAA,CAAA,UAAO;;;;;kCACR,8OAAC,4KAAA,CAAA,MAAG;wBAAC,SAAQ;kCACX,cAAA,8OAAC,4KAAA,CAAA,MAAG;sCACF,cAAA,8OAAC,gMAAA,CAAA,QAAK;;kDACJ,8OAAC,kMAAA,CAAA,SAAM;wCAAC,SAAS;kDAAa;;;;;;kDAG9B,8OAAC,kMAAA,CAAA,SAAM;wCAAC,SAAS;;0DACf,8OAAC,oNAAA,CAAA,gBAAa;;;;;4CAAG;;;;;;;kDAGnB,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAS;wCACT,SAAS;wCACT,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;kDAElB,SAAS,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;uCAEe"}}, {"offset": {"line": 1822, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1828, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/test-users/page.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Test User Management Page\n// Temporary page để test User Management components without authentication\n\nimport React, { useState } from 'react';\nimport {\n  Tabs,\n  Typography,\n  Space,\n  Card,\n  Statistic,\n  Row,\n  Col,\n  Modal,\n  message,\n  Button\n} from 'antd';\nimport {\n  UserOutlined,\n  TeamOutlined,\n  CrownOutlined,\n  ApiOutlined,\n  PlusOutlined\n} from '@ant-design/icons';\nimport { UserTable } from '@/modules/users/components/user-table';\nimport { UserForm } from '@/modules/users/components/user-form';\nimport { User, UserFilters } from '@/modules/users/types';\nimport { SystemUser, RegisteredUser } from '@/modules/auth/types';\n\nconst { Title, Text } = Typography;\n\n// ============================================================================\n// MOCK DATA\n// ============================================================================\n\nconst mockSystemUsers: SystemUser[] = [\n  {\n    id: 1,\n    username: 'admin',\n    email: '<EMAIL>',\n    role: 'admin',\n    isActive: true,\n    lastLoginAt: new Date('2024-01-15T10:30:00Z'),\n    createdAt: new Date('2024-01-01T00:00:00Z'),\n  },\n  {\n    id: 2,\n    username: 'editor1',\n    email: '<EMAIL>',\n    role: 'editor',\n    isActive: true,\n    lastLoginAt: new Date('2024-01-15T09:15:00Z'),\n    createdAt: new Date('2024-01-05T00:00:00Z'),\n  },\n  {\n    id: 3,\n    username: 'moderator1',\n    email: '<EMAIL>',\n    role: 'moderator',\n    isActive: false,\n    lastLoginAt: new Date('2024-01-10T14:20:00Z'),\n    createdAt: new Date('2024-01-10T00:00:00Z'),\n  },\n];\n\nconst mockRegisteredUsers: RegisteredUser[] = [\n  {\n    id: 1,\n    username: 'developer1',\n    email: '<EMAIL>',\n    tier: 'premium',\n    isActive: true,\n    isEmailVerified: true,\n    apiCallsUsed: 15000,\n    apiCallsLimit: 50000,\n    subscriptionEndDate: new Date('2024-12-31T23:59:59Z'),\n    lastLoginAt: new Date('2024-01-15T11:00:00Z'),\n    createdAt: new Date('2024-01-01T00:00:00Z'),\n  },\n  {\n    id: 2,\n    username: 'startup_user',\n    email: '<EMAIL>',\n    tier: 'enterprise',\n    isActive: true,\n    isEmailVerified: true,\n    apiCallsUsed: 75000,\n    apiCallsLimit: 200000,\n    subscriptionEndDate: new Date('2024-06-30T23:59:59Z'),\n    lastLoginAt: new Date('2024-01-15T08:30:00Z'),\n    createdAt: new Date('2023-12-15T00:00:00Z'),\n  },\n  {\n    id: 3,\n    username: 'free_user',\n    email: '<EMAIL>',\n    tier: 'free',\n    isActive: true,\n    isEmailVerified: false,\n    apiCallsUsed: 950,\n    apiCallsLimit: 1000,\n    subscriptionEndDate: null,\n    lastLoginAt: new Date('2024-01-14T16:45:00Z'),\n    createdAt: new Date('2024-01-14T00:00:00Z'),\n  },\n];\n\n// ============================================================================\n// TEST USER MANAGEMENT PAGE\n// ============================================================================\n\nexport default function TestUserManagementPage() {\n  // State management\n  const [activeTab, setActiveTab] = useState('system');\n  const [loading, setLoading] = useState(false);\n  const [showForm, setShowForm] = useState(false);\n  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\n\n  // Filters\n  const [systemUserFilters, setSystemUserFilters] = useState<UserFilters>({});\n  const [registeredUserFilters, setRegisteredUserFilters] = useState<UserFilters>({});\n\n  // Pagination\n  const [systemUserPagination, setSystemUserPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: mockSystemUsers.length,\n  });\n\n  const [registeredUserPagination, setRegisteredUserPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: mockRegisteredUsers.length,\n  });\n\n  // ========================================================================\n  // EVENT HANDLERS\n  // ========================================================================\n\n  const handleAddUser = (userType: 'system' | 'registered') => {\n    setFormMode('create');\n    setSelectedUser(null);\n    setActiveTab(userType);\n    setShowForm(true);\n  };\n\n  const handleEditUser = (user: User) => {\n    setFormMode('edit');\n    setSelectedUser(user);\n    setShowForm(true);\n  };\n\n  const handleDeleteUser = (user: User) => {\n    message.success(`User \"${user.username}\" deleted successfully`);\n  };\n\n  const handleFormSubmit = async (values: any) => {\n    setLoading(true);\n    try {\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      message.success(\n        `User ${formMode === 'create' ? 'created' : 'updated'} successfully`\n      );\n      setShowForm(false);\n      setSelectedUser(null);\n    } catch (error) {\n      message.error('Operation failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setLoading(false);\n      message.success('Data refreshed');\n    }, 1000);\n  };\n\n  const handleExport = () => {\n    message.info('Export functionality will be implemented');\n  };\n\n  // ========================================================================\n  // STATISTICS\n  // ========================================================================\n\n  const systemUserStats = {\n    total: mockSystemUsers.length,\n    active: mockSystemUsers.filter(u => u.isActive).length,\n    admins: mockSystemUsers.filter(u => u.role === 'admin').length,\n    editors: mockSystemUsers.filter(u => u.role === 'editor').length,\n  };\n\n  const registeredUserStats = {\n    total: mockRegisteredUsers.length,\n    active: mockRegisteredUsers.filter(u => u.isActive).length,\n    verified: mockRegisteredUsers.filter(u => u.isEmailVerified).length,\n    premium: mockRegisteredUsers.filter(u => u.tier === 'premium' || u.tier === 'enterprise').length,\n  };\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n        {/* Page Header */}\n        <Card>\n          <div className=\"text-center\">\n            <Title level={2} className=\"mb-2\">\n              🧪 User Management Test Page\n            </Title>\n            <Text type=\"secondary\" className=\"text-base\">\n              Testing User Management components without authentication\n            </Text>\n          </div>\n        </Card>\n\n        {/* Statistics Cards */}\n        <Row gutter={16}>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Total System Users\"\n                value={systemUserStats.total}\n                prefix={<CrownOutlined />}\n                valueStyle={{ color: '#1890ff' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Active System Users\"\n                value={systemUserStats.active}\n                prefix={<UserOutlined />}\n                valueStyle={{ color: '#52c41a' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Total Registered Users\"\n                value={registeredUserStats.total}\n                prefix={<TeamOutlined />}\n                valueStyle={{ color: '#722ed1' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Verified Users\"\n                value={registeredUserStats.verified}\n                prefix={<ApiOutlined />}\n                valueStyle={{ color: '#faad14' }}\n              />\n            </Card>\n          </Col>\n        </Row>\n\n        {/* User Management Tabs */}\n        <Card>\n          <Tabs\n            activeKey={activeTab}\n            onChange={setActiveTab}\n            tabBarExtraContent={\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => handleAddUser(activeTab as 'system' | 'registered')}\n              >\n                Add {activeTab === 'system' ? 'System User' : 'User'}\n              </Button>\n            }\n            items={[\n              {\n                key: 'system',\n                label: (\n                  <Space>\n                    <CrownOutlined />\n                    System Users ({systemUserStats.total})\n                  </Space>\n                ),\n                children: (\n                  <UserTable\n                    users={mockSystemUsers}\n                    loading={loading}\n                    userType=\"system\"\n                    onEdit={handleEditUser}\n                    onDelete={handleDeleteUser}\n                    onAdd={() => handleAddUser('system')}\n                    onRefresh={handleRefresh}\n                    onExport={handleExport}\n                    pagination={{\n                      ...systemUserPagination,\n                      onChange: (page, pageSize) => {\n                        setSystemUserPagination({ ...systemUserPagination, current: page, pageSize });\n                      },\n                    }}\n                    filters={systemUserFilters}\n                    onFiltersChange={setSystemUserFilters}\n                  />\n                ),\n              },\n              {\n                key: 'registered',\n                label: (\n                  <Space>\n                    <TeamOutlined />\n                    Registered Users ({registeredUserStats.total})\n                  </Space>\n                ),\n                children: (\n                  <UserTable\n                    users={mockRegisteredUsers}\n                    loading={loading}\n                    userType=\"registered\"\n                    onEdit={handleEditUser}\n                    onDelete={handleDeleteUser}\n                    onAdd={() => handleAddUser('registered')}\n                    onRefresh={handleRefresh}\n                    onExport={handleExport}\n                    pagination={{\n                      ...registeredUserPagination,\n                      onChange: (page, pageSize) => {\n                        setRegisteredUserPagination({ ...registeredUserPagination, current: page, pageSize });\n                      },\n                    }}\n                    filters={registeredUserFilters}\n                    onFiltersChange={setRegisteredUserFilters}\n                  />\n                ),\n              },\n            ]}\n          />\n        </Card>\n\n        {/* User Form Modal */}\n        <Modal\n          title={null}\n          open={showForm}\n          onCancel={() => setShowForm(false)}\n          footer={null}\n          width={800}\n          destroyOnHidden\n        >\n          <UserForm\n            userType={activeTab as 'system' | 'registered'}\n            mode={formMode}\n            initialValues={selectedUser || undefined}\n            loading={loading}\n            onSubmit={handleFormSubmit}\n            onCancel={() => setShowForm(false)}\n          />\n        </Modal>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,gDAAgD;AAChD,2EAA2E;AAE3E;AAoBA;AACA;AApBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAAA;AAAA;AAAA;AAZA;AAAA;AAYA;AAZA;AAAA;AANA;;;;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,+EAA+E;AAC/E,YAAY;AACZ,+EAA+E;AAE/E,MAAM,kBAAgC;IACpC;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;CACD;AAED,MAAM,sBAAwC;IAC5C;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,qBAAqB,IAAI,KAAK;QAC9B,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,qBAAqB,IAAI,KAAK;QAC9B,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,qBAAqB;QACrB,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;CACD;AAMc,SAAS;IACtB,mBAAmB;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,UAAU;IACV,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,CAAC;IACzE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,CAAC;IAEjF,aAAa;IACb,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/D,SAAS;QACT,UAAU;QACV,OAAO,gBAAgB,MAAM;IAC/B;IAEA,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvE,SAAS;QACT,UAAU;QACV,OAAO,oBAAoB,MAAM;IACnC;IAEA,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,gBAAgB,CAAC;QACrB,YAAY;QACZ,gBAAgB;QAChB,aAAa;QACb,YAAY;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY;QACZ,gBAAgB;QAChB,YAAY;IACd;IAEA,MAAM,mBAAmB,CAAC;QACxB,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,QAAQ,CAAC,sBAAsB,CAAC;IAChE;IAEA,MAAM,mBAAmB,OAAO;QAC9B,WAAW;QACX,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,oLAAA,CAAA,UAAO,CAAC,OAAO,CACb,CAAC,KAAK,EAAE,aAAa,WAAW,YAAY,UAAU,aAAa,CAAC;YAEtE,YAAY;YACZ,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,WAAW;QACX,WAAW;YACT,WAAW;YACX,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB,GAAG;IACL;IAEA,MAAM,eAAe;QACnB,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;IACf;IAEA,2EAA2E;IAC3E,aAAa;IACb,2EAA2E;IAE3E,MAAM,kBAAkB;QACtB,OAAO,gBAAgB,MAAM;QAC7B,QAAQ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;QACtD,QAAQ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;QAC9D,SAAS,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;IAClE;IAEA,MAAM,sBAAsB;QAC1B,OAAO,oBAAoB,MAAM;QACjC,QAAQ,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;QAC1D,UAAU,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,EAAE,MAAM;QACnE,SAAS,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,IAAI,KAAK,cAAc,MAAM;IAClG;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,8KAAA,CAAA,OAAI;8BACH,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,OAAO;gCAAG,WAAU;0CAAO;;;;;;0CAGlC,8OAAC;gCAAK,MAAK;gCAAY,WAAU;0CAAY;;;;;;;;;;;;;;;;;8BAOjD,8OAAC,4KAAA,CAAA,MAAG;oBAAC,QAAQ;;sCACX,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,gBAAgB,KAAK;oCAC5B,sBAAQ,8OAAC,oNAAA,CAAA,gBAAa;;;;;oCACtB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;sCAIrC,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,gBAAgB,MAAM;oCAC7B,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACrB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;sCAIrC,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,oBAAoB,KAAK;oCAChC,sBAAQ,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACrB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;sCAIrC,8OAAC,4KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,8OAAC,8KAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,wLAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,oBAAoB,QAAQ;oCACnC,sBAAQ,8OAAC,gNAAA,CAAA,cAAW;;;;;oCACpB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;;;;;;;8BAOvC,8OAAC,8KAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,8KAAA,CAAA,OAAI;wBACH,WAAW;wBACX,UAAU;wBACV,kCACE,8OAAC,kMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM,cAAc;;gCAC9B;gCACM,cAAc,WAAW,gBAAgB;;;;;;;wBAGlD,OAAO;4BACL;gCACE,KAAK;gCACL,qBACE,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC,oNAAA,CAAA,gBAAa;;;;;wCAAG;wCACF,gBAAgB,KAAK;wCAAC;;;;;;;gCAGzC,wBACE,8OAAC,uJAAA,CAAA,YAAS;oCACR,OAAO;oCACP,SAAS;oCACT,UAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,OAAO,IAAM,cAAc;oCAC3B,WAAW;oCACX,UAAU;oCACV,YAAY;wCACV,GAAG,oBAAoB;wCACvB,UAAU,CAAC,MAAM;4CACf,wBAAwB;gDAAE,GAAG,oBAAoB;gDAAE,SAAS;gDAAM;4CAAS;wCAC7E;oCACF;oCACA,SAAS;oCACT,iBAAiB;;;;;;4BAGvB;4BACA;gCACE,KAAK;gCACL,qBACE,8OAAC,gMAAA,CAAA,QAAK;;sDACJ,8OAAC,kNAAA,CAAA,eAAY;;;;;wCAAG;wCACG,oBAAoB,KAAK;wCAAC;;;;;;;gCAGjD,wBACE,8OAAC,uJAAA,CAAA,YAAS;oCACR,OAAO;oCACP,SAAS;oCACT,UAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,OAAO,IAAM,cAAc;oCAC3B,WAAW;oCACX,UAAU;oCACV,YAAY;wCACV,GAAG,wBAAwB;wCAC3B,UAAU,CAAC,MAAM;4CACf,4BAA4B;gDAAE,GAAG,wBAAwB;gDAAE,SAAS;gDAAM;4CAAS;wCACrF;oCACF;oCACA,SAAS;oCACT,iBAAiB;;;;;;4BAGvB;yBACD;;;;;;;;;;;8BAKL,8OAAC,gLAAA,CAAA,QAAK;oBACJ,OAAO;oBACP,MAAM;oBACN,UAAU,IAAM,YAAY;oBAC5B,QAAQ;oBACR,OAAO;oBACP,eAAe;8BAEf,cAAA,8OAAC,sJAAA,CAAA,WAAQ;wBACP,UAAU;wBACV,MAAM;wBACN,eAAe,gBAAgB;wBAC/B,SAAS;wBACT,UAAU;wBACV,UAAU,IAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;AAMxC"}}, {"offset": {"line": 2342, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}