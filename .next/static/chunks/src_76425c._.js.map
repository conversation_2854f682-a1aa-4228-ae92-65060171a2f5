{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts"], "sourcesContent": ["// APISportsGame CMS - Authentication Store\n// Zustand store cho dual authentication system\n\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { authApi } from '@/modules/auth/api';\nimport {\n  SystemUser,\n  RegisteredUser,\n  LoginRequest,\n  AuthState,\n  AuthActions,\n  UseAuthReturn,\n  UsePermissionsReturn,\n  isSystemUser,\n  isRegisteredUser,\n} from '@/modules/auth/types';\n\n// ============================================================================\n// AUTH STORE TYPES\n// ============================================================================\n\ninterface AuthStoreState extends AuthState, AuthActions {\n  // Helpers\n  isSystemUser: () => boolean;\n  isRegisteredUser: () => boolean;\n  hasRole: (role: string) => boolean;\n  hasTier: (tier: string) => boolean;\n}\n\n// ============================================================================\n// AUTH STORE IMPLEMENTATION\n// ============================================================================\n\nexport const useAuthStore = create<AuthStoreState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // ========================================================================\n      // LOGIN ACTION\n      // ========================================================================\n      login: async (credentials: LoginRequest) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const response = await apiClient.login(credentials);\n\n          set({\n            user: response.user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Đăng nhập thất bại';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // LOGOUT ACTION\n      // ========================================================================\n      logout: () => {\n        try {\n          apiClient.logoutUser();\n        } catch (error) {\n          console.error('Logout error:', error);\n        } finally {\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n        }\n      },\n\n      // ========================================================================\n      // GET PROFILE ACTION\n      // ========================================================================\n      getProfile: async () => {\n        set({ isLoading: true, error: null });\n\n        try {\n          const user = await apiClient.getProfile();\n\n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message ||\n            error.message ||\n            'Không thể lấy thông tin người dùng';\n\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n\n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // CLEAR ERROR ACTION\n      // ========================================================================\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // ========================================================================\n      // HELPER METHODS\n      // ========================================================================\n      isSystemUser: () => {\n        const { user } = get();\n        return user !== null && 'role' in user;\n      },\n\n      isRegisteredUser: () => {\n        const { user } = get();\n        return user !== null && 'tier' in user;\n      },\n\n      hasRole: (role: string) => {\n        const { user, isSystemUser } = get();\n        if (!isSystemUser() || !user) return false;\n\n        const systemUser = user as SystemUser;\n        return systemUser.role === role;\n      },\n\n      hasTier: (tier: string) => {\n        const { user, isRegisteredUser } = get();\n        if (!isRegisteredUser() || !user) return false;\n\n        const registeredUser = user as RegisteredUser;\n        return registeredUser.tier === tier;\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// ============================================================================\n// AUTH HOOKS\n// ============================================================================\n\n// Hook để check permissions\nexport const usePermissions = () => {\n  const { user, isSystemUser, isRegisteredUser, hasRole, hasTier } = useAuthStore();\n\n  return {\n    // User type checks\n    isSystemUser: isSystemUser(),\n    isRegisteredUser: isRegisteredUser(),\n\n    // Role checks (SystemUser)\n    isAdmin: hasRole('admin'),\n    isEditor: hasRole('editor'),\n    isModerator: hasRole('moderator'),\n\n    // Tier checks (RegisteredUser)\n    isFree: hasTier('free'),\n    isPremium: hasTier('premium'),\n    isEnterprise: hasTier('enterprise'),\n\n    // Permission checks\n    canManageUsers: hasRole('admin'),\n    canManageLeagues: hasRole('admin') || hasRole('editor'),\n    canManageTeams: hasRole('admin') || hasRole('editor'),\n    canManageFixtures: hasRole('admin') || hasRole('editor') || hasRole('moderator'),\n    canSync: hasRole('admin') || hasRole('editor'),\n    canViewAnalytics: hasRole('admin') || hasRole('editor'),\n\n    // Current user\n    currentUser: user,\n  };\n};\n\n// Hook để check authentication status\nexport const useAuth = () => {\n  const {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    getProfile,\n    clearError\n  } = useAuthStore();\n\n  return {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    getProfile,\n    clearError,\n  };\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,+CAA+C;;;;;;AAE/C;AACA;;;;AA8BO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,2EAA2E;QAC3E,eAAe;QACf,2EAA2E;QAC3E,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,UAAU,KAAK,CAAC;gBAEvC,IAAI;oBACF,MAAM,SAAS,IAAI;oBACnB,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,gBAAgB;QAChB,2EAA2E;QAC3E,QAAQ;YACN,IAAI;gBACF,UAAU,UAAU;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC,SAAU;gBACR,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,OAAO,MAAM,UAAU,UAAU;gBAEvC,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACzC,MAAM,OAAO,IACb;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,2EAA2E;QAC3E,iBAAiB;QACjB,2EAA2E;QAC3E,cAAc;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,SAAS,QAAQ,UAAU;QACpC;QAEA,kBAAkB;YAChB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,SAAS,QAAQ,UAAU;QACpC;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG;YAC/B,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO;YAErC,MAAM,aAAa;YACnB,OAAO,WAAW,IAAI,KAAK;QAC7B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG;YACnC,IAAI,CAAC,sBAAsB,CAAC,MAAM,OAAO;YAEzC,MAAM,iBAAiB;YACvB,OAAO,eAAe,IAAI,KAAK;QACjC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AASG,MAAM,iBAAiB;;IAC5B,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnE,OAAO;QACL,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAElB,2BAA2B;QAC3B,SAAS,QAAQ;QACjB,UAAU,QAAQ;QAClB,aAAa,QAAQ;QAErB,+BAA+B;QAC/B,QAAQ,QAAQ;QAChB,WAAW,QAAQ;QACnB,cAAc,QAAQ;QAEtB,oBAAoB;QACpB,gBAAgB,QAAQ;QACxB,kBAAkB,QAAQ,YAAY,QAAQ;QAC9C,gBAAgB,QAAQ,YAAY,QAAQ;QAC5C,mBAAmB,QAAQ,YAAY,QAAQ,aAAa,QAAQ;QACpE,SAAS,QAAQ,YAAY,QAAQ;QACrC,kBAAkB,QAAQ,YAAY,QAAQ;QAE9C,eAAe;QACf,aAAa;IACf;AACF;GA7Ba;;QACwD;;;AA+B9D,MAAM,UAAU;;IACrB,MAAM,EACJ,IAAI,EACJ,eAAe,EACf,SAAS,EACT,KAAK,EACL,KAAK,EACL,MAAM,EACN,UAAU,EACV,UAAU,EACX,GAAG;IAEJ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IAtBa;;QAUP"}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Login Page\n// Dual authentication system theo CMS Development Guide\n\nimport React, { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { \n  Card, \n  Form, \n  Input, \n  Button, \n  Typography, \n  Alert, \n  Space,\n  Divider,\n  Row,\n  Col \n} from 'antd';\nimport { \n  UserOutlined, \n  LockOutlined, \n  LoginOutlined,\n  ApiOutlined \n} from '@ant-design/icons';\nimport { useAuth } from '@/stores/auth-store';\nimport { LoginRequest } from '@/types';\n\nconst { Title, Text } = Typography;\n\n// ============================================================================\n// LOGIN PAGE COMPONENT\n// ============================================================================\n\nexport default function LoginPage() {\n  const router = useRouter();\n  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();\n  const [form] = Form.useForm();\n\n  // Redirect nếu đã đăng nhập\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, router]);\n\n  // Clear error khi component unmount\n  useEffect(() => {\n    return () => {\n      clearError();\n    };\n  }, [clearError]);\n\n  // ========================================================================\n  // FORM SUBMIT HANDLER\n  // ========================================================================\n  const handleSubmit = async (values: LoginRequest) => {\n    try {\n      await login(values);\n      // Redirect sẽ được xử lý bởi useEffect\n    } catch (error) {\n      // Error đã được xử lý trong store\n      console.error('Login failed:', error);\n    }\n  };\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\">\n      <Row justify=\"center\" align=\"middle\" className=\"w-full\">\n        <Col xs={24} sm={20} md={16} lg={12} xl={8}>\n          <Card \n            className=\"shadow-2xl border-0\"\n            bodyStyle={{ padding: '2rem' }}\n          >\n            {/* Header */}\n            <div className=\"text-center mb-8\">\n              <div className=\"flex justify-center mb-4\">\n                <div className=\"bg-blue-500 p-3 rounded-full\">\n                  <ApiOutlined className=\"text-white text-2xl\" />\n                </div>\n              </div>\n              <Title level={2} className=\"mb-2\">\n                APISportsGame CMS\n              </Title>\n              <Text type=\"secondary\" className=\"text-base\">\n                Đăng nhập vào hệ thống quản lý\n              </Text>\n            </div>\n\n            {/* Error Alert */}\n            {error && (\n              <Alert\n                message=\"Đăng nhập thất bại\"\n                description={error}\n                type=\"error\"\n                showIcon\n                closable\n                onClose={clearError}\n                className=\"mb-6\"\n              />\n            )}\n\n            {/* Login Form */}\n            <Form\n              form={form}\n              name=\"login\"\n              onFinish={handleSubmit}\n              layout=\"vertical\"\n              size=\"large\"\n              autoComplete=\"off\"\n            >\n              <Form.Item\n                name=\"username\"\n                label=\"Tên đăng nhập\"\n                rules={[\n                  { \n                    required: true, \n                    message: 'Vui lòng nhập tên đăng nhập!' \n                  },\n                  {\n                    min: 3,\n                    message: 'Tên đăng nhập phải có ít nhất 3 ký tự!'\n                  }\n                ]}\n              >\n                <Input\n                  prefix={<UserOutlined />}\n                  placeholder=\"Nhập tên đăng nhập\"\n                  autoComplete=\"username\"\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"password\"\n                label=\"Mật khẩu\"\n                rules={[\n                  { \n                    required: true, \n                    message: 'Vui lòng nhập mật khẩu!' \n                  },\n                  {\n                    min: 6,\n                    message: 'Mật khẩu phải có ít nhất 6 ký tự!'\n                  }\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"Nhập mật khẩu\"\n                  autoComplete=\"current-password\"\n                />\n              </Form.Item>\n\n              <Form.Item className=\"mb-6\">\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={isLoading}\n                  icon={<LoginOutlined />}\n                  block\n                  className=\"h-12 text-base font-medium\"\n                >\n                  {isLoading ? 'Đang đăng nhập...' : 'Đăng nhập'}\n                </Button>\n              </Form.Item>\n            </Form>\n\n            <Divider>\n              <Text type=\"secondary\">Thông tin hệ thống</Text>\n            </Divider>\n\n            {/* System Info */}\n            <div className=\"text-center space-y-2\">\n              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                <Title level={5} className=\"mb-2 text-blue-700\">\n                  Dual Authentication System\n                </Title>\n                <Space direction=\"vertical\" size=\"small\">\n                  <Text type=\"secondary\" className=\"text-sm\">\n                    🔐 SystemUser: Admin, Editor, Moderator\n                  </Text>\n                  <Text type=\"secondary\" className=\"text-sm\">\n                    👤 RegisteredUser: Free, Premium, Enterprise\n                  </Text>\n                </Space>\n              </div>\n              \n              <Text type=\"secondary\" className=\"text-xs\">\n                API Base URL: http://localhost:3000\n              </Text>\n            </div>\n\n            {/* Footer */}\n            <div className=\"text-center mt-6 pt-4 border-t border-gray-100\">\n              <Text type=\"secondary\" className=\"text-xs\">\n                APISportsGame CMS v1.0.0 - Phase 1 Implementation\n              </Text>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,iCAAiC;AACjC,wDAAwD;AAExD;AACA;AAmBA;AAlBA;AAAA;AAAA;AAAA;AAAA;AAYA;AAZA;AAAA;AAYA;AAAA;AAZA;AAYA;AAZA;AAAA;;;AAPA;;;;;;AA4BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAMnB,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACvE,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC;QAAiB;KAAO;IAE5B,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;uCAAO;oBACL;gBACF;;QACF;8BAAG;QAAC;KAAW;IAEf,2EAA2E;IAC3E,sBAAsB;IACtB,2EAA2E;IAC3E,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,MAAM;QACZ,uCAAuC;QACzC,EAAE,OAAO,OAAO;YACd,kCAAkC;YAClC,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAC3E,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,+KAAA,CAAA,MAAG;YAAC,SAAQ;YAAS,OAAM;YAAS,WAAU;sBAC7C,cAAA,6LAAC,+KAAA,CAAA,MAAG;gBAAC,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;gBAAI,IAAI;0BACvC,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBACH,WAAU;oBACV,WAAW;wBAAE,SAAS;oBAAO;;sCAG7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG3B,6LAAC;oCAAM,OAAO;oCAAG,WAAU;8CAAO;;;;;;8CAGlC,6LAAC;oCAAK,MAAK;oCAAY,WAAU;8CAAY;;;;;;;;;;;;wBAM9C,uBACC,6LAAC,mLAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,aAAa;4BACb,MAAK;4BACL,QAAQ;4BACR,QAAQ;4BACR,SAAS;4BACT,WAAU;;;;;;sCAKd,6LAAC,iLAAA,CAAA,OAAI;4BACH,MAAM;4BACN,MAAK;4BACL,UAAU;4BACV,QAAO;4BACP,MAAK;4BACL,cAAa;;8CAEb,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CACE,UAAU;4CACV,SAAS;wCACX;wCACA;4CACE,KAAK;4CACL,SAAS;wCACX;qCACD;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,cAAa;;;;;;;;;;;8CAIjB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CACE,UAAU;4CACV,SAAS;wCACX;wCACA;4CACE,KAAK;4CACL,SAAS;wCACX;qCACD;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;wCACb,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,cAAa;;;;;;;;;;;8CAIjB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,WAAU;8CACnB,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAS;wCACT,SAAS;wCACT,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;wCACpB,KAAK;wCACL,WAAU;kDAET,YAAY,sBAAsB;;;;;;;;;;;;;;;;;sCAKzC,6LAAC,uLAAA,CAAA,UAAO;sCACN,cAAA,6LAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;sCAIzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,OAAO;4CAAG,WAAU;sDAAqB;;;;;;sDAGhD,6LAAC,mMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAW,MAAK;;8DAC/B,6LAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;8DAG3C,6LAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;8CAM/C,6LAAC;oCAAK,MAAK;oCAAY,WAAU;8CAAU;;;;;;;;;;;;sCAM7C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,MAAK;gCAAY,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD;GA5KwB;;QACP,qIAAA,CAAA,YAAS;QACyC,iIAAA,CAAA,UAAO;QACzD,iLAAA,CAAA,OAAI,CAAC;;;KAHE"}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}