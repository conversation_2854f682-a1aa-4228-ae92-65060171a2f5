{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/query-client.ts"], "sourcesContent": ["// APISportsGame CMS - TanStack Query Configuration\n// Setup theo CMS Development Guide specifications\n\nimport { QueryClient } from '@tanstack/react-query';\n\n// Query Client configuration theo performance rules\nexport const queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      gcTime: 10 * 60 * 1000, // 10 minutes (cacheTime renamed to gcTime)\n      retry: 3,\n      refetchOnWindowFocus: false,\n      refetchOnReconnect: true,\n    },\n    mutations: {\n      retry: 1,\n    },\n  },\n});\n\n// Query Keys - centralized key management\nexport const queryKeys = {\n  // Auth keys\n  auth: {\n    profile: ['auth', 'profile'] as const,\n  },\n  \n  // League keys\n  leagues: {\n    all: ['leagues'] as const,\n    lists: () => [...queryKeys.leagues.all, 'list'] as const,\n    list: (filters?: any) => [...queryKeys.leagues.lists(), filters] as const,\n    details: () => [...queryKeys.leagues.all, 'detail'] as const,\n    detail: (id: number) => [...queryKeys.leagues.details(), id] as const,\n    active: () => [...queryKeys.leagues.all, 'active'] as const,\n  },\n  \n  // Team keys\n  teams: {\n    all: ['teams'] as const,\n    lists: () => [...queryKeys.teams.all, 'list'] as const,\n    list: (filters?: any) => [...queryKeys.teams.lists(), filters] as const,\n    details: () => [...queryKeys.teams.all, 'detail'] as const,\n    detail: (id: number) => [...queryKeys.teams.details(), id] as const,\n  },\n  \n  // Fixture keys\n  fixtures: {\n    all: ['fixtures'] as const,\n    lists: () => [...queryKeys.fixtures.all, 'list'] as const,\n    list: (filters?: any) => [...queryKeys.fixtures.lists(), filters] as const,\n    details: () => [...queryKeys.fixtures.all, 'detail'] as const,\n    detail: (id: number) => [...queryKeys.fixtures.details(), id] as const,\n    live: () => [...queryKeys.fixtures.all, 'live'] as const,\n    today: () => [...queryKeys.fixtures.all, 'today'] as const,\n  },\n  \n  // User keys\n  users: {\n    all: ['users'] as const,\n    lists: () => [...queryKeys.users.all, 'list'] as const,\n    list: (filters?: any) => [...queryKeys.users.lists(), filters] as const,\n    details: () => [...queryKeys.users.all, 'detail'] as const,\n    detail: (id: number) => [...queryKeys.users.details(), id] as const,\n  },\n  \n  // Sync keys\n  sync: {\n    all: ['sync'] as const,\n    status: () => [...queryKeys.sync.all, 'status'] as const,\n  },\n  \n  // Dashboard keys\n  dashboard: {\n    all: ['dashboard'] as const,\n    stats: () => [...queryKeys.dashboard.all, 'stats'] as const,\n  },\n} as const;\n"], "names": [], "mappings": "AAAA,mDAAmD;AACnD,kDAAkD;;;;;AAElD;;AAGO,MAAM,cAAc,IAAI,gLAAA,CAAA,cAAW,CAAC;IACzC,gBAAgB;QACd,SAAS;YACP,WAAW,IAAI,KAAK;YACpB,QAAQ,KAAK,KAAK;YAClB,OAAO;YACP,sBAAsB;YACtB,oBAAoB;QACtB;QACA,WAAW;YACT,OAAO;QACT;IACF;AACF;AAGO,MAAM,YAAY;IACvB,YAAY;IACZ,MAAM;QACJ,SAAS;YAAC;YAAQ;SAAU;IAC9B;IAEA,cAAc;IACd,SAAS;QACP,KAAK;YAAC;SAAU;QAChB,OAAO,IAAM;mBAAI,UAAU,OAAO,CAAC,GAAG;gBAAE;aAAO;QAC/C,MAAM,CAAC,UAAkB;mBAAI,UAAU,OAAO,CAAC,KAAK;gBAAI;aAAQ;QAChE,SAAS,IAAM;mBAAI,UAAU,OAAO,CAAC,GAAG;gBAAE;aAAS;QACnD,QAAQ,CAAC,KAAe;mBAAI,UAAU,OAAO,CAAC,OAAO;gBAAI;aAAG;QAC5D,QAAQ,IAAM;mBAAI,UAAU,OAAO,CAAC,GAAG;gBAAE;aAAS;IACpD;IAEA,YAAY;IACZ,OAAO;QACL,KAAK;YAAC;SAAQ;QACd,OAAO,IAAM;mBAAI,UAAU,KAAK,CAAC,GAAG;gBAAE;aAAO;QAC7C,MAAM,CAAC,UAAkB;mBAAI,UAAU,KAAK,CAAC,KAAK;gBAAI;aAAQ;QAC9D,SAAS,IAAM;mBAAI,UAAU,KAAK,CAAC,GAAG;gBAAE;aAAS;QACjD,QAAQ,CAAC,KAAe;mBAAI,UAAU,KAAK,CAAC,OAAO;gBAAI;aAAG;IAC5D;IAEA,eAAe;IACf,UAAU;QACR,KAAK;YAAC;SAAW;QACjB,OAAO,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAO;QAChD,MAAM,CAAC,UAAkB;mBAAI,UAAU,QAAQ,CAAC,KAAK;gBAAI;aAAQ;QACjE,SAAS,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAS;QACpD,QAAQ,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,OAAO;gBAAI;aAAG;QAC7D,MAAM,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAO;QAC/C,OAAO,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAQ;IACnD;IAEA,YAAY;IACZ,OAAO;QACL,KAAK;YAAC;SAAQ;QACd,OAAO,IAAM;mBAAI,UAAU,KAAK,CAAC,GAAG;gBAAE;aAAO;QAC7C,MAAM,CAAC,UAAkB;mBAAI,UAAU,KAAK,CAAC,KAAK;gBAAI;aAAQ;QAC9D,SAAS,IAAM;mBAAI,UAAU,KAAK,CAAC,GAAG;gBAAE;aAAS;QACjD,QAAQ,CAAC,KAAe;mBAAI,UAAU,KAAK,CAAC,OAAO;gBAAI;aAAG;IAC5D;IAEA,YAAY;IACZ,MAAM;QACJ,KAAK;YAAC;SAAO;QACb,QAAQ,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAS;IACjD;IAEA,iBAAiB;IACjB,WAAW;QACT,KAAK;YAAC;SAAY;QAClB,OAAO,IAAM;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;aAAQ;IACpD;AACF"}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/providers.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Providers Component\n// Setup all providers theo CMS Development Guide\n\nimport React from 'react';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { ConfigProvider, theme, App } from 'antd';\nimport viVN from 'antd/locale/vi_VN';\nimport { queryClient } from '@/lib/query-client';\n\n// ============================================================================\n// PROVIDERS COMPONENT\n// ============================================================================\n\ninterface ProvidersProps {\n  children: React.ReactNode;\n}\n\nexport function Providers({ children }: ProvidersProps) {\n  return (\n    <QueryClientProvider client={queryClient}>\n      <ConfigProvider\n        locale={viVN}\n        theme={{\n          algorithm: theme.defaultAlgorithm,\n          token: {\n            // Primary colors\n            colorPrimary: '#1890ff',\n            colorSuccess: '#52c41a',\n            colorWarning: '#faad14',\n            colorError: '#ff4d4f',\n            colorInfo: '#1890ff',\n            \n            // Layout\n            borderRadius: 6,\n            wireframe: false,\n            \n            // Typography\n            fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',\n            fontSize: 14,\n            \n            // Spacing\n            padding: 16,\n            margin: 16,\n          },\n          components: {\n            // Layout components\n            Layout: {\n              headerBg: '#001529',\n              siderBg: '#001529',\n              bodyBg: '#f0f2f5',\n            },\n            \n            // Menu components\n            Menu: {\n              darkItemBg: '#001529',\n              darkItemSelectedBg: '#1890ff',\n              darkItemHoverBg: '#1890ff',\n            },\n            \n            // Table components\n            Table: {\n              headerBg: '#fafafa',\n              headerColor: '#262626',\n              rowHoverBg: '#f5f5f5',\n            },\n            \n            // Card components\n            Card: {\n              headerBg: '#fafafa',\n              actionsBg: '#fafafa',\n            },\n            \n            // Button components\n            Button: {\n              borderRadius: 6,\n              controlHeight: 32,\n            },\n            \n            // Form components\n            Form: {\n              labelColor: '#262626',\n              labelFontSize: 14,\n            },\n          },\n        }}\n      >\n        <App>\n          {children}\n        </App>\n      </ConfigProvider>\n      \n      {/* React Query Devtools - chỉ hiển thị trong development */}\n      {process.env.NODE_ENV === 'development' && (\n        <ReactQueryDevtools \n          initialIsOpen={false} \n          position=\"bottom-right\"\n        />\n      )}\n    </QueryClientProvider>\n  );\n}\n\n// ============================================================================\n// THEME PROVIDER (Optional - for dark mode support)\n// ============================================================================\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n  darkMode?: boolean;\n}\n\nexport function ThemeProvider({ children, darkMode = false }: ThemeProviderProps) {\n  const themeConfig = {\n    algorithm: darkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,\n    token: {\n      colorPrimary: '#1890ff',\n      colorSuccess: '#52c41a',\n      colorWarning: '#faad14',\n      colorError: '#ff4d4f',\n      colorInfo: '#1890ff',\n      borderRadius: 6,\n      wireframe: false,\n    },\n    components: {\n      Layout: {\n        headerBg: darkMode ? '#141414' : '#001529',\n        siderBg: darkMode ? '#141414' : '#001529',\n        bodyBg: darkMode ? '#000000' : '#f0f2f5',\n      },\n      Menu: {\n        darkItemBg: darkMode ? '#141414' : '#001529',\n        darkItemSelectedBg: '#1890ff',\n        darkItemHoverBg: '#1890ff',\n      },\n    },\n  };\n\n  return (\n    <ConfigProvider theme={themeConfig} locale={viVN}>\n      <App>\n        {children}\n      </App>\n    </ConfigProvider>\n  );\n}\n\nexport default Providers;\n"], "names": [], "mappings": ";;;;;;AAUA;AAJA;AAEA;AACA;AADA;AAAA;AAuFO;AAxFP;AAPA;;;;;;;AAoBO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ,gIAAA,CAAA,cAAW;;0BACtC,6LAAC,yNAAA,CAAA,iBAAc;gBACb,QAAQ,0IAAA,CAAA,UAAI;gBACZ,OAAO;oBACL,WAAW,mLAAA,CAAA,QAAK,CAAC,gBAAgB;oBACjC,OAAO;wBACL,iBAAiB;wBACjB,cAAc;wBACd,cAAc;wBACd,cAAc;wBACd,YAAY;wBACZ,WAAW;wBAEX,SAAS;wBACT,cAAc;wBACd,WAAW;wBAEX,aAAa;wBACb,YAAY;wBACZ,UAAU;wBAEV,UAAU;wBACV,SAAS;wBACT,QAAQ;oBACV;oBACA,YAAY;wBACV,oBAAoB;wBACpB,QAAQ;4BACN,UAAU;4BACV,SAAS;4BACT,QAAQ;wBACV;wBAEA,kBAAkB;wBAClB,MAAM;4BACJ,YAAY;4BACZ,oBAAoB;4BACpB,iBAAiB;wBACnB;wBAEA,mBAAmB;wBACnB,OAAO;4BACL,UAAU;4BACV,aAAa;4BACb,YAAY;wBACd;wBAEA,kBAAkB;wBAClB,MAAM;4BACJ,UAAU;4BACV,WAAW;wBACb;wBAEA,oBAAoB;wBACpB,QAAQ;4BACN,cAAc;4BACd,eAAe;wBACjB;wBAEA,kBAAkB;wBAClB,MAAM;4BACJ,YAAY;4BACZ,eAAe;wBACjB;oBACF;gBACF;0BAEA,cAAA,6LAAC,+KAAA,CAAA,MAAG;8BACD;;;;;;;;;;;YAKJ,oDAAyB,+BACxB,6LAAC,uLAAA,CAAA,qBAAkB;gBACjB,eAAe;gBACf,UAAS;;;;;;;;;;;;AAKnB;KAnFgB;AA8FT,SAAS,cAAc,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAsB;IAC9E,MAAM,cAAc;QAClB,WAAW,WAAW,mLAAA,CAAA,QAAK,CAAC,aAAa,GAAG,mLAAA,CAAA,QAAK,CAAC,gBAAgB;QAClE,OAAO;YACL,cAAc;YACd,cAAc;YACd,cAAc;YACd,YAAY;YACZ,WAAW;YACX,cAAc;YACd,WAAW;QACb;QACA,YAAY;YACV,QAAQ;gBACN,UAAU,WAAW,YAAY;gBACjC,SAAS,WAAW,YAAY;gBAChC,QAAQ,WAAW,YAAY;YACjC;YACA,MAAM;gBACJ,YAAY,WAAW,YAAY;gBACnC,oBAAoB;gBACpB,iBAAiB;YACnB;QACF;IACF;IAEA,qBACE,6LAAC,yNAAA,CAAA,iBAAc;QAAC,OAAO;QAAa,QAAQ,0IAAA,CAAA,UAAI;kBAC9C,cAAA,6LAAC,+KAAA,CAAA,MAAG;sBACD;;;;;;;;;;;AAIT;MAjCgB;uCAmCD"}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}