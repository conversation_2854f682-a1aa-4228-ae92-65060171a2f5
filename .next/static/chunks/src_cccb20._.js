(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_cccb20._.js", {

"[project]/src/lib/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// APISportsGame CMS - API Client
// Base API client setup theo CMS Development Guide
__turbopack_esm__({
    "apiClient": (()=>apiClient),
    "default": (()=>__TURBOPACK__default__export__),
    "endpoints": (()=>endpoints)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
// ============================================================================
// API CONFIGURATION
// ============================================================================
const API_BASE_URL = 'http://localhost:3000';
const endpoints = {
    // Authentication endpoints
    auth: {
        login: '/auth/login',
        profile: '/auth/profile',
        refresh: '/auth/refresh',
        logout: '/auth/logout',
        adminRegister: '/auth/admin/register'
    },
    // RegisteredUser endpoints
    users: {
        register: '/users/register',
        login: '/users/login',
        verifyEmail: '/users/verify-email',
        profile: '/users/profile',
        apiUsage: '/users/api-usage'
    },
    // League endpoints
    leagues: {
        list: '/leagues',
        active: '/leagues/active',
        byId: (id)=>`/leagues/${id}`,
        create: '/leagues',
        update: (id)=>`/leagues/${id}`,
        delete: (id)=>`/leagues/${id}`,
        fixtures: (id)=>`/leagues/${id}/fixtures`
    },
    // Team endpoints
    teams: {
        list: '/teams',
        byId: (id)=>`/teams/${id}`,
        create: '/teams',
        update: (id)=>`/teams/${id}`,
        delete: (id)=>`/teams/${id}`,
        fixtures: (id)=>`/teams/${id}/fixtures`
    },
    // Fixture endpoints
    fixtures: {
        list: '/fixtures',
        byId: (id)=>`/fixtures/${id}`,
        live: '/fixtures/live',
        today: '/fixtures/today',
        byDate: (date)=>`/fixtures/date/${date}`,
        create: '/fixtures',
        update: (id)=>`/fixtures/${id}`,
        delete: (id)=>`/fixtures/${id}`
    },
    // Sync endpoints
    sync: {
        leagues: '/sync/leagues',
        teams: '/sync/teams',
        fixtures: '/sync/fixtures',
        daily: '/sync/daily',
        status: '/sync/status'
    }
};
// ============================================================================
// AXIOS INSTANCE SETUP
// ============================================================================
class ApiClient {
    instance;
    constructor(){
        this.instance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: API_BASE_URL,
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        this.setupInterceptors();
    }
    setupInterceptors() {
        // Request interceptor - thêm JWT token
        this.instance.interceptors.request.use((config)=>{
            const token = this.getToken();
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }
            return config;
        }, (error)=>Promise.reject(error));
        // Response interceptor - xử lý errors
        this.instance.interceptors.response.use((response)=>response, async (error)=>{
            const originalRequest = error.config;
            // Handle 401 errors - token expired
            if (error.response?.status === 401 && !originalRequest._retry) {
                originalRequest._retry = true;
                try {
                    await this.refreshToken();
                    const token = this.getToken();
                    if (token) {
                        originalRequest.headers.Authorization = `Bearer ${token}`;
                        return this.instance(originalRequest);
                    }
                } catch (refreshError) {
                    this.logout();
                    window.location.href = '/auth/login';
                }
            }
            return Promise.reject(error);
        });
    }
    getToken() {
        if ("TURBOPACK compile-time truthy", 1) {
            return localStorage.getItem('access_token');
        }
        "TURBOPACK unreachable";
    }
    setToken(token) {
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.setItem('access_token', token);
        }
    }
    removeToken() {
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
        }
    }
    async refreshToken() {
        const refreshToken = ("TURBOPACK compile-time truthy", 1) ? localStorage.getItem('refresh_token') : ("TURBOPACK unreachable", undefined);
        if (!refreshToken) {
            throw new Error('No refresh token');
        }
        const response = await this.instance.post(endpoints.auth.refresh, {
            refresh_token: refreshToken
        });
        const { access_token } = response.data;
        this.setToken(access_token);
    }
    logout() {
        this.removeToken();
    }
    // ============================================================================
    // AUTHENTICATION METHODS
    // ============================================================================
    async login(credentials) {
        const response = await this.instance.post(endpoints.auth.login, credentials);
        const { access_token, refresh_token } = response.data;
        this.setToken(access_token);
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.setItem('refresh_token', refresh_token);
        }
        return response.data;
    }
    async getProfile() {
        const response = await this.instance.get(endpoints.auth.profile);
        return response.data;
    }
    async logoutUser() {
        try {
            await this.instance.post(endpoints.auth.logout);
        } finally{
            this.logout();
        }
    }
    async createSystemUser(userData) {
        const response = await this.instance.post(endpoints.auth.adminRegister, userData);
        return response.data;
    }
    // ============================================================================
    // LEAGUE METHODS
    // ============================================================================
    async getLeagues(params) {
        const response = await this.instance.get(endpoints.leagues.list, {
            params
        });
        return response.data;
    }
    async getLeague(id) {
        const response = await this.instance.get(endpoints.leagues.byId(id));
        return response.data;
    }
    async createLeague(leagueData) {
        const response = await this.instance.post(endpoints.leagues.create, leagueData);
        return response.data;
    }
    async updateLeague(id, leagueData) {
        const response = await this.instance.put(endpoints.leagues.update(id), leagueData);
        return response.data;
    }
    async deleteLeague(id) {
        await this.instance.delete(endpoints.leagues.delete(id));
    }
    // ============================================================================
    // TEAM METHODS
    // ============================================================================
    async getTeams(params) {
        const response = await this.instance.get(endpoints.teams.list, {
            params
        });
        return response.data;
    }
    async getTeam(id) {
        const response = await this.instance.get(endpoints.teams.byId(id));
        return response.data;
    }
    async createTeam(teamData) {
        const response = await this.instance.post(endpoints.teams.create, teamData);
        return response.data;
    }
    async updateTeam(id, teamData) {
        const response = await this.instance.put(endpoints.teams.update(id), teamData);
        return response.data;
    }
    async deleteTeam(id) {
        await this.instance.delete(endpoints.teams.delete(id));
    }
    // ============================================================================
    // FIXTURE METHODS
    // ============================================================================
    async getFixtures(params) {
        const response = await this.instance.get(endpoints.fixtures.list, {
            params
        });
        return response.data;
    }
    async getFixture(id) {
        const response = await this.instance.get(endpoints.fixtures.byId(id));
        return response.data;
    }
    async getLiveFixtures() {
        const response = await this.instance.get(endpoints.fixtures.live);
        return response.data;
    }
    async getTodayFixtures() {
        const response = await this.instance.get(endpoints.fixtures.today);
        return response.data;
    }
    // ============================================================================
    // SYNC METHODS
    // ============================================================================
    async syncLeagues() {
        await this.instance.post(endpoints.sync.leagues);
    }
    async syncTeams() {
        await this.instance.post(endpoints.sync.teams);
    }
    async syncFixtures() {
        await this.instance.post(endpoints.sync.fixtures);
    }
    async getSyncStatus() {
        const response = await this.instance.get(endpoints.sync.status);
        return response.data;
    }
}
const apiClient = new ApiClient();
const __TURBOPACK__default__export__ = apiClient;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/stores/auth-store.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// APISportsGame CMS - Authentication Store
// Zustand store cho dual authentication system
__turbopack_esm__({
    "useAuth": (()=>useAuth),
    "useAuthStore": (()=>useAuthStore),
    "usePermissions": (()=>usePermissions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature();
;
;
;
const useAuthStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // Initial state
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        // ========================================================================
        // LOGIN ACTION
        // ========================================================================
        login: async (credentials)=>{
            set({
                isLoading: true,
                error: null
            });
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].login(credentials);
                set({
                    user: response.user,
                    isAuthenticated: true,
                    isLoading: false,
                    error: null
                });
            } catch (error) {
                const errorMessage = error.response?.data?.message || error.message || 'Đăng nhập thất bại';
                set({
                    user: null,
                    isAuthenticated: false,
                    isLoading: false,
                    error: errorMessage
                });
                throw error;
            }
        },
        // ========================================================================
        // LOGOUT ACTION
        // ========================================================================
        logout: ()=>{
            try {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].logoutUser();
            } catch (error) {
                console.error('Logout error:', error);
            } finally{
                set({
                    user: null,
                    isAuthenticated: false,
                    isLoading: false,
                    error: null
                });
            }
        },
        // ========================================================================
        // GET PROFILE ACTION
        // ========================================================================
        getProfile: async ()=>{
            set({
                isLoading: true,
                error: null
            });
            try {
                const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].getProfile();
                set({
                    user,
                    isAuthenticated: true,
                    isLoading: false,
                    error: null
                });
            } catch (error) {
                const errorMessage = error.response?.data?.message || error.message || 'Không thể lấy thông tin người dùng';
                set({
                    user: null,
                    isAuthenticated: false,
                    isLoading: false,
                    error: errorMessage
                });
                throw error;
            }
        },
        // ========================================================================
        // CLEAR ERROR ACTION
        // ========================================================================
        clearError: ()=>{
            set({
                error: null
            });
        },
        // ========================================================================
        // HELPER METHODS
        // ========================================================================
        isSystemUser: ()=>{
            const { user } = get();
            return user !== null && 'role' in user;
        },
        isRegisteredUser: ()=>{
            const { user } = get();
            return user !== null && 'tier' in user;
        },
        hasRole: (role)=>{
            const { user, isSystemUser } = get();
            if (!isSystemUser() || !user) return false;
            const systemUser = user;
            return systemUser.role === role;
        },
        hasTier: (tier)=>{
            const { user, isRegisteredUser } = get();
            if (!isRegisteredUser() || !user) return false;
            const registeredUser = user;
            return registeredUser.tier === tier;
        }
    }), {
    name: 'auth-storage',
    partialize: (state)=>({
            user: state.user,
            isAuthenticated: state.isAuthenticated
        })
}));
const usePermissions = ()=>{
    _s();
    const { user, isSystemUser, isRegisteredUser, hasRole, hasTier } = useAuthStore();
    return {
        // User type checks
        isSystemUser: isSystemUser(),
        isRegisteredUser: isRegisteredUser(),
        // Role checks (SystemUser)
        isAdmin: hasRole('admin'),
        isEditor: hasRole('editor'),
        isModerator: hasRole('moderator'),
        // Tier checks (RegisteredUser)
        isFree: hasTier('free'),
        isPremium: hasTier('premium'),
        isEnterprise: hasTier('enterprise'),
        // Permission checks
        canManageUsers: hasRole('admin'),
        canManageLeagues: hasRole('admin') || hasRole('editor'),
        canManageTeams: hasRole('admin') || hasRole('editor'),
        canManageFixtures: hasRole('admin') || hasRole('editor') || hasRole('moderator'),
        canSync: hasRole('admin') || hasRole('editor'),
        canViewAnalytics: hasRole('admin') || hasRole('editor'),
        // Current user
        currentUser: user
    };
};
_s(usePermissions, "fa0nCC+W+BSc1JgXNOldQ9BpMEc=", false, function() {
    return [
        useAuthStore
    ];
});
const useAuth = ()=>{
    _s1();
    const { user, isAuthenticated, isLoading, error, login, logout, getProfile, clearError } = useAuthStore();
    return {
        user,
        isAuthenticated,
        isLoading,
        error,
        login,
        logout,
        getProfile,
        clearError
    };
};
_s1(useAuth, "lijLNl1j21u9KAEcaq1vauBZ8kc=", false, function() {
    return [
        useAuthStore
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// APISportsGame CMS - Home Page
// Redirect to appropriate page based on authentication status
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/stores/auth-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$spin$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spin$3e$__ = __turbopack_import__("[project]/node_modules/antd/es/spin/index.js [app-client] (ecmascript) <export default as Spin>");
;
var _s = __turbopack_refresh__.signature();
'use client';
;
;
;
;
function Home() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { isAuthenticated, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Home.useEffect": ()=>{
            if (!isLoading) {
                if (isAuthenticated) {
                    router.push('/dashboard');
                } else {
                    router.push('/auth/login');
                }
            }
        }
    }["Home.useEffect"], [
        isAuthenticated,
        isLoading,
        router
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen flex items-center justify-center",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$spin$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spin$3e$__["Spin"], {
            size: "large",
            tip: "Đang tải..."
        }, void 0, false, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 27,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 26,
        columnNumber: 5
    }, this);
}
_s(Home, "x1YCcUir+HeWcFDAY64YqsnOmcQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = Home;
var _c;
__turbopack_refresh__.register(_c, "Home");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=src_cccb20._.js.map