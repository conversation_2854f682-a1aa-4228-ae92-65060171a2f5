{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/lib/api.ts"], "sourcesContent": ["// APISportsGame CMS - API Client\n// Base API client setup theo CMS Development Guide\n\nimport axios, { AxiosInstance, AxiosResponse } from 'axios';\nimport { \n  SystemUser, \n  RegisteredUser, \n  League, \n  Team, \n  Fixture,\n  LoginRequest,\n  LoginResponse,\n  RegisterRequest,\n  PaginatedResponse,\n  ApiError,\n  DashboardStats,\n  SyncStatus\n} from '@/types';\n\n// ============================================================================\n// API CONFIGURATION\n// ============================================================================\n\nconst API_BASE_URL = 'http://localhost:3000';\n\n// API Endpoints theo CMS Development Guide\nexport const endpoints = {\n  // Authentication endpoints\n  auth: {\n    login: '/auth/login',\n    profile: '/auth/profile',\n    refresh: '/auth/refresh',\n    logout: '/auth/logout',\n    adminRegister: '/auth/admin/register'\n  },\n  // RegisteredUser endpoints\n  users: {\n    register: '/users/register',\n    login: '/users/login',\n    verifyEmail: '/users/verify-email',\n    profile: '/users/profile',\n    apiUsage: '/users/api-usage'\n  },\n  // League endpoints\n  leagues: {\n    list: '/leagues',\n    active: '/leagues/active',\n    byId: (id: number) => `/leagues/${id}`,\n    create: '/leagues',\n    update: (id: number) => `/leagues/${id}`,\n    delete: (id: number) => `/leagues/${id}`,\n    fixtures: (id: number) => `/leagues/${id}/fixtures`\n  },\n  // Team endpoints\n  teams: {\n    list: '/teams',\n    byId: (id: number) => `/teams/${id}`,\n    create: '/teams',\n    update: (id: number) => `/teams/${id}`,\n    delete: (id: number) => `/teams/${id}`,\n    fixtures: (id: number) => `/teams/${id}/fixtures`\n  },\n  // Fixture endpoints\n  fixtures: {\n    list: '/fixtures',\n    byId: (id: number) => `/fixtures/${id}`,\n    live: '/fixtures/live',\n    today: '/fixtures/today',\n    byDate: (date: string) => `/fixtures/date/${date}`,\n    create: '/fixtures',\n    update: (id: number) => `/fixtures/${id}`,\n    delete: (id: number) => `/fixtures/${id}`\n  },\n  // Sync endpoints\n  sync: {\n    leagues: '/sync/leagues',\n    teams: '/sync/teams',\n    fixtures: '/sync/fixtures',\n    daily: '/sync/daily',\n    status: '/sync/status'\n  }\n};\n\n// ============================================================================\n// AXIOS INSTANCE SETUP\n// ============================================================================\n\nclass ApiClient {\n  private instance: AxiosInstance;\n\n  constructor() {\n    this.instance = axios.create({\n      baseURL: API_BASE_URL,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      timeout: 10000,\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor - thêm JWT token\n    this.instance.interceptors.request.use(\n      (config) => {\n        const token = this.getToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Response interceptor - xử lý errors\n    this.instance.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        // Handle 401 errors - token expired\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n          \n          try {\n            await this.refreshToken();\n            const token = this.getToken();\n            if (token) {\n              originalRequest.headers.Authorization = `Bearer ${token}`;\n              return this.instance(originalRequest);\n            }\n          } catch (refreshError) {\n            this.logout();\n            window.location.href = '/auth/login';\n          }\n        }\n\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  private getToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('access_token');\n    }\n    return null;\n  }\n\n  private setToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('access_token', token);\n    }\n  }\n\n  private removeToken(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('refresh_token');\n    }\n  }\n\n  private async refreshToken(): Promise<void> {\n    const refreshToken = typeof window !== 'undefined' \n      ? localStorage.getItem('refresh_token') \n      : null;\n    \n    if (!refreshToken) {\n      throw new Error('No refresh token');\n    }\n\n    const response = await this.instance.post(endpoints.auth.refresh, {\n      refresh_token: refreshToken\n    });\n\n    const { access_token } = response.data;\n    this.setToken(access_token);\n  }\n\n  private logout(): void {\n    this.removeToken();\n  }\n\n  // ============================================================================\n  // AUTHENTICATION METHODS\n  // ============================================================================\n\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    const response = await this.instance.post<LoginResponse>(\n      endpoints.auth.login, \n      credentials\n    );\n    \n    const { access_token, refresh_token } = response.data;\n    this.setToken(access_token);\n    \n    if (typeof window !== 'undefined') {\n      localStorage.setItem('refresh_token', refresh_token);\n    }\n    \n    return response.data;\n  }\n\n  async getProfile(): Promise<SystemUser | RegisteredUser> {\n    const response = await this.instance.get<SystemUser | RegisteredUser>(\n      endpoints.auth.profile\n    );\n    return response.data;\n  }\n\n  async logoutUser(): Promise<void> {\n    try {\n      await this.instance.post(endpoints.auth.logout);\n    } finally {\n      this.logout();\n    }\n  }\n\n  async createSystemUser(userData: any): Promise<SystemUser> {\n    const response = await this.instance.post<SystemUser>(\n      endpoints.auth.adminRegister,\n      userData\n    );\n    return response.data;\n  }\n\n  // ============================================================================\n  // LEAGUE METHODS\n  // ============================================================================\n\n  async getLeagues(params?: any): Promise<PaginatedResponse<League>> {\n    const response = await this.instance.get<PaginatedResponse<League>>(\n      endpoints.leagues.list,\n      { params }\n    );\n    return response.data;\n  }\n\n  async getLeague(id: number): Promise<League> {\n    const response = await this.instance.get<League>(\n      endpoints.leagues.byId(id)\n    );\n    return response.data;\n  }\n\n  async createLeague(leagueData: any): Promise<League> {\n    const response = await this.instance.post<League>(\n      endpoints.leagues.create,\n      leagueData\n    );\n    return response.data;\n  }\n\n  async updateLeague(id: number, leagueData: any): Promise<League> {\n    const response = await this.instance.put<League>(\n      endpoints.leagues.update(id),\n      leagueData\n    );\n    return response.data;\n  }\n\n  async deleteLeague(id: number): Promise<void> {\n    await this.instance.delete(endpoints.leagues.delete(id));\n  }\n\n  // ============================================================================\n  // TEAM METHODS\n  // ============================================================================\n\n  async getTeams(params?: any): Promise<PaginatedResponse<Team>> {\n    const response = await this.instance.get<PaginatedResponse<Team>>(\n      endpoints.teams.list,\n      { params }\n    );\n    return response.data;\n  }\n\n  async getTeam(id: number): Promise<Team> {\n    const response = await this.instance.get<Team>(\n      endpoints.teams.byId(id)\n    );\n    return response.data;\n  }\n\n  async createTeam(teamData: any): Promise<Team> {\n    const response = await this.instance.post<Team>(\n      endpoints.teams.create,\n      teamData\n    );\n    return response.data;\n  }\n\n  async updateTeam(id: number, teamData: any): Promise<Team> {\n    const response = await this.instance.put<Team>(\n      endpoints.teams.update(id),\n      teamData\n    );\n    return response.data;\n  }\n\n  async deleteTeam(id: number): Promise<void> {\n    await this.instance.delete(endpoints.teams.delete(id));\n  }\n\n  // ============================================================================\n  // FIXTURE METHODS\n  // ============================================================================\n\n  async getFixtures(params?: any): Promise<PaginatedResponse<Fixture>> {\n    const response = await this.instance.get<PaginatedResponse<Fixture>>(\n      endpoints.fixtures.list,\n      { params }\n    );\n    return response.data;\n  }\n\n  async getFixture(id: number): Promise<Fixture> {\n    const response = await this.instance.get<Fixture>(\n      endpoints.fixtures.byId(id)\n    );\n    return response.data;\n  }\n\n  async getLiveFixtures(): Promise<Fixture[]> {\n    const response = await this.instance.get<Fixture[]>(\n      endpoints.fixtures.live\n    );\n    return response.data;\n  }\n\n  async getTodayFixtures(): Promise<Fixture[]> {\n    const response = await this.instance.get<Fixture[]>(\n      endpoints.fixtures.today\n    );\n    return response.data;\n  }\n\n  // ============================================================================\n  // SYNC METHODS\n  // ============================================================================\n\n  async syncLeagues(): Promise<void> {\n    await this.instance.post(endpoints.sync.leagues);\n  }\n\n  async syncTeams(): Promise<void> {\n    await this.instance.post(endpoints.sync.teams);\n  }\n\n  async syncFixtures(): Promise<void> {\n    await this.instance.post(endpoints.sync.fixtures);\n  }\n\n  async getSyncStatus(): Promise<SyncStatus> {\n    const response = await this.instance.get<SyncStatus>(\n      endpoints.sync.status\n    );\n    return response.data;\n  }\n}\n\n// Export singleton instance\nexport const apiClient = new ApiClient();\nexport default apiClient;\n"], "names": [], "mappings": "AAAA,iCAAiC;AACjC,mDAAmD;;;;;;AAEnD;;AAgBA,+EAA+E;AAC/E,oBAAoB;AACpB,+EAA+E;AAE/E,MAAM,eAAe;AAGd,MAAM,YAAY;IACvB,2BAA2B;IAC3B,MAAM;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,eAAe;IACjB;IACA,2BAA2B;IAC3B,OAAO;QACL,UAAU;QACV,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;IACZ;IACA,mBAAmB;IACnB,SAAS;QACP,MAAM;QACN,QAAQ;QACR,MAAM,CAAC,KAAe,CAAC,SAAS,EAAE,IAAI;QACtC,QAAQ;QACR,QAAQ,CAAC,KAAe,CAAC,SAAS,EAAE,IAAI;QACxC,QAAQ,CAAC,KAAe,CAAC,SAAS,EAAE,IAAI;QACxC,UAAU,CAAC,KAAe,CAAC,SAAS,EAAE,GAAG,SAAS,CAAC;IACrD;IACA,iBAAiB;IACjB,OAAO;QACL,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,OAAO,EAAE,IAAI;QACpC,QAAQ;QACR,QAAQ,CAAC,KAAe,CAAC,OAAO,EAAE,IAAI;QACtC,QAAQ,CAAC,KAAe,CAAC,OAAO,EAAE,IAAI;QACtC,UAAU,CAAC,KAAe,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;IACnD;IACA,oBAAoB;IACpB,UAAU;QACR,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,UAAU,EAAE,IAAI;QACvC,MAAM;QACN,OAAO;QACP,QAAQ,CAAC,OAAiB,CAAC,eAAe,EAAE,MAAM;QAClD,QAAQ;QACR,QAAQ,CAAC,KAAe,CAAC,UAAU,EAAE,IAAI;QACzC,QAAQ,CAAC,KAAe,CAAC,UAAU,EAAE,IAAI;IAC3C;IACA,iBAAiB;IACjB,MAAM;QACJ,SAAS;QACT,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;IACV;AACF;AAEA,+EAA+E;AAC/E,uBAAuB;AACvB,+EAA+E;AAE/E,MAAM;IACI,SAAwB;IAEhC,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC3B,SAAS;YACT,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEQ,oBAAoB;QAC1B,uCAAuC;QACvC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;YACC,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YAClD;YACA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;QAG5B,sCAAsC;QACtC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,OAAO;YACL,MAAM,kBAAkB,MAAM,MAAM;YAEpC,oCAAoC;YACpC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;gBAC7D,gBAAgB,MAAM,GAAG;gBAEzB,IAAI;oBACF,MAAM,IAAI,CAAC,YAAY;oBACvB,MAAM,QAAQ,IAAI,CAAC,QAAQ;oBAC3B,IAAI,OAAO;wBACT,gBAAgB,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;wBACzD,OAAO,IAAI,CAAC,QAAQ,CAAC;oBACvB;gBACF,EAAE,OAAO,cAAc;oBACrB,IAAI,CAAC,MAAM;oBACX,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB;YACF;YAEA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEQ,WAA0B;QAChC,wCAAmC;YACjC,OAAO,aAAa,OAAO,CAAC;QAC9B;;IAEF;IAEQ,SAAS,KAAa,EAAQ;QACpC,wCAAmC;YACjC,aAAa,OAAO,CAAC,gBAAgB;QACvC;IACF;IAEQ,cAAoB;QAC1B,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,MAAc,eAA8B;QAC1C,MAAM,eAAe,uCACjB,aAAa,OAAO,CAAC;QAGzB,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAChE,eAAe;QACjB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,SAAS,IAAI;QACtC,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEQ,SAAe;QACrB,IAAI,CAAC,WAAW;IAClB;IAEA,+EAA+E;IAC/E,yBAAyB;IACzB,+EAA+E;IAE/E,MAAM,MAAM,WAAyB,EAA0B;QAC7D,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,UAAU,IAAI,CAAC,KAAK,EACpB;QAGF,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,SAAS,IAAI;QACrD,IAAI,CAAC,QAAQ,CAAC;QAEd,wCAAmC;YACjC,aAAa,OAAO,CAAC,iBAAiB;QACxC;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAmD;QACvD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,IAAI,CAAC,OAAO;QAExB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAA4B;QAChC,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM;QAChD,SAAU;YACR,IAAI,CAAC,MAAM;QACb;IACF;IAEA,MAAM,iBAAiB,QAAa,EAAuB;QACzD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,UAAU,IAAI,CAAC,aAAa,EAC5B;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,+EAA+E;IAC/E,iBAAiB;IACjB,+EAA+E;IAE/E,MAAM,WAAW,MAAY,EAAsC;QACjE,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,OAAO,CAAC,IAAI,EACtB;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,UAAU,EAAU,EAAmB;QAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,OAAO,CAAC,IAAI,CAAC;QAEzB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,UAAe,EAAmB;QACnD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,UAAU,OAAO,CAAC,MAAM,EACxB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,EAAU,EAAE,UAAe,EAAmB;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,OAAO,CAAC,MAAM,CAAC,KACzB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,EAAU,EAAiB;QAC5C,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,OAAO,CAAC,MAAM,CAAC;IACtD;IAEA,+EAA+E;IAC/E,eAAe;IACf,+EAA+E;IAE/E,MAAM,SAAS,MAAY,EAAoC;QAC7D,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,KAAK,CAAC,IAAI,EACpB;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,QAAQ,EAAU,EAAiB;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,KAAK,CAAC,IAAI,CAAC;QAEvB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,QAAa,EAAiB;QAC7C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,UAAU,KAAK,CAAC,MAAM,EACtB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,EAAU,EAAE,QAAa,EAAiB;QACzD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,KAAK,CAAC,MAAM,CAAC,KACvB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,MAAM,CAAC;IACpD;IAEA,+EAA+E;IAC/E,kBAAkB;IAClB,+EAA+E;IAE/E,MAAM,YAAY,MAAY,EAAuC;QACnE,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,QAAQ,CAAC,IAAI,EACvB;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,QAAQ,CAAC,IAAI,CAAC;QAE1B,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,kBAAsC;QAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,QAAQ,CAAC,IAAI;QAEzB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,mBAAuC;QAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,QAAQ,CAAC,KAAK;QAE1B,OAAO,SAAS,IAAI;IACtB;IAEA,+EAA+E;IAC/E,eAAe;IACf,+EAA+E;IAE/E,MAAM,cAA6B;QACjC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO;IACjD;IAEA,MAAM,YAA2B;QAC/B,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK;IAC/C;IAEA,MAAM,eAA8B;QAClC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ;IAClD;IAEA,MAAM,gBAAqC;QACzC,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,IAAI,CAAC,MAAM;QAEvB,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY,IAAI;uCACd"}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/stores/auth-store.ts"], "sourcesContent": ["// APISportsGame CMS - Authentication Store\n// Zustand store cho dual authentication system\n\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { SystemUser, RegisteredUser, LoginRequest } from '@/types';\nimport { apiClient } from '@/lib/api';\n\n// ============================================================================\n// AUTH STORE TYPES\n// ============================================================================\n\ninterface AuthState {\n  // State\n  user: SystemUser | RegisteredUser | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n  \n  // Actions\n  login: (credentials: LoginRequest) => Promise<void>;\n  logout: () => void;\n  getProfile: () => Promise<void>;\n  clearError: () => void;\n  \n  // Helpers\n  isSystemUser: () => boolean;\n  isRegisteredUser: () => boolean;\n  hasRole: (role: string) => boolean;\n  hasTier: (tier: string) => boolean;\n}\n\n// ============================================================================\n// AUTH STORE IMPLEMENTATION\n// ============================================================================\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // ========================================================================\n      // LOGIN ACTION\n      // ========================================================================\n      login: async (credentials: LoginRequest) => {\n        set({ isLoading: true, error: null });\n        \n        try {\n          const response = await apiClient.login(credentials);\n          \n          set({\n            user: response.user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message || \n                              error.message || \n                              'Đăng nhập thất bại';\n          \n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n          \n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // LOGOUT ACTION\n      // ========================================================================\n      logout: () => {\n        try {\n          apiClient.logoutUser();\n        } catch (error) {\n          console.error('Logout error:', error);\n        } finally {\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n        }\n      },\n\n      // ========================================================================\n      // GET PROFILE ACTION\n      // ========================================================================\n      getProfile: async () => {\n        set({ isLoading: true, error: null });\n        \n        try {\n          const user = await apiClient.getProfile();\n          \n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message || \n                              error.message || \n                              'Không thể lấy thông tin người dùng';\n          \n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n          \n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // CLEAR ERROR ACTION\n      // ========================================================================\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // ========================================================================\n      // HELPER METHODS\n      // ========================================================================\n      isSystemUser: () => {\n        const { user } = get();\n        return user !== null && 'role' in user;\n      },\n\n      isRegisteredUser: () => {\n        const { user } = get();\n        return user !== null && 'tier' in user;\n      },\n\n      hasRole: (role: string) => {\n        const { user, isSystemUser } = get();\n        if (!isSystemUser() || !user) return false;\n        \n        const systemUser = user as SystemUser;\n        return systemUser.role === role;\n      },\n\n      hasTier: (tier: string) => {\n        const { user, isRegisteredUser } = get();\n        if (!isRegisteredUser() || !user) return false;\n        \n        const registeredUser = user as RegisteredUser;\n        return registeredUser.tier === tier;\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// ============================================================================\n// AUTH HOOKS\n// ============================================================================\n\n// Hook để check permissions\nexport const usePermissions = () => {\n  const { user, isSystemUser, isRegisteredUser, hasRole, hasTier } = useAuthStore();\n\n  return {\n    // User type checks\n    isSystemUser: isSystemUser(),\n    isRegisteredUser: isRegisteredUser(),\n    \n    // Role checks (SystemUser)\n    isAdmin: hasRole('admin'),\n    isEditor: hasRole('editor'),\n    isModerator: hasRole('moderator'),\n    \n    // Tier checks (RegisteredUser)\n    isFree: hasTier('free'),\n    isPremium: hasTier('premium'),\n    isEnterprise: hasTier('enterprise'),\n    \n    // Permission checks\n    canManageUsers: hasRole('admin'),\n    canManageLeagues: hasRole('admin') || hasRole('editor'),\n    canManageTeams: hasRole('admin') || hasRole('editor'),\n    canManageFixtures: hasRole('admin') || hasRole('editor') || hasRole('moderator'),\n    canSync: hasRole('admin') || hasRole('editor'),\n    canViewAnalytics: hasRole('admin') || hasRole('editor'),\n    \n    // Current user\n    currentUser: user,\n  };\n};\n\n// Hook để check authentication status\nexport const useAuth = () => {\n  const { \n    user, \n    isAuthenticated, \n    isLoading, \n    error, \n    login, \n    logout, \n    getProfile, \n    clearError \n  } = useAuthStore();\n\n  return {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    getProfile,\n    clearError,\n  };\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,+CAA+C;;;;;;AAK/C;AAHA;AACA;;;;;AAgCO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,2EAA2E;QAC3E,eAAe;QACf,2EAA2E;QAC3E,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAC;gBAEvC,IAAI;oBACF,MAAM,SAAS,IAAI;oBACnB,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACvB,MAAM,OAAO,IACb;gBAEpB,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,gBAAgB;QAChB,2EAA2E;QAC3E,QAAQ;YACN,IAAI;gBACF,oHAAA,CAAA,YAAS,CAAC,UAAU;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC,SAAU;gBACR,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,OAAO,MAAM,oHAAA,CAAA,YAAS,CAAC,UAAU;gBAEvC,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACvB,MAAM,OAAO,IACb;gBAEpB,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,2EAA2E;QAC3E,iBAAiB;QACjB,2EAA2E;QAC3E,cAAc;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,SAAS,QAAQ,UAAU;QACpC;QAEA,kBAAkB;YAChB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,SAAS,QAAQ,UAAU;QACpC;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG;YAC/B,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO;YAErC,MAAM,aAAa;YACnB,OAAO,WAAW,IAAI,KAAK;QAC7B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG;YACnC,IAAI,CAAC,sBAAsB,CAAC,MAAM,OAAO;YAEzC,MAAM,iBAAiB;YACvB,OAAO,eAAe,IAAI,KAAK;QACjC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AASG,MAAM,iBAAiB;;IAC5B,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnE,OAAO;QACL,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAElB,2BAA2B;QAC3B,SAAS,QAAQ;QACjB,UAAU,QAAQ;QAClB,aAAa,QAAQ;QAErB,+BAA+B;QAC/B,QAAQ,QAAQ;QAChB,WAAW,QAAQ;QACnB,cAAc,QAAQ;QAEtB,oBAAoB;QACpB,gBAAgB,QAAQ;QACxB,kBAAkB,QAAQ,YAAY,QAAQ;QAC9C,gBAAgB,QAAQ,YAAY,QAAQ;QAC5C,mBAAmB,QAAQ,YAAY,QAAQ,aAAa,QAAQ;QACpE,SAAS,QAAQ,YAAY,QAAQ;QACrC,kBAAkB,QAAQ,YAAY,QAAQ;QAE9C,eAAe;QACf,aAAa;IACf;AACF;GA7Ba;;QACwD;;;AA+B9D,MAAM,UAAU;;IACrB,MAAM,EACJ,IAAI,EACJ,eAAe,EACf,SAAS,EACT,KAAK,EACL,KAAK,EACL,MAAM,EACN,UAAU,EACV,UAAU,EACX,GAAG;IAEJ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IAtBa;;QAUP"}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/layouts/dashboard-layout.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Dashboard Layout\n// Main layout cho dashboard với navigation và authentication\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport {\n  Layout,\n  Menu,\n  Avatar,\n  Dropdown,\n  Typography,\n  Space,\n  Button,\n  Badge,\n  Spin,\n  Alert\n} from 'antd';\nimport {\n  DashboardOutlined,\n  UserOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  SyncOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n  BellOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  ApiOutlined\n} from '@ant-design/icons';\nimport { useAuth, usePermissions } from '@/stores/auth-store';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title, Text } = Typography;\n\n// ============================================================================\n// DASHBOARD LAYOUT COMPONENT\n// ============================================================================\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, isAuthenticated, isLoading, logout, getProfile } = useAuth();\n  const permissions = usePermissions();\n\n  const [collapsed, setCollapsed] = useState(false);\n  const [mounted, setMounted] = useState(false);\n\n  // ========================================================================\n  // EFFECTS\n  // ========================================================================\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  // Check authentication và load profile\n  useEffect(() => {\n    if (!isAuthenticated && !isLoading) {\n      router.push('/auth/login');\n      return;\n    }\n\n    if (isAuthenticated && !user) {\n      getProfile().catch(() => {\n        router.push('/auth/login');\n      });\n    }\n  }, [isAuthenticated, isLoading, user, router, getProfile]);\n\n  // ========================================================================\n  // MENU CONFIGURATION\n  // ========================================================================\n\n  const getMenuItems = () => {\n    const items = [\n      {\n        key: '/dashboard',\n        icon: <DashboardOutlined />,\n        label: 'Dashboard',\n      }\n    ];\n\n    // User Management - chỉ admin\n    if (permissions.canManageUsers) {\n      items.push({\n        key: '/dashboard/users',\n        icon: <UserOutlined />,\n        label: 'User Management',\n      });\n    }\n\n    // Sports Data Management\n    if (permissions.canManageLeagues) {\n      items.push({\n        key: '/dashboard/leagues',\n        icon: <TrophyOutlined />,\n        label: 'Quản lý Leagues',\n      });\n    }\n\n    if (permissions.canManageTeams) {\n      items.push({\n        key: '/dashboard/teams',\n        icon: <TeamOutlined />,\n        label: 'Quản lý Teams',\n      });\n    }\n\n    if (permissions.canManageFixtures) {\n      items.push({\n        key: '/dashboard/fixtures',\n        icon: <CalendarOutlined />,\n        label: 'Quản lý Fixtures',\n      });\n    }\n\n    // Analytics\n    if (permissions.canViewAnalytics) {\n      items.push({\n        key: '/dashboard/analytics',\n        icon: <BarChartOutlined />,\n        label: 'Analytics',\n      });\n    }\n\n    // Sync Operations\n    if (permissions.canSync) {\n      items.push({\n        key: '/dashboard/sync',\n        icon: <SyncOutlined />,\n        label: 'Sync Operations',\n      });\n    }\n\n    return items;\n  };\n\n  // ========================================================================\n  // USER DROPDOWN MENU\n  // ========================================================================\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: 'Thông tin cá nhân',\n      onClick: () => router.push('/dashboard/profile'),\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: 'Cài đặt',\n      onClick: () => router.push('/dashboard/settings'),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: 'Đăng xuất',\n      onClick: () => {\n        logout();\n        router.push('/auth/login');\n      },\n    },\n  ];\n\n  // ========================================================================\n  // HANDLERS\n  // ========================================================================\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    router.push(key);\n  };\n\n  const toggleCollapsed = () => {\n    setCollapsed(!collapsed);\n  };\n\n  // ========================================================================\n  // LOADING STATE\n  // ========================================================================\n\n  if (!mounted || isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Spin size=\"large\" tip=\"Đang tải...\" />\n      </div>\n    );\n  }\n\n  // ========================================================================\n  // UNAUTHENTICATED STATE\n  // ========================================================================\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Alert\n          message=\"Chưa đăng nhập\"\n          description=\"Vui lòng đăng nhập để tiếp tục\"\n          type=\"warning\"\n          showIcon\n        />\n      </div>\n    );\n  }\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <Layout className=\"min-h-screen\">\n      {/* Sidebar */}\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={collapsed}\n        theme=\"dark\"\n        width={256}\n        className=\"shadow-lg\"\n      >\n        {/* Logo */}\n        <div className=\"h-16 flex items-center justify-center border-b border-gray-700\">\n          <Space>\n            <ApiOutlined className=\"text-white text-xl\" />\n            {!collapsed && (\n              <Title level={4} className=\"text-white m-0\">\n                CMS\n              </Title>\n            )}\n          </Space>\n        </div>\n\n        {/* Navigation Menu */}\n        <Menu\n          theme=\"dark\"\n          mode=\"inline\"\n          selectedKeys={[pathname]}\n          items={getMenuItems()}\n          onClick={handleMenuClick}\n          className=\"border-r-0\"\n        />\n      </Sider>\n\n      {/* Main Layout */}\n      <Layout>\n        {/* Header */}\n        <Header className=\"bg-white shadow-sm px-4 flex items-center justify-between\">\n          {/* Left side */}\n          <Space>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={toggleCollapsed}\n              className=\"text-lg\"\n            />\n            <Title level={4} className=\"m-0\">\n              APISportsGame CMS\n            </Title>\n          </Space>\n\n          {/* Right side */}\n          <Space size=\"middle\">\n            {/* Notifications */}\n            <Badge count={0} showZero={false}>\n              <Button\n                type=\"text\"\n                icon={<BellOutlined />}\n                className=\"text-lg\"\n              />\n            </Badge>\n\n            {/* User Info */}\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space className=\"cursor-pointer hover:bg-gray-50 px-2 py-1 rounded\">\n                <Avatar\n                  size=\"small\"\n                  icon={<UserOutlined />}\n                  className=\"bg-blue-500\"\n                />\n                <div className=\"text-left\">\n                  <div className=\"text-sm font-medium\">\n                    {user?.username}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    {permissions.isSystemUser && `${(user as any)?.role}`}\n                    {permissions.isRegisteredUser && `${(user as any)?.tier}`}\n                  </div>\n                </div>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n\n        {/* Content */}\n        <Content className=\"p-6 bg-gray-50 overflow-auto\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n}\n\nexport default DashboardLayout;\n"], "names": [], "mappings": ";;;;;AAEA,uCAAuC;AACvC,6DAA6D;AAE7D;AACA;AA4BA;AA3BA;AAAA;AAYA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;AAYA;AAZA;AAAA;AAYA;AAAA;AAZA;AAYA;AAZA;AAAA;AAAA;AAAA;;;AAPA;;;;;;AAoCA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAU3B,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IAChE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACvE,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,2EAA2E;IAC3E,UAAU;IACV,2EAA2E;IAE3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,WAAW;QACb;oCAAG,EAAE;IAEL,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,WAAW;gBAClC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,mBAAmB,CAAC,MAAM;gBAC5B,aAAa,KAAK;iDAAC;wBACjB,OAAO,IAAI,CAAC;oBACd;;YACF;QACF;oCAAG;QAAC;QAAiB;QAAW;QAAM;QAAQ;KAAW;IAEzD,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,eAAe;QACnB,MAAM,QAAQ;YACZ;gBACE,KAAK;gBACL,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;gBACxB,OAAO;YACT;SACD;QAED,8BAA8B;QAC9B,IAAI,YAAY,cAAc,EAAE;YAC9B,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,yBAAyB;QACzB,IAAI,YAAY,gBAAgB,EAAE;YAChC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;gBACrB,OAAO;YACT;QACF;QAEA,IAAI,YAAY,cAAc,EAAE;YAC9B,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,IAAI,YAAY,iBAAiB,EAAE;YACjC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gBACvB,OAAO;YACT;QACF;QAEA,YAAY;QACZ,IAAI,YAAY,gBAAgB,EAAE;YAChC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gBACvB,OAAO;YACT;QACF;QAEA,kBAAkB;QAClB,IAAI,YAAY,OAAO,EAAE;YACvB,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,SAAS;gBACP;gBACA,OAAO,IAAI,CAAC;YACd;QACF;KACD;IAED,2EAA2E;IAC3E,WAAW;IACX,2EAA2E;IAE3E,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,aAAa,CAAC;IAChB;IAEA,2EAA2E;IAC3E,gBAAgB;IAChB,2EAA2E;IAE3E,IAAI,CAAC,WAAW,WAAW;QACzB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,iLAAA,CAAA,OAAI;gBAAC,MAAK;gBAAQ,KAAI;;;;;;;;;;;IAG7B;IAEA,2EAA2E;IAC3E,wBAAwB;IACxB,2EAA2E;IAE3E,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;;;;;;;;;;;IAIhB;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAU;;0BAEhB,6LAAC;gBACC,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,OAAM;gBACN,OAAO;gBACP,WAAU;;kCAGV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,mNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACtB,CAAC,2BACA,6LAAC;oCAAM,OAAO;oCAAG,WAAU;8CAAiB;;;;;;;;;;;;;;;;;kCAQlD,6LAAC,iLAAA,CAAA,OAAI;wBACH,OAAM;wBACN,MAAK;wBACL,cAAc;4BAAC;yBAAS;wBACxB,OAAO;wBACP,SAAS;wBACT,WAAU;;;;;;;;;;;;0BAKd,6LAAC,qLAAA,CAAA,SAAM;;kCAEL,6LAAC;wBAAO,WAAU;;0CAEhB,6LAAC,mMAAA,CAAA,QAAK;;kDACJ,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,0BAAY,6LAAC,iOAAA,CAAA,qBAAkB;;;;mEAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAEZ,6LAAC;wCAAM,OAAO;wCAAG,WAAU;kDAAM;;;;;;;;;;;;0CAMnC,6LAAC,mMAAA,CAAA,QAAK;gCAAC,MAAK;;kDAEV,6LAAC,mLAAA,CAAA,QAAK;wCAAC,OAAO;wCAAG,UAAU;kDACzB,cAAA,6LAAC,qMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4CACnB,WAAU;;;;;;;;;;;kDAKd,6LAAC,yLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAc;wCAC7B,WAAU;wCACV,KAAK;kDAEL,cAAA,6LAAC,mMAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,6LAAC,qLAAA,CAAA,SAAM;oDACL,MAAK;oDACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oDACnB,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,MAAM;;;;;;sEAET,6LAAC;4DAAI,WAAU;;gEACZ,YAAY,YAAY,IAAI,GAAI,MAAc,MAAM;gEACpD,YAAY,gBAAgB,IAAI,GAAI,MAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrE,6LAAC;wBAAQ,WAAU;kCAChB;;;;;;;;;;;;;;;;;;AAKX;GA9QgB;;QACC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACqC,iIAAA,CAAA,UAAO;QACpD,iIAAA,CAAA,iBAAc;;;KAJpB;uCAgRD"}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/tables/user-table.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - User Table Component\n// Reusable table cho SystemUser và RegisteredUser management\n\nimport React, { useState } from 'react';\nimport {\n  Table,\n  Tag,\n  Space,\n  Button,\n  Dropdown,\n  Avatar,\n  Typography,\n  Tooltip,\n  Modal,\n  message,\n  Input,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Card\n} from 'antd';\nimport {\n  UserOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  MoreOutlined,\n  SearchOutlined,\n  FilterOutlined,\n  PlusOutlined,\n  ExportOutlined,\n  ReloadOutlined\n} from '@ant-design/icons';\nimport { SystemUser, RegisteredUser, UserFilters } from '@/types';\nimport dayjs from 'dayjs';\n\nconst { Text } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\ninterface UserTableProps {\n  users: (SystemUser | RegisteredUser)[];\n  loading?: boolean;\n  userType: 'system' | 'registered';\n  onEdit?: (user: SystemUser | RegisteredUser) => void;\n  onDelete?: (user: SystemUser | RegisteredUser) => void;\n  onAdd?: () => void;\n  onRefresh?: () => void;\n  onExport?: () => void;\n  pagination?: {\n    current: number;\n    pageSize: number;\n    total: number;\n    onChange: (page: number, pageSize: number) => void;\n  };\n  filters?: UserFilters;\n  onFiltersChange?: (filters: UserFilters) => void;\n}\n\n// ============================================================================\n// USER TABLE COMPONENT\n// ============================================================================\n\nexport function UserTable({\n  users,\n  loading = false,\n  userType,\n  onEdit,\n  onDelete,\n  onAdd,\n  onRefresh,\n  onExport,\n  pagination,\n  filters = {},\n  onFiltersChange\n}: UserTableProps) {\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [showFilters, setShowFilters] = useState(false);\n\n  // ========================================================================\n  // HELPER FUNCTIONS\n  // ========================================================================\n\n  const isSystemUser = (user: SystemUser | RegisteredUser): user is SystemUser => {\n    return 'role' in user;\n  };\n\n  const isRegisteredUser = (user: SystemUser | RegisteredUser): user is RegisteredUser => {\n    return 'tier' in user;\n  };\n\n  // ========================================================================\n  // COLUMN DEFINITIONS\n  // ========================================================================\n\n  const getColumns = () => {\n    const baseColumns = [\n      {\n        title: 'User',\n        dataIndex: 'username',\n        key: 'username',\n        render: (username: string, record: SystemUser | RegisteredUser) => (\n          <Space>\n            <Avatar size=\"small\" icon={<UserOutlined />} />\n            <div>\n              <div className=\"font-medium\">{username}</div>\n              <Text type=\"secondary\" className=\"text-xs\">\n                {record.email}\n              </Text>\n            </div>\n          </Space>\n        ),\n        sorter: true,\n      },\n      {\n        title: userType === 'system' ? 'Role' : 'Tier',\n        dataIndex: userType === 'system' ? 'role' : 'tier',\n        key: userType === 'system' ? 'role' : 'tier',\n        render: (value: string) => {\n          const colors = {\n            // System roles\n            admin: 'red',\n            editor: 'blue',\n            moderator: 'green',\n            // User tiers\n            free: 'default',\n            premium: 'gold',\n            enterprise: 'purple',\n          };\n          return (\n            <Tag color={colors[value as keyof typeof colors] || 'default'}>\n              {value.toUpperCase()}\n            </Tag>\n          );\n        },\n        filters: userType === 'system' \n          ? [\n              { text: 'Admin', value: 'admin' },\n              { text: 'Editor', value: 'editor' },\n              { text: 'Moderator', value: 'moderator' },\n            ]\n          : [\n              { text: 'Free', value: 'free' },\n              { text: 'Premium', value: 'premium' },\n              { text: 'Enterprise', value: 'enterprise' },\n            ],\n      },\n      {\n        title: 'Status',\n        dataIndex: 'isActive',\n        key: 'isActive',\n        render: (isActive: boolean, record: SystemUser | RegisteredUser) => (\n          <Space direction=\"vertical\" size=\"small\">\n            <Tag color={isActive ? 'success' : 'error'}>\n              {isActive ? 'Active' : 'Inactive'}\n            </Tag>\n            {isRegisteredUser(record) && (\n              <Tag color={record.isEmailVerified ? 'success' : 'warning'} className=\"text-xs\">\n                {record.isEmailVerified ? 'Verified' : 'Unverified'}\n              </Tag>\n            )}\n          </Space>\n        ),\n        filters: [\n          { text: 'Active', value: true },\n          { text: 'Inactive', value: false },\n        ],\n      }\n    ];\n\n    // Add API usage column for RegisteredUser\n    if (userType === 'registered') {\n      baseColumns.push({\n        title: 'API Usage',\n        key: 'apiUsage',\n        render: (_, record: RegisteredUser) => {\n          const percentage = record.apiCallsLimit \n            ? (record.apiCallsUsed / record.apiCallsLimit) * 100 \n            : 0;\n          \n          return (\n            <div>\n              <div className=\"text-sm\">\n                {record.apiCallsUsed.toLocaleString()} / {record.apiCallsLimit?.toLocaleString() || '∞'}\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-1 mt-1\">\n                <div \n                  className={`h-1 rounded-full ${\n                    percentage > 90 ? 'bg-red-500' : \n                    percentage > 70 ? 'bg-yellow-500' : 'bg-green-500'\n                  }`}\n                  style={{ width: `${Math.min(percentage, 100)}%` }}\n                />\n              </div>\n            </div>\n          );\n        },\n      });\n    }\n\n    // Add common columns\n    baseColumns.push(\n      {\n        title: 'Last Login',\n        dataIndex: 'lastLoginAt',\n        key: 'lastLoginAt',\n        render: (date: string) => (\n          <Tooltip title={dayjs(date).format('YYYY-MM-DD HH:mm:ss')}>\n            <Text type=\"secondary\" className=\"text-sm\">\n              {dayjs(date).fromNow()}\n            </Text>\n          </Tooltip>\n        ),\n        sorter: true,\n      },\n      {\n        title: 'Created',\n        dataIndex: 'createdAt',\n        key: 'createdAt',\n        render: (date: string) => (\n          <Text type=\"secondary\" className=\"text-sm\">\n            {dayjs(date).format('MMM DD, YYYY')}\n          </Text>\n        ),\n        sorter: true,\n      },\n      {\n        title: 'Actions',\n        key: 'actions',\n        width: 120,\n        render: (_, record: SystemUser | RegisteredUser) => (\n          <Space>\n            <Tooltip title=\"Edit\">\n              <Button\n                type=\"text\"\n                size=\"small\"\n                icon={<EditOutlined />}\n                onClick={() => onEdit?.(record)}\n              />\n            </Tooltip>\n            <Dropdown\n              menu={{\n                items: [\n                  {\n                    key: 'edit',\n                    label: 'Edit User',\n                    icon: <EditOutlined />,\n                    onClick: () => onEdit?.(record),\n                  },\n                  {\n                    key: 'delete',\n                    label: 'Delete User',\n                    icon: <DeleteOutlined />,\n                    danger: true,\n                    onClick: () => handleDelete(record),\n                  },\n                ],\n              }}\n              trigger={['click']}\n            >\n              <Button type=\"text\" size=\"small\" icon={<MoreOutlined />} />\n            </Dropdown>\n          </Space>\n        ),\n      }\n    );\n\n    return baseColumns;\n  };\n\n  // ========================================================================\n  // EVENT HANDLERS\n  // ========================================================================\n\n  const handleDelete = (user: SystemUser | RegisteredUser) => {\n    Modal.confirm({\n      title: 'Delete User',\n      content: `Are you sure you want to delete user \"${user.username}\"?`,\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: () => {\n        onDelete?.(user);\n        message.success('User deleted successfully');\n      },\n    });\n  };\n\n  const handleBulkDelete = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('Please select users to delete');\n      return;\n    }\n\n    Modal.confirm({\n      title: 'Delete Selected Users',\n      content: `Are you sure you want to delete ${selectedRowKeys.length} selected users?`,\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: () => {\n        // Handle bulk delete\n        setSelectedRowKeys([]);\n        message.success(`${selectedRowKeys.length} users deleted successfully`);\n      },\n    });\n  };\n\n  const handleFilterChange = (key: keyof UserFilters, value: any) => {\n    const newFilters = { ...filters, [key]: value };\n    onFiltersChange?.(newFilters);\n  };\n\n  // ========================================================================\n  // ROW SELECTION\n  // ========================================================================\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: setSelectedRowKeys,\n    getCheckboxProps: (record: SystemUser | RegisteredUser) => ({\n      disabled: false,\n      name: record.username,\n    }),\n  };\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Header Actions */}\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Search\n                placeholder={`Search ${userType} users...`}\n                allowClear\n                style={{ width: 300 }}\n                value={filters.search}\n                onChange={(e) => handleFilterChange('search', e.target.value)}\n                onSearch={(value) => handleFilterChange('search', value)}\n              />\n              <Button\n                icon={<FilterOutlined />}\n                onClick={() => setShowFilters(!showFilters)}\n              >\n                Filters\n              </Button>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              {selectedRowKeys.length > 0 && (\n                <Button\n                  danger\n                  icon={<DeleteOutlined />}\n                  onClick={handleBulkDelete}\n                >\n                  Delete ({selectedRowKeys.length})\n                </Button>\n              )}\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={onRefresh}\n                loading={loading}\n              >\n                Refresh\n              </Button>\n              <Button\n                icon={<ExportOutlined />}\n                onClick={onExport}\n              >\n                Export\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={onAdd}\n              >\n                Add {userType === 'system' ? 'System User' : 'User'}\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        {/* Advanced Filters */}\n        {showFilters && (\n          <div className=\"mt-4 p-4 bg-gray-50 rounded\">\n            <Row gutter={16}>\n              <Col span={6}>\n                <Select\n                  placeholder={userType === 'system' ? 'Select Role' : 'Select Tier'}\n                  allowClear\n                  style={{ width: '100%' }}\n                  value={userType === 'system' ? filters.role : filters.tier}\n                  onChange={(value) => \n                    handleFilterChange(userType === 'system' ? 'role' : 'tier', value)\n                  }\n                >\n                  {userType === 'system' ? (\n                    <>\n                      <Option value=\"admin\">Admin</Option>\n                      <Option value=\"editor\">Editor</Option>\n                      <Option value=\"moderator\">Moderator</Option>\n                    </>\n                  ) : (\n                    <>\n                      <Option value=\"free\">Free</Option>\n                      <Option value=\"premium\">Premium</Option>\n                      <Option value=\"enterprise\">Enterprise</Option>\n                    </>\n                  )}\n                </Select>\n              </Col>\n              <Col span={6}>\n                <Select\n                  placeholder=\"Select Status\"\n                  allowClear\n                  style={{ width: '100%' }}\n                  value={filters.isActive}\n                  onChange={(value) => handleFilterChange('isActive', value)}\n                >\n                  <Option value={true}>Active</Option>\n                  <Option value={false}>Inactive</Option>\n                </Select>\n              </Col>\n              <Col span={6}>\n                <DatePicker\n                  placeholder=\"Created From\"\n                  style={{ width: '100%' }}\n                  onChange={(date) => handleFilterChange('createdFrom', date?.toISOString())}\n                />\n              </Col>\n              <Col span={6}>\n                <DatePicker\n                  placeholder=\"Created To\"\n                  style={{ width: '100%' }}\n                  onChange={(date) => handleFilterChange('createdTo', date?.toISOString())}\n                />\n              </Col>\n            </Row>\n          </div>\n        )}\n      </Card>\n\n      {/* Data Table */}\n      <Card>\n        <Table\n          rowSelection={rowSelection}\n          columns={getColumns()}\n          dataSource={users}\n          loading={loading}\n          pagination={pagination ? {\n            current: pagination.current,\n            pageSize: pagination.pageSize,\n            total: pagination.total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `${range[0]}-${range[1]} of ${total} users`,\n            onChange: pagination.onChange,\n          } : false}\n          rowKey=\"id\"\n          scroll={{ x: 1200 }}\n          size=\"middle\"\n        />\n      </Card>\n    </div>\n  );\n}\n\nexport default UserTable;\n"], "names": [], "mappings": ";;;;;AAEA,2CAA2C;AAC3C,6DAA6D;AAE7D;AA+BA;AA9BA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAlBA;AAAA;AAAA;AAkBA;AAlBA;AAkBA;AAAA;AAlBA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAAA;AAAA;AAAA;AAlBA;AAAA;;;AANA;;;;;AAsCA,MAAM,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,mLAAA,CAAA,QAAK;AACxB,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AA6BlB,SAAS,UAAU,EACxB,KAAK,EACL,UAAU,KAAK,EACf,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACL,SAAS,EACT,QAAQ,EACR,UAAU,EACV,UAAU,CAAC,CAAC,EACZ,eAAe,EACA;;IACf,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAE3E,MAAM,eAAe,CAAC;QACpB,OAAO,UAAU;IACnB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,UAAU;IACnB;IAEA,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,aAAa;QACjB,MAAM,cAAc;YAClB;gBACE,OAAO;gBACP,WAAW;gBACX,KAAK;gBACL,QAAQ,CAAC,UAAkB,uBACzB,6LAAC,mMAAA,CAAA,QAAK;;0CACJ,6LAAC,qLAAA,CAAA,SAAM;gCAAC,MAAK;gCAAQ,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;0CACxC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAe;;;;;;kDAC9B,6LAAC;wCAAK,MAAK;wCAAY,WAAU;kDAC9B,OAAO,KAAK;;;;;;;;;;;;;;;;;;gBAKrB,QAAQ;YACV;YACA;gBACE,OAAO,aAAa,WAAW,SAAS;gBACxC,WAAW,aAAa,WAAW,SAAS;gBAC5C,KAAK,aAAa,WAAW,SAAS;gBACtC,QAAQ,CAAC;oBACP,MAAM,SAAS;wBACb,eAAe;wBACf,OAAO;wBACP,QAAQ;wBACR,WAAW;wBACX,aAAa;wBACb,MAAM;wBACN,SAAS;wBACT,YAAY;oBACd;oBACA,qBACE,6LAAC,+KAAA,CAAA,MAAG;wBAAC,OAAO,MAAM,CAAC,MAA6B,IAAI;kCACjD,MAAM,WAAW;;;;;;gBAGxB;gBACA,SAAS,aAAa,WAClB;oBACE;wBAAE,MAAM;wBAAS,OAAO;oBAAQ;oBAChC;wBAAE,MAAM;wBAAU,OAAO;oBAAS;oBAClC;wBAAE,MAAM;wBAAa,OAAO;oBAAY;iBACzC,GACD;oBACE;wBAAE,MAAM;wBAAQ,OAAO;oBAAO;oBAC9B;wBAAE,MAAM;wBAAW,OAAO;oBAAU;oBACpC;wBAAE,MAAM;wBAAc,OAAO;oBAAa;iBAC3C;YACP;YACA;gBACE,OAAO;gBACP,WAAW;gBACX,KAAK;gBACL,QAAQ,CAAC,UAAmB,uBAC1B,6LAAC,mMAAA,CAAA,QAAK;wBAAC,WAAU;wBAAW,MAAK;;0CAC/B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,OAAO,WAAW,YAAY;0CAChC,WAAW,WAAW;;;;;;4BAExB,iBAAiB,yBAChB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,OAAO,OAAO,eAAe,GAAG,YAAY;gCAAW,WAAU;0CACnE,OAAO,eAAe,GAAG,aAAa;;;;;;;;;;;;gBAK/C,SAAS;oBACP;wBAAE,MAAM;wBAAU,OAAO;oBAAK;oBAC9B;wBAAE,MAAM;wBAAY,OAAO;oBAAM;iBAClC;YACH;SACD;QAED,0CAA0C;QAC1C,IAAI,aAAa,cAAc;YAC7B,YAAY,IAAI,CAAC;gBACf,OAAO;gBACP,KAAK;gBACL,QAAQ,CAAC,GAAG;oBACV,MAAM,aAAa,OAAO,aAAa,GACnC,AAAC,OAAO,YAAY,GAAG,OAAO,aAAa,GAAI,MAC/C;oBAEJ,qBACE,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;oCACZ,OAAO,YAAY,CAAC,cAAc;oCAAG;oCAAI,OAAO,aAAa,EAAE,oBAAoB;;;;;;;0CAEtF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAW,CAAC,iBAAiB,EAC3B,aAAa,KAAK,eAClB,aAAa,KAAK,kBAAkB,gBACpC;oCACF,OAAO;wCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC;oCAAC;;;;;;;;;;;;;;;;;gBAK1D;YACF;QACF;QAEA,qBAAqB;QACrB,YAAY,IAAI,CACd;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,qBACP,6LAAC,uLAAA,CAAA,UAAO;oBAAC,OAAO,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;8BACjC,cAAA,6LAAC;wBAAK,MAAK;wBAAY,WAAU;kCAC9B,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,OAAO;;;;;;;;;;;YAI1B,QAAQ;QACV,GACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,qBACP,6LAAC;oBAAK,MAAK;oBAAY,WAAU;8BAC9B,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;;;;;;YAGxB,QAAQ;QACV,GACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,6LAAC,mMAAA,CAAA,QAAK;;sCACJ,6LAAC,uLAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gCACnB,SAAS,IAAM,SAAS;;;;;;;;;;;sCAG5B,6LAAC,yLAAA,CAAA,WAAQ;4BACP,MAAM;gCACJ,OAAO;oCACL;wCACE,KAAK;wCACL,OAAO;wCACP,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACnB,SAAS,IAAM,SAAS;oCAC1B;oCACA;wCACE,KAAK;wCACL,OAAO;wCACP,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;wCACrB,QAAQ;wCACR,SAAS,IAAM,aAAa;oCAC9B;iCACD;4BACH;4BACA,SAAS;gCAAC;6BAAQ;sCAElB,cAAA,6LAAC,qMAAA,CAAA,SAAM;gCAAC,MAAK;gCAAO,MAAK;gCAAQ,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;QAI5D;QAGF,OAAO;IACT;IAEA,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,eAAe,CAAC;QACpB,mLAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,sCAAsC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;YACnE,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,WAAW;gBACX,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,mLAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,gCAAgC,EAAE,gBAAgB,MAAM,CAAC,gBAAgB,CAAC;YACpF,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,qBAAqB;gBACrB,mBAAmB,EAAE;gBACrB,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,gBAAgB,MAAM,CAAC,2BAA2B,CAAC;YACxE;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC,KAAwB;QAClD,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,CAAC,IAAI,EAAE;QAAM;QAC9C,kBAAkB;IACpB;IAEA,2EAA2E;IAC3E,gBAAgB;IAChB,2EAA2E;IAE3E,MAAM,eAAe;QACnB;QACA,UAAU;QACV,kBAAkB,CAAC,SAAwC,CAAC;gBAC1D,UAAU;gBACV,MAAM,OAAO,QAAQ;YACvB,CAAC;IACH;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,iLAAA,CAAA,OAAI;;kCACH,6LAAC,+KAAA,CAAA,MAAG;wBAAC,SAAQ;wBAAgB,OAAM;;0CACjC,6LAAC,+KAAA,CAAA,MAAG;0CACF,cAAA,6LAAC,mMAAA,CAAA,QAAK;;sDACJ,6LAAC;4CACC,aAAa,CAAC,OAAO,EAAE,SAAS,SAAS,CAAC;4CAC1C,UAAU;4CACV,OAAO;gDAAE,OAAO;4CAAI;4CACpB,OAAO,QAAQ,MAAM;4CACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;4CAC5D,UAAU,CAAC,QAAU,mBAAmB,UAAU;;;;;;sDAEpD,6LAAC,qMAAA,CAAA,SAAM;4CACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS,IAAM,eAAe,CAAC;sDAChC;;;;;;;;;;;;;;;;;0CAKL,6LAAC,+KAAA,CAAA,MAAG;0CACF,cAAA,6LAAC,mMAAA,CAAA,QAAK;;wCACH,gBAAgB,MAAM,GAAG,mBACxB,6LAAC,qMAAA,CAAA,SAAM;4CACL,MAAM;4CACN,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS;;gDACV;gDACU,gBAAgB,MAAM;gDAAC;;;;;;;sDAGpC,6LAAC,qMAAA,CAAA,SAAM;4CACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS;4CACT,SAAS;sDACV;;;;;;sDAGD,6LAAC,qMAAA,CAAA,SAAM;4CACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4CACrB,SAAS;sDACV;;;;;;sDAGD,6LAAC,qMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4CACnB,SAAS;;gDACV;gDACM,aAAa,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;oBAOpD,6BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAa,aAAa,WAAW,gBAAgB;wCACrD,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAO;wCACvB,OAAO,aAAa,WAAW,QAAQ,IAAI,GAAG,QAAQ,IAAI;wCAC1D,UAAU,CAAC,QACT,mBAAmB,aAAa,WAAW,SAAS,QAAQ;kDAG7D,aAAa,yBACZ;;8DACE,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;;yEAG5B;;8DACE,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAa;;;;;;;;;;;;;;;;;;8CAKnC,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAO;wCACvB,OAAO,QAAQ,QAAQ;wCACvB,UAAU,CAAC,QAAU,mBAAmB,YAAY;;0DAEpD,6LAAC;gDAAO,OAAO;0DAAM;;;;;;0DACrB,6LAAC;gDAAO,OAAO;0DAAO;;;;;;;;;;;;;;;;;8CAG1B,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iMAAA,CAAA,aAAU;wCACT,aAAY;wCACZ,OAAO;4CAAE,OAAO;wCAAO;wCACvB,UAAU,CAAC,OAAS,mBAAmB,eAAe,MAAM;;;;;;;;;;;8CAGhE,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iMAAA,CAAA,aAAU;wCACT,aAAY;wCACZ,OAAO;4CAAE,OAAO;wCAAO;wCACvB,UAAU,CAAC,OAAS,mBAAmB,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStE,6LAAC,iLAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mLAAA,CAAA,QAAK;oBACJ,cAAc;oBACd,SAAS;oBACT,YAAY;oBACZ,SAAS;oBACT,YAAY,aAAa;wBACvB,SAAS,WAAW,OAAO;wBAC3B,UAAU,WAAW,QAAQ;wBAC7B,OAAO,WAAW,KAAK;wBACvB,iBAAiB;wBACjB,iBAAiB;wBACjB,WAAW,CAAC,OAAO,QACjB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC;wBAC7C,UAAU,WAAW,QAAQ;oBAC/B,IAAI;oBACJ,QAAO;oBACP,QAAQ;wBAAE,GAAG;oBAAK;oBAClB,MAAK;;;;;;;;;;;;;;;;;AAKf;GAzZgB;KAAA;uCA2ZD"}}, {"offset": {"line": 1744, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1750, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/components/forms/user-form.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - User Form Component\n// Reusable form cho create/edit SystemUser và RegisteredUser\n\nimport React, { useEffect } from 'react';\nimport {\n  Form,\n  Input,\n  Select,\n  Switch,\n  Button,\n  Space,\n  Card,\n  Row,\n  Col,\n  Typography,\n  Divider,\n  InputNumber,\n  DatePicker,\n  Alert\n} from 'antd';\nimport {\n  UserOutlined,\n  MailOutlined,\n  LockOutlined,\n  SaveOutlined,\n  CloseOutlined\n} from '@ant-design/icons';\nimport { SystemUser, RegisteredUser, CreateSystemUserForm } from '@/types';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\ninterface UserFormProps {\n  userType: 'system' | 'registered';\n  mode: 'create' | 'edit';\n  initialValues?: Partial<SystemUser | RegisteredUser>;\n  loading?: boolean;\n  onSubmit: (values: any) => void;\n  onCancel: () => void;\n}\n\n// ============================================================================\n// USER FORM COMPONENT\n// ============================================================================\n\nexport function UserForm({\n  userType,\n  mode,\n  initialValues,\n  loading = false,\n  onSubmit,\n  onCancel\n}: UserFormProps) {\n  const [form] = Form.useForm();\n\n  // ========================================================================\n  // EFFECTS\n  // ========================================================================\n\n  useEffect(() => {\n    if (initialValues) {\n      // Convert dates for form\n      const formValues = {\n        ...initialValues,\n        subscriptionEndDate: initialValues.subscriptionEndDate \n          ? dayjs(initialValues.subscriptionEndDate) \n          : null,\n      };\n      form.setFieldsValue(formValues);\n    }\n  }, [initialValues, form]);\n\n  // ========================================================================\n  // FORM VALIDATION RULES\n  // ========================================================================\n\n  const validationRules = {\n    username: [\n      { required: true, message: 'Username is required' },\n      { min: 3, message: 'Username must be at least 3 characters' },\n      { max: 50, message: 'Username must not exceed 50 characters' },\n      { pattern: /^[a-zA-Z0-9_]+$/, message: 'Username can only contain letters, numbers, and underscores' }\n    ],\n    email: [\n      { required: true, message: 'Email is required' },\n      { type: 'email' as const, message: 'Please enter a valid email' }\n    ],\n    password: mode === 'create' ? [\n      { required: true, message: 'Password is required' },\n      { min: 6, message: 'Password must be at least 6 characters' },\n      { max: 100, message: 'Password must not exceed 100 characters' }\n    ] : [\n      { min: 6, message: 'Password must be at least 6 characters' },\n      { max: 100, message: 'Password must not exceed 100 characters' }\n    ],\n    role: [\n      { required: true, message: 'Role is required' }\n    ],\n    tier: [\n      { required: true, message: 'Tier is required' }\n    ]\n  };\n\n  // ========================================================================\n  // EVENT HANDLERS\n  // ========================================================================\n\n  const handleSubmit = async (values: any) => {\n    try {\n      // Process form values\n      const processedValues = {\n        ...values,\n        subscriptionEndDate: values.subscriptionEndDate?.toISOString(),\n      };\n\n      // Remove password if empty in edit mode\n      if (mode === 'edit' && !processedValues.password) {\n        delete processedValues.password;\n      }\n\n      onSubmit(processedValues);\n    } catch (error) {\n      console.error('Form submission error:', error);\n    }\n  };\n\n  const handleReset = () => {\n    form.resetFields();\n  };\n\n  // ========================================================================\n  // RENDER HELPERS\n  // ========================================================================\n\n  const renderSystemUserFields = () => (\n    <>\n      <Col span={24}>\n        <Form.Item\n          name=\"role\"\n          label=\"Role\"\n          rules={validationRules.role}\n        >\n          <Select placeholder=\"Select user role\">\n            <Option value=\"admin\">\n              <Space>\n                <span>👑</span>\n                <div>\n                  <div>Admin</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Full system access\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n            <Option value=\"editor\">\n              <Space>\n                <span>✏️</span>\n                <div>\n                  <div>Editor</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Content management\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n            <Option value=\"moderator\">\n              <Space>\n                <span>🛡️</span>\n                <div>\n                  <div>Moderator</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Content moderation\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n          </Select>\n        </Form.Item>\n      </Col>\n    </>\n  );\n\n  const renderRegisteredUserFields = () => (\n    <>\n      <Col span={12}>\n        <Form.Item\n          name=\"tier\"\n          label=\"Subscription Tier\"\n          rules={validationRules.tier}\n        >\n          <Select placeholder=\"Select subscription tier\">\n            <Option value=\"free\">\n              <Space>\n                <span>🆓</span>\n                <div>\n                  <div>Free</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Basic features\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n            <Option value=\"premium\">\n              <Space>\n                <span>⭐</span>\n                <div>\n                  <div>Premium</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Enhanced features\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n            <Option value=\"enterprise\">\n              <Space>\n                <span>🏢</span>\n                <div>\n                  <div>Enterprise</div>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Full features\n                  </Text>\n                </div>\n              </Space>\n            </Option>\n          </Select>\n        </Form.Item>\n      </Col>\n\n      <Col span={12}>\n        <Form.Item\n          name=\"isEmailVerified\"\n          label=\"Email Verified\"\n          valuePropName=\"checked\"\n        >\n          <Switch />\n        </Form.Item>\n      </Col>\n\n      <Col span={12}>\n        <Form.Item\n          name=\"apiCallsUsed\"\n          label=\"API Calls Used\"\n        >\n          <InputNumber\n            min={0}\n            style={{ width: '100%' }}\n            formatter={value => `${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n            parser={value => value!.replace(/\\$\\s?|(,*)/g, '')}\n          />\n        </Form.Item>\n      </Col>\n\n      <Col span={12}>\n        <Form.Item\n          name=\"apiCallsLimit\"\n          label=\"API Calls Limit\"\n        >\n          <InputNumber\n            min={0}\n            style={{ width: '100%' }}\n            formatter={value => `${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n            parser={value => value!.replace(/\\$\\s?|(,*)/g, '')}\n            placeholder=\"Leave empty for unlimited\"\n          />\n        </Form.Item>\n      </Col>\n\n      <Col span={24}>\n        <Form.Item\n          name=\"subscriptionEndDate\"\n          label=\"Subscription End Date\"\n        >\n          <DatePicker\n            style={{ width: '100%' }}\n            showTime\n            format=\"YYYY-MM-DD HH:mm:ss\"\n            placeholder=\"Select subscription end date\"\n          />\n        </Form.Item>\n      </Col>\n    </>\n  );\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <Title level={4}>\n          {mode === 'create' ? 'Create' : 'Edit'} {userType === 'system' ? 'System User' : 'Registered User'}\n        </Title>\n        <Text type=\"secondary\">\n          {mode === 'create' \n            ? `Add a new ${userType} user to the system`\n            : `Update ${userType} user information`\n          }\n        </Text>\n      </div>\n\n      {/* Info Alert */}\n      <Alert\n        message={`${userType === 'system' ? 'System User' : 'Registered User'} Information`}\n        description={\n          userType === 'system' \n            ? 'System users have administrative access to the CMS. Choose roles carefully.'\n            : 'Registered users are API consumers with different subscription tiers and usage limits.'\n        }\n        type=\"info\"\n        showIcon\n        className=\"mb-6\"\n      />\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleSubmit}\n        autoComplete=\"off\"\n        size=\"large\"\n      >\n        <Row gutter={16}>\n          {/* Basic Information */}\n          <Col span={24}>\n            <Divider orientation=\"left\">Basic Information</Divider>\n          </Col>\n\n          <Col span={12}>\n            <Form.Item\n              name=\"username\"\n              label=\"Username\"\n              rules={validationRules.username}\n            >\n              <Input\n                prefix={<UserOutlined />}\n                placeholder=\"Enter username\"\n                disabled={mode === 'edit'}\n              />\n            </Form.Item>\n          </Col>\n\n          <Col span={12}>\n            <Form.Item\n              name=\"email\"\n              label=\"Email\"\n              rules={validationRules.email}\n            >\n              <Input\n                prefix={<MailOutlined />}\n                placeholder=\"Enter email address\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col span={24}>\n            <Form.Item\n              name=\"password\"\n              label={mode === 'create' ? 'Password' : 'New Password (leave empty to keep current)'}\n              rules={validationRules.password}\n            >\n              <Input.Password\n                prefix={<LockOutlined />}\n                placeholder={mode === 'create' ? 'Enter password' : 'Enter new password'}\n              />\n            </Form.Item>\n          </Col>\n\n          {/* Role/Tier Specific Fields */}\n          <Col span={24}>\n            <Divider orientation=\"left\">\n              {userType === 'system' ? 'Role & Permissions' : 'Subscription & Usage'}\n            </Divider>\n          </Col>\n\n          {userType === 'system' ? renderSystemUserFields() : renderRegisteredUserFields()}\n\n          {/* Status */}\n          <Col span={24}>\n            <Divider orientation=\"left\">Status</Divider>\n          </Col>\n\n          <Col span={12}>\n            <Form.Item\n              name=\"isActive\"\n              label=\"Active Status\"\n              valuePropName=\"checked\"\n              initialValue={true}\n            >\n              <Switch />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        {/* Form Actions */}\n        <Divider />\n        <Row justify=\"end\">\n          <Col>\n            <Space>\n              <Button onClick={handleReset}>\n                Reset\n              </Button>\n              <Button onClick={onCancel}>\n                <CloseOutlined />\n                Cancel\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                icon={<SaveOutlined />}\n              >\n                {mode === 'create' ? 'Create User' : 'Update User'}\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Form>\n    </Card>\n  );\n}\n\nexport default UserForm;\n"], "names": [], "mappings": ";;;;;AAEA,0CAA0C;AAC1C,6DAA6D;AAE7D;AAyBA;AAxBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AAAA;AAhBA;AAgBA;AAAA;;;AAtBA;;;;;AAgCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAmBnB,SAAS,SAAS,EACvB,QAAQ,EACR,IAAI,EACJ,aAAa,EACb,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACM;;IACd,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,2EAA2E;IAC3E,UAAU;IACV,2EAA2E;IAE3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,eAAe;gBACjB,yBAAyB;gBACzB,MAAM,aAAa;oBACjB,GAAG,aAAa;oBAChB,qBAAqB,cAAc,mBAAmB,GAClD,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE,cAAc,mBAAmB,IACvC;gBACN;gBACA,KAAK,cAAc,CAAC;YACtB;QACF;6BAAG;QAAC;QAAe;KAAK;IAExB,2EAA2E;IAC3E,wBAAwB;IACxB,2EAA2E;IAE3E,MAAM,kBAAkB;QACtB,UAAU;YACR;gBAAE,UAAU;gBAAM,SAAS;YAAuB;YAClD;gBAAE,KAAK;gBAAG,SAAS;YAAyC;YAC5D;gBAAE,KAAK;gBAAI,SAAS;YAAyC;YAC7D;gBAAE,SAAS;gBAAmB,SAAS;YAA8D;SACtG;QACD,OAAO;YACL;gBAAE,UAAU;gBAAM,SAAS;YAAoB;YAC/C;gBAAE,MAAM;gBAAkB,SAAS;YAA6B;SACjE;QACD,UAAU,SAAS,WAAW;YAC5B;gBAAE,UAAU;gBAAM,SAAS;YAAuB;YAClD;gBAAE,KAAK;gBAAG,SAAS;YAAyC;YAC5D;gBAAE,KAAK;gBAAK,SAAS;YAA0C;SAChE,GAAG;YACF;gBAAE,KAAK;gBAAG,SAAS;YAAyC;YAC5D;gBAAE,KAAK;gBAAK,SAAS;YAA0C;SAChE;QACD,MAAM;YACJ;gBAAE,UAAU;gBAAM,SAAS;YAAmB;SAC/C;QACD,MAAM;YACJ;gBAAE,UAAU;gBAAM,SAAS;YAAmB;SAC/C;IACH;IAEA,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,sBAAsB;YACtB,MAAM,kBAAkB;gBACtB,GAAG,MAAM;gBACT,qBAAqB,OAAO,mBAAmB,EAAE;YACnD;YAEA,wCAAwC;YACxC,IAAI,SAAS,UAAU,CAAC,gBAAgB,QAAQ,EAAE;gBAChD,OAAO,gBAAgB,QAAQ;YACjC;YAEA,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,cAAc;QAClB,KAAK,WAAW;IAClB;IAEA,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,yBAAyB,kBAC7B;sBACE,cAAA,6LAAC,+KAAA,CAAA,MAAG;gBAAC,MAAM;0BACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAM;oBACN,OAAO,gBAAgB,IAAI;8BAE3B,cAAA,6LAAC,qLAAA,CAAA,SAAM;wBAAC,aAAY;;0CAClB,6LAAC;gCAAO,OAAM;0CACZ,cAAA,6LAAC,mMAAA,CAAA,QAAK;;sDACJ,6LAAC;sDAAK;;;;;;sDACN,6LAAC;;8DACC,6LAAC;8DAAI;;;;;;8DACL,6LAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC;gCAAO,OAAM;0CACZ,cAAA,6LAAC,mMAAA,CAAA,QAAK;;sDACJ,6LAAC;sDAAK;;;;;;sDACN,6LAAC;;8DACC,6LAAC;8DAAI;;;;;;8DACL,6LAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC;gCAAO,OAAM;0CACZ,cAAA,6LAAC,mMAAA,CAAA,QAAK;;sDACJ,6LAAC;sDAAK;;;;;;sDACN,6LAAC;;8DACC,6LAAC;8DAAI;;;;;;8DACL,6LAAC;oDAAK,MAAK;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAY3D,MAAM,6BAA6B,kBACjC;;8BACE,6LAAC,+KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;wBACN,OAAO,gBAAgB,IAAI;kCAE3B,cAAA,6LAAC,qLAAA,CAAA,SAAM;4BAAC,aAAY;;8CAClB,6LAAC;oCAAO,OAAM;8CACZ,cAAA,6LAAC,mMAAA,CAAA,QAAK;;0DACJ,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;kEACC,6LAAC;kEAAI;;;;;;kEACL,6LAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMjD,6LAAC;oCAAO,OAAM;8CACZ,cAAA,6LAAC,mMAAA,CAAA,QAAK;;0DACJ,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;kEACC,6LAAC;kEAAI;;;;;;kEACL,6LAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMjD,6LAAC;oCAAO,OAAM;8CACZ,cAAA,6LAAC,mMAAA,CAAA,QAAK;;0DACJ,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;kEACC,6LAAC;kEAAI;;;;;;kEACL,6LAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUvD,6LAAC,+KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;wBACN,eAAc;kCAEd,cAAA,6LAAC,qLAAA,CAAA,SAAM;;;;;;;;;;;;;;;8BAIX,6LAAC,+KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;kCAEN,cAAA,6LAAC,mMAAA,CAAA,cAAW;4BACV,KAAK;4BACL,OAAO;gCAAE,OAAO;4BAAO;4BACvB,WAAW,CAAA,QAAS,GAAG,OAAO,CAAC,OAAO,CAAC,yBAAyB;4BAChE,QAAQ,CAAA,QAAS,MAAO,OAAO,CAAC,eAAe;;;;;;;;;;;;;;;;8BAKrD,6LAAC,+KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;kCAEN,cAAA,6LAAC,mMAAA,CAAA,cAAW;4BACV,KAAK;4BACL,OAAO;gCAAE,OAAO;4BAAO;4BACvB,WAAW,CAAA,QAAS,GAAG,OAAO,CAAC,OAAO,CAAC,yBAAyB;4BAChE,QAAQ,CAAA,QAAS,MAAO,OAAO,CAAC,eAAe;4BAC/C,aAAY;;;;;;;;;;;;;;;;8BAKlB,6LAAC,+KAAA,CAAA,MAAG;oBAAC,MAAM;8BACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,MAAK;wBACL,OAAM;kCAEN,cAAA,6LAAC,iMAAA,CAAA,aAAU;4BACT,OAAO;gCAAE,OAAO;4BAAO;4BACvB,QAAQ;4BACR,QAAO;4BACP,aAAY;;;;;;;;;;;;;;;;;;IAOtB,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,6LAAC,iLAAA,CAAA,OAAI;;0BACH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,OAAO;;4BACX,SAAS,WAAW,WAAW;4BAAO;4BAAE,aAAa,WAAW,gBAAgB;;;;;;;kCAEnF,6LAAC;wBAAK,MAAK;kCACR,SAAS,WACN,CAAC,UAAU,EAAE,SAAS,mBAAmB,CAAC,GAC1C,CAAC,OAAO,EAAE,SAAS,iBAAiB,CAAC;;;;;;;;;;;;0BAM7C,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAS,GAAG,aAAa,WAAW,gBAAgB,kBAAkB,YAAY,CAAC;gBACnF,aACE,aAAa,WACT,gFACA;gBAEN,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;0BAGZ,6LAAC,iLAAA,CAAA,OAAI;gBACH,MAAM;gBACN,QAAO;gBACP,UAAU;gBACV,cAAa;gBACb,MAAK;;kCAEL,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CAEX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,6LAAC,uLAAA,CAAA,UAAO;oCAAC,aAAY;8CAAO;;;;;;;;;;;0CAG9B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO,gBAAgB,QAAQ;8CAE/B,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;wCACZ,UAAU,SAAS;;;;;;;;;;;;;;;;0CAKzB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO,gBAAgB,KAAK;8CAE5B,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;;;;;;;;;;;;;;;;0CAKlB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAO,SAAS,WAAW,aAAa;oCACxC,OAAO,gBAAgB,QAAQ;8CAE/B,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;wCACb,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAa,SAAS,WAAW,mBAAmB;;;;;;;;;;;;;;;;0CAM1D,6LAAC,+KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,6LAAC,uLAAA,CAAA,UAAO;oCAAC,aAAY;8CAClB,aAAa,WAAW,uBAAuB;;;;;;;;;;;4BAInD,aAAa,WAAW,2BAA2B;0CAGpD,6LAAC,+KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,6LAAC,uLAAA,CAAA,UAAO;oCAAC,aAAY;8CAAO;;;;;;;;;;;0CAG9B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,eAAc;oCACd,cAAc;8CAEd,cAAA,6LAAC,qLAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;kCAMb,6LAAC,uLAAA,CAAA,UAAO;;;;;kCACR,6LAAC,+KAAA,CAAA,MAAG;wBAAC,SAAQ;kCACX,cAAA,6LAAC,+KAAA,CAAA,MAAG;sCACF,cAAA,6LAAC,mMAAA,CAAA,QAAK;;kDACJ,6LAAC,qMAAA,CAAA,SAAM;wCAAC,SAAS;kDAAa;;;;;;kDAG9B,6LAAC,qMAAA,CAAA,SAAM;wCAAC,SAAS;;0DACf,6LAAC,uNAAA,CAAA,gBAAa;;;;;4CAAG;;;;;;;kDAGnB,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAS;wCACT,SAAS;wCACT,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;kDAElB,SAAS,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GAtXgB;;QAQC,iLAAA,CAAA,OAAI,CAAC;;;KARN;uCAwXD"}}, {"offset": {"line": 2640, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2646, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/src/app/dashboard/users/page.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - User Management Page\n// Main page cho quản lý SystemUser và RegisteredUser\n\nimport React, { useState } from 'react';\nimport {\n  Tabs,\n  Typography,\n  Space,\n  Card,\n  Statistic,\n  Row,\n  Col,\n  Modal,\n  message,\n  Button\n} from 'antd';\nimport {\n  UserOutlined,\n  TeamOutlined,\n  CrownOutlined,\n  ApiOutlined,\n  PlusOutlined\n} from '@ant-design/icons';\nimport { DashboardLayout } from '@/components/layouts/dashboard-layout';\nimport { UserTable } from '@/components/tables/user-table';\nimport { UserForm } from '@/components/forms/user-form';\nimport { usePermissions } from '@/stores/auth-store';\nimport { SystemUser, RegisteredUser, UserFilters } from '@/types';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\n\n// ============================================================================\n// MOCK DATA - sẽ được thay thế bằng real API calls\n// ============================================================================\n\nconst mockSystemUsers: SystemUser[] = [\n  {\n    id: 1,\n    username: 'admin',\n    email: '<EMAIL>',\n    role: 'admin',\n    isActive: true,\n    lastLoginAt: new Date('2024-01-15T10:30:00Z'),\n    createdAt: new Date('2024-01-01T00:00:00Z'),\n  },\n  {\n    id: 2,\n    username: 'editor1',\n    email: '<EMAIL>',\n    role: 'editor',\n    isActive: true,\n    lastLoginAt: new Date('2024-01-15T09:15:00Z'),\n    createdAt: new Date('2024-01-05T00:00:00Z'),\n  },\n  {\n    id: 3,\n    username: 'moderator1',\n    email: '<EMAIL>',\n    role: 'moderator',\n    isActive: false,\n    lastLoginAt: new Date('2024-01-10T14:20:00Z'),\n    createdAt: new Date('2024-01-10T00:00:00Z'),\n  },\n];\n\nconst mockRegisteredUsers: RegisteredUser[] = [\n  {\n    id: 1,\n    username: 'developer1',\n    email: '<EMAIL>',\n    tier: 'premium',\n    isActive: true,\n    isEmailVerified: true,\n    apiCallsUsed: 15000,\n    apiCallsLimit: 50000,\n    subscriptionEndDate: new Date('2024-12-31T23:59:59Z'),\n    lastLoginAt: new Date('2024-01-15T11:00:00Z'),\n    createdAt: new Date('2024-01-01T00:00:00Z'),\n  },\n  {\n    id: 2,\n    username: 'startup_user',\n    email: '<EMAIL>',\n    tier: 'enterprise',\n    isActive: true,\n    isEmailVerified: true,\n    apiCallsUsed: 75000,\n    apiCallsLimit: 200000,\n    subscriptionEndDate: new Date('2024-06-30T23:59:59Z'),\n    lastLoginAt: new Date('2024-01-15T08:30:00Z'),\n    createdAt: new Date('2023-12-15T00:00:00Z'),\n  },\n  {\n    id: 3,\n    username: 'free_user',\n    email: '<EMAIL>',\n    tier: 'free',\n    isActive: true,\n    isEmailVerified: false,\n    apiCallsUsed: 950,\n    apiCallsLimit: 1000,\n    subscriptionEndDate: null,\n    lastLoginAt: new Date('2024-01-14T16:45:00Z'),\n    createdAt: new Date('2024-01-14T00:00:00Z'),\n  },\n];\n\n// ============================================================================\n// USER MANAGEMENT PAGE COMPONENT\n// ============================================================================\n\nexport default function UserManagementPage() {\n  const permissions = usePermissions();\n  \n  // State management\n  const [activeTab, setActiveTab] = useState('system');\n  const [loading, setLoading] = useState(false);\n  const [showForm, setShowForm] = useState(false);\n  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');\n  const [selectedUser, setSelectedUser] = useState<SystemUser | RegisteredUser | null>(null);\n  \n  // Filters\n  const [systemUserFilters, setSystemUserFilters] = useState<UserFilters>({});\n  const [registeredUserFilters, setRegisteredUserFilters] = useState<UserFilters>({});\n\n  // Pagination\n  const [systemUserPagination, setSystemUserPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: mockSystemUsers.length,\n  });\n  \n  const [registeredUserPagination, setRegisteredUserPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: mockRegisteredUsers.length,\n  });\n\n  // ========================================================================\n  // PERMISSION CHECK\n  // ========================================================================\n  \n  if (!permissions.canManageUsers) {\n    return (\n      <DashboardLayout>\n        <Card>\n          <div className=\"text-center py-8\">\n            <UserOutlined className=\"text-4xl text-gray-400 mb-4\" />\n            <Title level={3}>Access Denied</Title>\n            <Text type=\"secondary\">\n              You don't have permission to manage users.\n            </Text>\n          </div>\n        </Card>\n      </DashboardLayout>\n    );\n  }\n\n  // ========================================================================\n  // EVENT HANDLERS\n  // ========================================================================\n\n  const handleAddUser = (userType: 'system' | 'registered') => {\n    setFormMode('create');\n    setSelectedUser(null);\n    setActiveTab(userType);\n    setShowForm(true);\n  };\n\n  const handleEditUser = (user: SystemUser | RegisteredUser) => {\n    setFormMode('edit');\n    setSelectedUser(user);\n    setShowForm(true);\n  };\n\n  const handleDeleteUser = (user: SystemUser | RegisteredUser) => {\n    // Mock delete - sẽ được thay thế bằng API call\n    message.success(`User \"${user.username}\" deleted successfully`);\n  };\n\n  const handleFormSubmit = async (values: any) => {\n    setLoading(true);\n    try {\n      // Mock API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      message.success(\n        `User ${formMode === 'create' ? 'created' : 'updated'} successfully`\n      );\n      setShowForm(false);\n      setSelectedUser(null);\n    } catch (error) {\n      message.error('Operation failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRefresh = () => {\n    setLoading(true);\n    // Mock refresh\n    setTimeout(() => {\n      setLoading(false);\n      message.success('Data refreshed');\n    }, 1000);\n  };\n\n  const handleExport = () => {\n    message.info('Export functionality will be implemented');\n  };\n\n  // ========================================================================\n  // STATISTICS\n  // ========================================================================\n\n  const systemUserStats = {\n    total: mockSystemUsers.length,\n    active: mockSystemUsers.filter(u => u.isActive).length,\n    admins: mockSystemUsers.filter(u => u.role === 'admin').length,\n    editors: mockSystemUsers.filter(u => u.role === 'editor').length,\n  };\n\n  const registeredUserStats = {\n    total: mockRegisteredUsers.length,\n    active: mockRegisteredUsers.filter(u => u.isActive).length,\n    verified: mockRegisteredUsers.filter(u => u.isEmailVerified).length,\n    premium: mockRegisteredUsers.filter(u => u.tier === 'premium' || u.tier === 'enterprise').length,\n  };\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Page Header */}\n        <div>\n          <Title level={2} className=\"mb-2\">\n            User Management\n          </Title>\n          <Text type=\"secondary\" className=\"text-base\">\n            Manage system users and registered API users\n          </Text>\n        </div>\n\n        {/* Statistics Cards */}\n        <Row gutter={16}>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Total System Users\"\n                value={systemUserStats.total}\n                prefix={<CrownOutlined />}\n                valueStyle={{ color: '#1890ff' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Active System Users\"\n                value={systemUserStats.active}\n                prefix={<UserOutlined />}\n                valueStyle={{ color: '#52c41a' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Total Registered Users\"\n                value={registeredUserStats.total}\n                prefix={<TeamOutlined />}\n                valueStyle={{ color: '#722ed1' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Verified Users\"\n                value={registeredUserStats.verified}\n                prefix={<ApiOutlined />}\n                valueStyle={{ color: '#faad14' }}\n              />\n            </Card>\n          </Col>\n        </Row>\n\n        {/* User Management Tabs */}\n        <Card>\n          <Tabs\n            activeKey={activeTab}\n            onChange={setActiveTab}\n            tabBarExtraContent={\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => handleAddUser(activeTab as 'system' | 'registered')}\n              >\n                Add {activeTab === 'system' ? 'System User' : 'User'}\n              </Button>\n            }\n          >\n            <TabPane\n              tab={\n                <Space>\n                  <CrownOutlined />\n                  System Users ({systemUserStats.total})\n                </Space>\n              }\n              key=\"system\"\n            >\n              <UserTable\n                users={mockSystemUsers}\n                loading={loading}\n                userType=\"system\"\n                onEdit={handleEditUser}\n                onDelete={handleDeleteUser}\n                onAdd={() => handleAddUser('system')}\n                onRefresh={handleRefresh}\n                onExport={handleExport}\n                pagination={{\n                  ...systemUserPagination,\n                  onChange: (page, pageSize) => {\n                    setSystemUserPagination({ ...systemUserPagination, current: page, pageSize });\n                  },\n                }}\n                filters={systemUserFilters}\n                onFiltersChange={setSystemUserFilters}\n              />\n            </TabPane>\n\n            <TabPane\n              tab={\n                <Space>\n                  <TeamOutlined />\n                  Registered Users ({registeredUserStats.total})\n                </Space>\n              }\n              key=\"registered\"\n            >\n              <UserTable\n                users={mockRegisteredUsers}\n                loading={loading}\n                userType=\"registered\"\n                onEdit={handleEditUser}\n                onDelete={handleDeleteUser}\n                onAdd={() => handleAddUser('registered')}\n                onRefresh={handleRefresh}\n                onExport={handleExport}\n                pagination={{\n                  ...registeredUserPagination,\n                  onChange: (page, pageSize) => {\n                    setRegisteredUserPagination({ ...registeredUserPagination, current: page, pageSize });\n                  },\n                }}\n                filters={registeredUserFilters}\n                onFiltersChange={setRegisteredUserFilters}\n              />\n            </TabPane>\n          </Tabs>\n        </Card>\n\n        {/* User Form Modal */}\n        <Modal\n          title={null}\n          open={showForm}\n          onCancel={() => setShowForm(false)}\n          footer={null}\n          width={800}\n          destroyOnClose\n        >\n          <UserForm\n            userType={activeTab as 'system' | 'registered'}\n            mode={formMode}\n            initialValues={selectedUser || undefined}\n            loading={loading}\n            onSubmit={handleFormSubmit}\n            onCancel={() => setShowForm(false)}\n          />\n        </Modal>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,2CAA2C;AAC3C,qDAAqD;AAErD;AAoBA;AACA;AACA;AACA;AAtBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAAA;AAAA;AAAA;AAZA;AAYA;AAZA;AAAA;;;AANA;;;;;;;;AA+BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAClC,MAAM,EAAE,OAAO,EAAE,GAAG,iLAAA,CAAA,OAAI;AAExB,+EAA+E;AAC/E,mDAAmD;AACnD,+EAA+E;AAE/E,MAAM,kBAAgC;IACpC;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;CACD;AAED,MAAM,sBAAwC;IAC5C;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,qBAAqB,IAAI,KAAK;QAC9B,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,qBAAqB,IAAI,KAAK;QAC9B,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,qBAAqB;QACrB,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;IACtB;CACD;AAMc,SAAS;;IACtB,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEjC,mBAAmB;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsC;IAErF,UAAU;IACV,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,CAAC;IACzE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,CAAC;IAEjF,aAAa;IACb,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/D,SAAS;QACT,UAAU;QACV,OAAO,gBAAgB,MAAM;IAC/B;IAEA,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvE,SAAS;QACT,UAAU;QACV,OAAO,oBAAoB,MAAM;IACnC;IAEA,2EAA2E;IAC3E,mBAAmB;IACnB,2EAA2E;IAE3E,IAAI,CAAC,YAAY,cAAc,EAAE;QAC/B,qBACE,6LAAC,uJAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC,iLAAA,CAAA,OAAI;0BACH,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;sCACxB,6LAAC;4BAAM,OAAO;sCAAG;;;;;;sCACjB,6LAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;;;;;;;;;;;IAOjC;IAEA,2EAA2E;IAC3E,iBAAiB;IACjB,2EAA2E;IAE3E,MAAM,gBAAgB,CAAC;QACrB,YAAY;QACZ,gBAAgB;QAChB,aAAa;QACb,YAAY;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY;QACZ,gBAAgB;QAChB,YAAY;IACd;IAEA,MAAM,mBAAmB,CAAC;QACxB,+CAA+C;QAC/C,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,QAAQ,CAAC,sBAAsB,CAAC;IAChE;IAEA,MAAM,mBAAmB,OAAO;QAC9B,WAAW;QACX,IAAI;YACF,gBAAgB;YAChB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,uLAAA,CAAA,UAAO,CAAC,OAAO,CACb,CAAC,KAAK,EAAE,aAAa,WAAW,YAAY,UAAU,aAAa,CAAC;YAEtE,YAAY;YACZ,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,WAAW;QACX,eAAe;QACf,WAAW;YACT,WAAW;YACX,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB,GAAG;IACL;IAEA,MAAM,eAAe;QACnB,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;IACf;IAEA,2EAA2E;IAC3E,aAAa;IACb,2EAA2E;IAE3E,MAAM,kBAAkB;QACtB,OAAO,gBAAgB,MAAM;QAC7B,QAAQ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;QACtD,QAAQ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;QAC9D,SAAS,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;IAClE;IAEA,MAAM,sBAAsB;QAC1B,OAAO,oBAAoB,MAAM;QACjC,QAAQ,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;QAC1D,UAAU,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,EAAE,MAAM;QACnE,SAAS,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,IAAI,KAAK,cAAc,MAAM;IAClG;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,6LAAC,uJAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;;sCACC,6LAAC;4BAAM,OAAO;4BAAG,WAAU;sCAAO;;;;;;sCAGlC,6LAAC;4BAAK,MAAK;4BAAY,WAAU;sCAAY;;;;;;;;;;;;8BAM/C,6LAAC,+KAAA,CAAA,MAAG;oBAAC,QAAQ;;sCACX,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,6LAAC,iLAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,gBAAgB,KAAK;oCAC5B,sBAAQ,6LAAC,uNAAA,CAAA,gBAAa;;;;;oCACtB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;sCAIrC,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,6LAAC,iLAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,gBAAgB,MAAM;oCAC7B,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACrB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;sCAIrC,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,6LAAC,iLAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,oBAAoB,KAAK;oCAChC,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACrB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;sCAIrC,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,6LAAC,iLAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;oCACR,OAAM;oCACN,OAAO,oBAAoB,QAAQ;oCACnC,sBAAQ,6LAAC,mNAAA,CAAA,cAAW;;;;;oCACpB,YAAY;wCAAE,OAAO;oCAAU;;;;;;;;;;;;;;;;;;;;;;8BAOvC,6LAAC,iLAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,iLAAA,CAAA,OAAI;wBACH,WAAW;wBACX,UAAU;wBACV,kCACE,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM,cAAc;;gCAC9B;gCACM,cAAc,WAAW,gBAAgB;;;;;;;;0CAIlD,6LAAC;gCACC,mBACE,6LAAC,mMAAA,CAAA,QAAK;;sDACJ,6LAAC,uNAAA,CAAA,gBAAa;;;;;wCAAG;wCACF,gBAAgB,KAAK;wCAAC;;;;;;;0CAKzC,cAAA,6LAAC,gJAAA,CAAA,YAAS;oCACR,OAAO;oCACP,SAAS;oCACT,UAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,OAAO,IAAM,cAAc;oCAC3B,WAAW;oCACX,UAAU;oCACV,YAAY;wCACV,GAAG,oBAAoB;wCACvB,UAAU,CAAC,MAAM;4CACf,wBAAwB;gDAAE,GAAG,oBAAoB;gDAAE,SAAS;gDAAM;4CAAS;wCAC7E;oCACF;oCACA,SAAS;oCACT,iBAAiB;;;;;;+BAlBf;;;;;0CAsBN,6LAAC;gCACC,mBACE,6LAAC,mMAAA,CAAA,QAAK;;sDACJ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCAAG;wCACG,oBAAoB,KAAK;wCAAC;;;;;;;0CAKjD,cAAA,6LAAC,gJAAA,CAAA,YAAS;oCACR,OAAO;oCACP,SAAS;oCACT,UAAS;oCACT,QAAQ;oCACR,UAAU;oCACV,OAAO,IAAM,cAAc;oCAC3B,WAAW;oCACX,UAAU;oCACV,YAAY;wCACV,GAAG,wBAAwB;wCAC3B,UAAU,CAAC,MAAM;4CACf,4BAA4B;gDAAE,GAAG,wBAAwB;gDAAE,SAAS;gDAAM;4CAAS;wCACrF;oCACF;oCACA,SAAS;oCACT,iBAAiB;;;;;;+BAlBf;;;;;;;;;;;;;;;;8BAyBV,6LAAC,mLAAA,CAAA,QAAK;oBACJ,OAAO;oBACP,MAAM;oBACN,UAAU,IAAM,YAAY;oBAC5B,QAAQ;oBACR,OAAO;oBACP,cAAc;8BAEd,cAAA,6LAAC,8IAAA,CAAA,WAAQ;wBACP,UAAU;wBACV,MAAM;wBACN,eAAe,gBAAgB;wBAC/B,SAAS;wBACT,UAAU;wBACV,UAAU,IAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;AAMxC;GAnRwB;;QACF,iIAAA,CAAA,iBAAc;;;KADZ"}}, {"offset": {"line": 3229, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}