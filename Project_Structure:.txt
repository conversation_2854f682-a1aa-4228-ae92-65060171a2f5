apisportsgame-cms/
├── app/
│   ├── (auth)/
│   │   ├── login/
│   │   └── register/
│   ├── (dashboard)/
│   │   ├── users/
│   │   ├── leagues/
│   │   ├── teams/
│   │   ├── fixtures/
│   │   └── analytics/
│   ├── api/                # Proxy routes
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── ui/                 # Reusable components
│   ├── forms/              # Form components
│   ├── tables/             # Data tables
│   └── charts/             # Analytics charts
├── lib/
│   ├── api.ts              # API client
│   ├── auth.ts             # Auth configuration
│   ├── types.ts            # TypeScript types from guide
│   └── utils.ts
├── stores/                 # Zustand stores
├── docs/
│   └── CMS_DEVELOPMENT_GUIDE.md  # Copy of main document
├── .augment-rules.md       # Augment rules for CMS
└── README.md