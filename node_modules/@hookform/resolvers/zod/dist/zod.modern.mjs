import{validateFieldsNatively as r,toNestErrors as o}from"@hookform/resolvers";import{appendErrors as e}from"react-hook-form";function s(r,o){const s={};for(;r.length;){const n=r[0],{code:t,message:a,path:i}=n,c=i.join(".");if(!s[c])if("unionErrors"in n){const r=n.unionErrors[0].errors[0];s[c]={message:r.message,type:r.code}}else s[c]={message:a,type:t};if("unionErrors"in n&&n.unionErrors.forEach(o=>o.errors.forEach(o=>r.push(o))),o){const r=s[c].types,a=r&&r[n.code];s[c]=e(c,o,s,t,a?[].concat(a,n.message):n.message)}r.shift()}return s}function n(e,n,t={}){return async(a,i,c)=>{try{const o=await e["sync"===t.mode?"parse":"parseAsync"](a,n);return c.shouldUseNativeValidation&&r({},c),{errors:{},values:t.raw?Object.assign({},a):o}}catch(r){if((r=>Array.isArray(null==r?void 0:r.errors))(r))return{values:{},errors:o(s(r.errors,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)};throw r}}}export{n as zodResolver};
//# sourceMappingURL=zod.modern.mjs.map
