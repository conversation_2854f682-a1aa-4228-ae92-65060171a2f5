{"version": 3, "file": "zod.js", "sources": ["../src/zod.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Resolver,\n  ResolverError,\n  ResolverSuccess,\n  appendErrors,\n} from 'react-hook-form';\nimport { ZodError, z } from 'zod';\n\nconst isZodError = (error: any): error is ZodError =>\n  Array.isArray(error?.errors);\n\nfunction parseErrorSchema(\n  zodErrors: z.ZodIssue[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n  for (; zodErrors.length; ) {\n    const error = zodErrors[0];\n    const { code, message, path } = error;\n    const _path = path.join('.');\n\n    if (!errors[_path]) {\n      if ('unionErrors' in error) {\n        const unionError = error.unionErrors[0].errors[0];\n\n        errors[_path] = {\n          message: unionError.message,\n          type: unionError.code,\n        };\n      } else {\n        errors[_path] = { message, type: code };\n      }\n    }\n\n    if ('unionErrors' in error) {\n      error.unionErrors.forEach((unionError) =>\n        unionError.errors.forEach((e) => zodErrors.push(e)),\n      );\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[error.code];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        code,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    zodErrors.shift();\n  }\n\n  return errors;\n}\n\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: z.ZodSchema<Output, any, Input>,\n  schemaOptions?: Partial<z.ParseParams>,\n  resolverOptions?: {\n    mode?: 'async' | 'sync';\n    raw?: false;\n  },\n): Resolver<Input, Context, Output>;\n\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: z.ZodSchema<Output, any, Input>,\n  schemaOptions: Partial<z.ParseParams> | undefined,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver function for react-hook-form that validates form data using a Zod schema\n * @param {z.ZodSchema<Input>} schema - The Zod schema used to validate the form data\n * @param {Partial<z.ParseParams>} [schemaOptions] - Optional configuration options for Zod parsing\n * @param {Object} [resolverOptions] - Optional resolver-specific configuration\n * @param {('async'|'sync')} [resolverOptions.mode='async'] - Validation mode. Use 'sync' for synchronous validation\n * @param {boolean} [resolverOptions.raw=false] - If true, returns the raw form values instead of the parsed data\n * @returns {Resolver<z.output<typeof schema>>} A resolver function compatible with react-hook-form\n * @throws {Error} Throws if validation fails with a non-Zod error\n * @example\n * const schema = z.object({\n *   name: z.string().min(2),\n *   age: z.number().min(18)\n * });\n *\n * useForm({\n *   resolver: zodResolver(schema)\n * });\n */\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: z.ZodSchema<Output, any, Input>,\n  schemaOptions?: Partial<z.ParseParams>,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Output | Input> {\n  return async (values: Input, _, options) => {\n    try {\n      const data = await schema[\n        resolverOptions.mode === 'sync' ? 'parse' : 'parseAsync'\n      ](values, schemaOptions);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {} as FieldErrors,\n        values: resolverOptions.raw ? Object.assign({}, values) : data,\n      } satisfies ResolverSuccess<Output | Input>;\n    } catch (error) {\n      if (isZodError(error)) {\n        return {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              error.errors,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        } satisfies ResolverError<Input>;\n      }\n\n      throw error;\n    }\n  };\n}\n"], "names": ["parseErrorSchema", "zodErrors", "validateAllFieldCriteria", "errors", "length", "error", "code", "message", "_path", "path", "join", "unionError", "unionErrors", "type", "for<PERSON>ach", "e", "push", "types", "messages", "appendErrors", "concat", "shift", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "Promise", "resolve", "mode", "then", "data", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "Object", "assign", "_catch", "Array", "isArray", "isZodError", "toNestErrors", "criteriaMode", "reject"], "mappings": "kEAeA,SAASA,EACPC,EACAC,GAGA,IADA,IAAMC,EAAqC,CAAE,EACtCF,EAAUG,QAAU,CACzB,IAAMC,EAAQJ,EAAU,GAChBK,EAAwBD,EAAxBC,KAAMC,EAAkBF,EAAlBE,QACRC,EAD0BH,EAATI,KACJC,KAAK,KAExB,IAAKP,EAAOK,GACV,GAAI,gBAAiBH,EAAO,CAC1B,IAAMM,EAAaN,EAAMO,YAAY,GAAGT,OAAO,GAE/CA,EAAOK,GAAS,CACdD,QAASI,EAAWJ,QACpBM,KAAMF,EAAWL,KAErB,MACEH,EAAOK,GAAS,CAAED,QAAAA,EAASM,KAAMP,GAUrC,GANI,gBAAiBD,GACnBA,EAAMO,YAAYE,QAAQ,SAACH,GACzB,OAAAA,EAAWR,OAAOW,QAAQ,SAACC,GAAC,OAAKd,EAAUe,KAAKD,EAAE,EAAC,GAInDb,EAA0B,CAC5B,IAAMe,EAAQd,EAAOK,GAAOS,MACtBC,EAAWD,GAASA,EAAMZ,EAAMC,MAEtCH,EAAOK,GAASW,EAAYA,aAC1BX,EACAN,EACAC,EACAG,EACAY,EACK,GAAgBE,OAAOF,EAAsBb,EAAME,SACpDF,EAAME,QAEd,CAEAN,EAAUoB,OACZ,CAEA,OAAOlB,CACT,qBAuCM,SACJmB,EACAC,EACAC,GAKA,YALAA,IAAAA,IAAAA,EAGI,CAAE,GAEQC,SAAAA,EAAeC,EAAGC,GAAW,IAAA,OAAAC,QAAAC,gCACrCD,QAAAC,QACiBP,EACQ,SAAzBE,EAAgBM,KAAkB,QAAU,cAC5CL,EAAQF,IAAcQ,KAFlBC,SAAAA,GAMN,OAFAL,EAAQM,2BAA6BC,EAAAA,uBAAuB,CAAA,EAAIP,GAEzD,CACLxB,OAAQ,CAAA,EACRsB,OAAQD,EAAgBW,IAAMC,OAAOC,OAAO,CAAE,EAAEZ,GAAUO,EAChB,4DAXLM,CAAA,EAYhCjC,SAAAA,GACP,GA/Ga,SAACA,GAClB,OAAAkC,MAAMC,QAAa,MAALnC,OAAK,EAALA,EAAOF,OAAO,CA8GpBsC,CAAWpC,GACb,MAAO,CACLoB,OAAQ,CAAA,EACRtB,OAAQuC,EAAYA,aAClB1C,EACEK,EAAMF,QACLwB,EAAQM,2BACkB,QAAzBN,EAAQgB,cAEZhB,IAKN,MAAMtB,CACR,GACF,CAAC,MAAAU,GAAAa,OAAAA,QAAAgB,OAAA7B,EACH,CAAA,CAAA"}