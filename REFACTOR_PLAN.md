# APISportsGame CMS - Modular Refactor Plan

**Date:** 2024-01-15
**Status:** 🔄 IN PROGRESS
**Goal:** Module hóa mã nguồn để tránh xung đột và thuận tiện scale up

## 🎯 **Refactor Objectives**

### **1. Domain-Driven Design**
- Tách code theo domains: Auth, Users, Leagues, Teams, Fixtures
- Mỗi domain có structure riêng biệt
- Minimize cross-domain dependencies

### **2. Code Reusability**
- Shared utilities và components
- Common types và interfaces
- Reusable hooks và stores

### **3. Scalability**
- Easy to add new features
- Clear separation of concerns
- Maintainable codebase

## 🏗️ **New Architecture Structure**

```
src/
├── shared/                     # Shared utilities
│   ├── components/             # Common UI components
│   │   ├── ui/                 # Base UI components
│   │   ├── forms/              # Generic form components
│   │   └── tables/             # Generic table components
│   ├── hooks/                  # Common hooks
│   ├── utils/                  # Utility functions
│   ├── types/                  # Shared types
│   └── constants/              # App constants
├── modules/                    # Domain modules
│   ├── auth/                   # Authentication domain
│   │   ├── components/         # Auth-specific components
│   │   ├── hooks/              # Auth hooks
│   │   ├── stores/             # Auth state management
│   │   ├── api/                # Auth API calls
│   │   ├── types/              # Auth types
│   │   └── index.ts            # Public exports
│   ├── users/                  # User management domain
│   │   ├── components/         # User components
│   │   │   ├── user-table.tsx
│   │   │   ├── user-form.tsx
│   │   │   └── index.ts
│   │   ├── hooks/              # User hooks
│   │   │   ├── use-users.ts
│   │   │   ├── use-user-mutations.ts
│   │   │   └── index.ts
│   │   ├── api/                # User API
│   │   │   ├── user-api.ts
│   │   │   └── index.ts
│   │   ├── types/              # User types
│   │   │   ├── user.types.ts
│   │   │   └── index.ts
│   │   ├── stores/             # User stores
│   │   └── index.ts            # Public exports
│   ├── leagues/                # League management domain
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── api/
│   │   ├── types/
│   │   ├── stores/
│   │   └── index.ts
│   ├── teams/                  # Team management domain
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── api/
│   │   ├── types/
│   │   ├── stores/
│   │   └── index.ts
│   └── fixtures/               # Fixture management domain
│       ├── components/
│       ├── hooks/
│       ├── api/
│       ├── types/
│       ├── stores/
│       └── index.ts
├── app/                        # NextJS App Router
│   ├── auth/
│   ├── dashboard/
│   ├── layout.tsx
│   └── page.tsx
└── lib/                        # Core libraries
    ├── api-client.ts           # Base API client
    ├── query-client.ts         # TanStack Query setup
    └── providers.tsx           # App providers
```

## 🔄 **Refactor Steps**

### **Phase 1: Shared Infrastructure**
1. ✅ Create shared utilities
2. ✅ Extract common components
3. ✅ Setup base API client
4. ✅ Create common types

### **Phase 2: Auth Module**
1. ✅ Extract auth types
2. ✅ Create auth API module
3. ✅ Refactor auth store
4. ✅ Create auth hooks

### **Phase 3: Users Module**
1. ✅ Extract user types
2. ✅ Create user API module
3. ✅ Refactor user components
4. ✅ Create user hooks
5. ✅ Update imports and fix circular dependencies

### **Phase 4: Sports Data Modules**
1. 🔄 Create leagues module
2. 🔄 Create teams module
3. 🔄 Create fixtures module
4. 🔄 Update app routing

### **Phase 5: Integration & Testing**
1. ✅ Update imports
2. ✅ Test all modules
3. 🔄 Performance optimization
4. 🔄 Documentation update

## 📋 **Module Standards**

### **Each Module Must Have:**
1. **Public API:** Clear exports via index.ts
2. **Type Safety:** Full TypeScript coverage
3. **Error Handling:** Comprehensive error management
4. **Testing:** Unit tests for critical functions
5. **Documentation:** Clear component documentation

### **Import Rules:**
1. **No Cross-Domain Imports:** Modules can only import from shared/
2. **Public API Only:** Import via module's index.ts
3. **Shared First:** Use shared components before creating new ones
4. **Type Imports:** Separate type imports from value imports

## 🎯 **Benefits Expected**

### **1. Maintainability**
- Clear code organization
- Easy to find and modify features
- Reduced cognitive load

### **2. Scalability**
- Easy to add new domains
- Parallel development possible
- Clear boundaries

### **3. Reusability**
- Shared components across domains
- Common patterns và utilities
- Consistent UX

### **4. Testing**
- Isolated unit testing
- Mock dependencies easily
- Clear test boundaries

## 🔍 **Quality Metrics**

### **Before Refactor:**
- **Files:** ~15 files
- **Lines of Code:** ~2000 lines
- **Coupling:** High (monolithic)
- **Reusability:** Low

### **After Refactor Target:**
- **Modules:** 5 domains + shared
- **Coupling:** Low (domain isolation)
- **Reusability:** High (shared components)
- **Maintainability:** High (clear structure)

---

**Status:** 🔄 **READY TO START REFACTOR**
**Next:** Begin Phase 1 - Shared Infrastructure
**Timeline:** ~2-3 hours for complete refactor

*This refactor will create a solid foundation for scaling the CMS to handle multiple sports data domains efficiently.*
