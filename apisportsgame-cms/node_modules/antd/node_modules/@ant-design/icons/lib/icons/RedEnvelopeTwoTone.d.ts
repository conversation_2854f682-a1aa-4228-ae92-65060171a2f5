import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![red-envelope](data:image/svg+xml;base64,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) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
