import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![pushpin](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3OC4zIDM5Mi4xTDYzMS45IDE0NS43Yy02LjUtNi41LTE1LTkuNy0yMy41LTkuN3MtMTcgMy4yLTIzLjUgOS43TDQyMy44IDMwNi45Yy0xMi4yLTEuNC0yNC41LTItMzYuOC0yLTczLjIgMC0xNDYuNCAyNC4xLTIwNi41IDcyLjNhMzMuMjMgMzMuMjMgMCAwMC0yLjcgNDkuNGwxODEuNyAxODEuNy0yMTUuNCAyMTUuMmExNS44IDE1LjggMCAwMC00LjYgOS44bC0zLjQgMzcuMmMtLjkgOS40IDYuNiAxNy40IDE1LjkgMTcuNC41IDAgMSAwIDEuNS0uMWwzNy4yLTMuNGMzLjctLjMgNy4yLTIgOS44LTQuNmwyMTUuNC0yMTUuNCAxODEuNyAxODEuN2M2LjUgNi41IDE1IDkuNyAyMy41IDkuNyA5LjcgMCAxOS4zLTQuMiAyNS45LTEyLjQgNTYuMy03MC4zIDc5LjctMTU4LjMgNzAuMi0yNDMuNGwxNjEuMS0xNjEuMWMxMi45LTEyLjggMTIuOS0zMy44IDAtNDYuOHpNNjY2LjIgNTQ5LjNsLTI0LjUgMjQuNSAzLjggMzQuNGEyNTkuOTIgMjU5LjkyIDAgMDEtMzAuNCAxNTMuOUwyNjIgNDA4LjhjMTIuOS03LjEgMjYuMy0xMy4xIDQwLjMtMTcuOSAyNy4yLTkuNCA1NS43LTE0LjEgODQuNy0xNC4xIDkuNiAwIDE5LjMuNSAyOC45IDEuNmwzNC40IDMuOCAyNC41LTI0LjVMNjA4LjUgMjI0IDgwMCA0MTUuNSA2NjYuMiA1NDkuM3oiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
