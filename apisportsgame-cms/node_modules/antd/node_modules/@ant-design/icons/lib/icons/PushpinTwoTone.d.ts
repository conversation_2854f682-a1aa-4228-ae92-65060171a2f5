import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![pushpin](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ3NC44IDM1Ny43bC0yNC41IDI0LjUtMzQuNC0zLjhjLTkuNi0xLjEtMTkuMy0xLjYtMjguOS0xLjYtMjkgMC01Ny41IDQuNy04NC43IDE0LjEtMTQgNC44LTI3LjQgMTAuOC00MC4zIDE3LjlsMzUzLjEgMzUzLjNhMjU5LjkyIDI1OS45MiAwIDAwMzAuNC0xNTMuOWwtMy44LTM0LjQgMjQuNS0yNC41TDgwMCA0MTUuNSA2MDguNSAyMjQgNDc0LjggMzU3Ljd6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04NzguMyAzOTIuMUw2MzEuOSAxNDUuN2MtNi41LTYuNS0xNS05LjctMjMuNS05LjdzLTE3IDMuMi0yMy41IDkuN0w0MjMuOCAzMDYuOWMtMTIuMi0xLjQtMjQuNS0yLTM2LjgtMi03My4yIDAtMTQ2LjQgMjQuMS0yMDYuNSA3Mi4zYTMzLjIzIDMzLjIzIDAgMDAtMi43IDQ5LjRsMTgxLjcgMTgxLjctMjE1LjQgMjE1LjJhMTUuOCAxNS44IDAgMDAtNC42IDkuOGwtMy40IDM3LjJjLS45IDkuNCA2LjYgMTcuNCAxNS45IDE3LjQuNSAwIDEgMCAxLjUtLjFsMzcuMi0zLjRjMy43LS4zIDcuMi0yIDkuOC00LjZsMjE1LjQtMjE1LjQgMTgxLjcgMTgxLjdjNi41IDYuNSAxNSA5LjcgMjMuNSA5LjcgOS43IDAgMTkuMy00LjIgMjUuOS0xMi40IDU2LjMtNzAuMyA3OS43LTE1OC4zIDcwLjItMjQzLjRsMTYxLjEtMTYxLjFjMTIuOS0xMi44IDEyLjktMzMuOCAwLTQ2Ljh6TTY2Ni4yIDU0OS4zbC0yNC41IDI0LjUgMy44IDM0LjRhMjU5LjkyIDI1OS45MiAwIDAxLTMwLjQgMTUzLjlMMjYyIDQwOC44YzEyLjktNy4xIDI2LjMtMTMuMSA0MC4zLTE3LjkgMjcuMi05LjQgNTUuNy0xNC4xIDg0LjctMTQuMSA5LjYgMCAxOS4zLjUgMjguOSAxLjZsMzQuNCAzLjggMjQuNS0yNC41TDYwOC41IDIyNCA4MDAgNDE1LjUgNjY2LjIgNTQ5LjN6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
