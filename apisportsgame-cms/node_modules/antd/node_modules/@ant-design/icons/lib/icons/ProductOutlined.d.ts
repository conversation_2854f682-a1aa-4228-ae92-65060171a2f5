import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![product](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNDY0IDE0NGExNiAxNiAwIDAxMTYgMTZ2MzA0YTE2IDE2IDAgMDEtMTYgMTZIMTYwYTE2IDE2IDAgMDEtMTYtMTZWMTYwYTE2IDE2IDAgMDExNi0xNnptLTUyIDY4SDIxMnYyMDBoMjAwem00OTMuMzMgODcuNjlhMTYgMTYgMCAwMTAgMjIuNjJMNzI0LjMxIDUwMy4zM2ExNiAxNiAwIDAxLTIyLjYyIDBMNTIwLjY3IDMyMi4zMWExNiAxNiAwIDAxMC0yMi42MmwxODEuMDItMTgxLjAyYTE2IDE2IDAgMDEyMi42MiAwem0tODQuODUgMTEuM0w3MTMgMjAzLjUzIDYwNS41MiAzMTEgNzEzIDQxOC40OHpNNDY0IDU0NGExNiAxNiAwIDAxMTYgMTZ2MzA0YTE2IDE2IDAgMDEtMTYgMTZIMTYwYTE2IDE2IDAgMDEtMTYtMTZWNTYwYTE2IDE2IDAgMDExNi0xNnptLTUyIDY4SDIxMnYyMDBoMjAwem00NTItNjhhMTYgMTYgMCAwMTE2IDE2djMwNGExNiAxNiAwIDAxLTE2IDE2SDU2MGExNiAxNiAwIDAxLTE2LTE2VjU2MGExNiAxNiAwIDAxMTYtMTZ6bS01MiA2OEg2MTJ2MjAwaDIwMHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
