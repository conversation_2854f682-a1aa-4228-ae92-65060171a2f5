import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![shake](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMyNCA2NjZhNDggNDggMCAxMDk2IDAgNDggNDggMCAxMC05NiAwem02MTYuNy0zMDkuNkw2NjcuNiA4My4yQzY1NS4yIDcwLjkgNjM4LjcgNjQgNjIxLjEgNjRzLTM0LjEgNi44LTQ2LjUgMTkuMkw4My4zIDU3NC41YTY1Ljg1IDY1Ljg1IDAgMDAwIDkzLjFsMjczLjIgMjczLjJjMTIuMyAxMi4zIDI4LjkgMTkuMiA0Ni41IDE5LjJzMzQuMS02LjggNDYuNS0xOS4ybDQ5MS4zLTQ5MS4zYzI1LjYtMjUuNyAyNS42LTY3LjUtLjEtOTMuMXpNNDAzIDg4MC4xTDE0My45IDYyMWw0NzcuMi00NzcuMiAyNTkgMjU5LjJMNDAzIDg4MC4xek0xNTIuOCAzNzMuN2E3LjkgNy45IDAgMDAxMS4yIDBMMzczLjcgMTY0YTcuOSA3LjkgMCAwMDAtMTEuMmwtMzguNC0zOC40YTcuOSA3LjkgMCAwMC0xMS4yIDBMMTE0LjMgMzIzLjlhNy45IDcuOSAwIDAwMCAxMS4ybDM4LjUgMzguNnptNzE4LjYgMjc2LjZhNy45IDcuOSAwIDAwLTExLjIgMEw2NTAuMyA4NjAuMWE3LjkgNy45IDAgMDAwIDExLjJsMzguNCAzOC40YTcuOSA3LjkgMCAwMDExLjIgMEw5MDkuNyA3MDBhNy45IDcuOSAwIDAwMC0xMS4ybC0zOC4zLTM4LjV6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
