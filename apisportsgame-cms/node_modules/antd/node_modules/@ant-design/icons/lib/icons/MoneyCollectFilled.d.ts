import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![money-collect](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMS41IDY5OS43YTggOCAwIDAwLTEwLjMtNC44TDg0MCA3MTcuMlYxNzljMC0zNy42LTMwLjQtNjgtNjgtNjhIMjUyYy0zNy42IDAtNjggMzAuNC02OCA2OHY1MzguMmwtNjEuMy0yMi4zYy0uOS0uMy0xLjgtLjUtMi43LS41LTQuNCAwLTggMy42LTggOFY3NjJjMCAzLjMgMi4xIDYuMyA1LjMgNy41TDUwMSA5MDkuMWM3LjEgMi42IDE0LjggMi42IDIxLjkgMGwzODMuOC0xMzkuNWMzLjItMS4yIDUuMy00LjIgNS4zLTcuNXYtNTkuNmMwLTEtLjItMS45LS41LTIuOHptLTI0My44LTM3N0w1NjQgNTE0LjNoNTcuNmM0LjQgMCA4IDMuNiA4IDh2MjcuMWMwIDQuNC0zLjYgOC04IDhoLTc2LjN2MzloNzYuM2M0LjQgMCA4IDMuNiA4IDh2MjcuMWMwIDQuNC0zLjYgOC04IDhoLTc2LjNWNzAzYzAgNC40LTMuNiA4LTggOGgtNDkuOWMtNC40IDAtOC0zLjYtOC04di02My40aC03NmMtNC40IDAtOC0zLjYtOC04di0yNy4xYzAtNC40IDMuNi04IDgtOGg3NnYtMzloLTc2Yy00LjQgMC04LTMuNi04LTh2LTI3LjFjMC00LjQgMy42LTggOC04aDU3TDM1Ni41IDMyMi44Yy0yLjEtMy44LS43LTguNyAzLjItMTAuOCAxLjItLjcgMi41LTEgMy44LTFoNTUuN2E4IDggMCAwMTcuMSA0LjRMNTExIDQ4NC4yaDMuM0w1OTkgMzE1LjRjMS4zLTIuNyA0LjEtNC40IDcuMS00LjRoNTQuNWM0LjQgMCA4IDMuNiA4LjEgNy45IDAgMS4zLS40IDIuNi0xIDMuOHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
