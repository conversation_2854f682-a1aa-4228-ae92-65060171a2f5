import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![java-script](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNDE2IDE3NkgyNTUuNTR2NDI1LjYyYzAgMTA1LjMtMzYuMTYgMTM0LjcxLTk5LjEgMTM0LjcxLTI5LjUgMC01Ni4wNS01LjA1LTc2LjcyLTEyLjE0TDYzIDg0OC43OUM5Mi40OCA4NTguOTEgMTM3LjczIDg2NSAxNzMuMTMgODY1IDMxNy42MyA4NjUgNDE2IDc5Ny4xNiA0MTYgNjAyLjY2em0zNDkuNDktMTZDNjEwLjI2IDE2MCA1MTIgMjQ4LjEzIDUxMiAzNjQuNmMwIDEwMC4zMiA3NS42NyAxNjMuMTMgMTg1LjcgMjAzLjY0IDc5LjU3IDI4LjM2IDExMS4wMyA1My43IDExMS4wMyA5NS4yMiAwIDQ1LjU3LTM2LjM2IDc0Ljk2LTEwNS4xMyA3NC45Ni02My44NyAwLTEyMS44NS0yMS4zMS0xNjEuMTUtNDIuNTh2LS4wNEw1MTIgODIyLjQzQzU0OS4zNiA4NDMuNzMgNjE5LjEyIDg2NSA2OTQuNzQgODY1IDg3Ni41MiA4NjUgOTYxIDc2Ny43NSA5NjEgNjUzLjNjMC05Ny4yNS01NC4wNC0xNjAuMDQtMTcwLjk0LTIwNC42My04Ni40Ny0zNC40NC0xMjIuODEtNTMuNjctMTIyLjgxLTk3LjIzIDAtMzQuNDUgMzEuNDUtNjUuODQgOTYuMy02NS44NCA2My44MyAwIDEwNy43MyAyMS40NSAxMzMuMyAzNC42NGwzOC4zNC0xMjguMTlDODk1LjEgMTc0LjQ2IDg0MS4xMSAxNjAgNzY1LjUgMTYwIiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
