import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![notification](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJjLTMuOCAwLTcuNy43LTExLjYgMi4zTDI5MiAzNDUuOUgxMjhjLTguOCAwLTE2IDcuNC0xNiAxNi42djI5OWMwIDkuMiA3LjIgMTYuNiAxNiAxNi42aDEwMS42Yy0zLjcgMTEuNi01LjYgMjMuOS01LjYgMzYuNCAwIDY1LjkgNTMuOCAxMTkuNSAxMjAgMTE5LjUgNTUuNCAwIDEwMi4xLTM3LjYgMTE1LjktODguNGw0MDguNiAxNjQuMmMzLjkgMS41IDcuOCAyLjMgMTEuNiAyLjMgMTYuOSAwIDMyLTE0LjIgMzItMzMuMlYxNDUuMkM5MTIgMTI2LjIgODk3IDExMiA4ODAgMTEyek0zNDQgNzYyLjNjLTI2LjUgMC00OC0yMS40LTQ4LTQ3LjggMC0xMS4yIDMuOS0yMS45IDExLTMwLjRsODQuOSAzNC4xYy0yIDI0LjYtMjIuNyA0NC4xLTQ3LjkgNDQuMXoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
