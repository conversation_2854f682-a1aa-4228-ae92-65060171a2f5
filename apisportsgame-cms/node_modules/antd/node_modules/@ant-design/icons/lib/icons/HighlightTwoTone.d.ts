import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![highlight](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIyOS42IDc5Ni4zaDE2MC4ybDU0LjMtNTQuMS04MC4xLTc4Ljl6bTIyMC43LTM5Ny4xbDI2Mi44IDI1OC45IDE0Ny4zLTE0NS0yNjIuOC0yNTl6bS03Ny4xIDE2Ni4xbDE3MS40IDE2OC45IDY4LjYtNjcuNi0xNzEuNC0xNjguOXoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTk1Ny42IDUwNy41TDYwMy4yIDE1OC4zYTcuOSA3LjkgMCAwMC0xMS4yIDBMMzUzLjMgMzkzLjVhOC4wMyA4LjAzIDAgMDAtLjEgMTEuM2wuMS4xIDQwIDM5LjQtMTE3LjIgMTE1LjNhOC4wMyA4LjAzIDAgMDAtLjEgMTEuM2wuMS4xIDM5LjUgMzguOS0xODkuMSAxODdINzIuMWMtNC40IDAtOC4xIDMuNi04LjEgOHY1NS4yYzAgNC40IDMuNiA4IDggOGgzNDQuOWMyLjEgMCA0LjEtLjggNS42LTIuM2w3Ni4xLTc1LjZMNTM5IDgzMGE3LjkgNy45IDAgMDAxMS4yIDBsMTE3LjEtMTE1LjYgNDAuMSAzOS41YTcuOSA3LjkgMCAwMDExLjIgMGwyMzguNy0yMzUuMmMzLjQtMyAzLjQtOCAuMy0xMS4yek0zODkuOCA3OTYuM0gyMjkuNmwxMzQuNC0xMzMgODAuMSA3OC45LTU0LjMgNTQuMXptMTU0LjgtNjIuMUwzNzMuMiA1NjUuM2w2OC42LTY3LjYgMTcxLjQgMTY4LjktNjguNiA2Ny42em0xNjguNS03Ni4xTDQ1MC4zIDM5OS4ybDE0Ny4zLTE0NS4xIDI2Mi44IDI1OS0xNDcuMyAxNDV6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
