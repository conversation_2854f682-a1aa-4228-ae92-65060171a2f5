import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![heat-map](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1NS43IDg1NmwtNDE2LTcyMGMtNi4yLTEwLjctMTYuOS0xNi0yNy43LTE2cy0yMS42IDUuMy0yNy43IDE2bC00MTYgNzIwQzU2IDg3Ny40IDcxLjQgOTA0IDk2IDkwNGg4MzJjMjQuNiAwIDQwLTI2LjYgMjcuNy00OHptLTc5MC40LTIzLjlMNTEyIDIzMS45IDg1OC43IDgzMkgxNjUuM3ptMzE5LTQ3NC4xbC0yMjggMzk0Yy0xMi4zIDIxLjMgMy4xIDQ4IDI3LjcgNDhoNDU1LjhjMjQuNyAwIDQwLjEtMjYuNyAyNy43LTQ4TDUzOS43IDM1OGMtNi4yLTEwLjctMTctMTYtMjcuNy0xNi0xMC44IDAtMjEuNiA1LjMtMjcuNyAxNnptMjE0IDM4NkgzMjUuN0w1MTIgNDIybDE4Ni4zIDMyMnptLTIxNC0xOTQuMWwtNTcgOTguNEM0MTUgNjY5LjUgNDMwLjQgNjk2IDQ1NSA2OTZoMTE0YzI0LjYgMCAzOS45LTI2LjUgMjcuNy00Ny43bC01Ny05OC40Yy02LjEtMTAuNi0xNi45LTE1LjktMjcuNy0xNS45cy0yMS41IDUuMy0yNy43IDE1Ljl6bTU3LjEgOTguNGgtNTguN2wyOS40LTUwLjcgMjkuMyA1MC43eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
