import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![like](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4NS45IDUzMy43YzE2LjgtMjIuMiAyNi4xLTQ5LjQgMjYuMS03Ny43IDAtNDQuOS0yNS4xLTg3LjQtNjUuNS0xMTEuMWE2Ny42NyA2Ny42NyAwIDAwLTM0LjMtOS4zSDU3Mi40bDYtMTIyLjljMS40LTI5LjctOS4xLTU3LjktMjkuNS03OS40QTEwNi42MiAxMDYuNjIgMCAwMDQ3MSA5OS45Yy01MiAwLTk4IDM1LTExMS44IDg1LjFsLTg1LjkgMzExSDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MzY0YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDYwMS4zYzkuMiAwIDE4LjItMS44IDI2LjUtNS40IDQ3LjYtMjAuMyA3OC4zLTY2LjggNzguMy0xMTguNCAwLTEyLjYtMS44LTI1LTUuNC0zNyAxNi44LTIyLjIgMjYuMS00OS40IDI2LjEtNzcuNyAwLTEyLjYtMS44LTI1LTUuNC0zNyAxNi44LTIyLjIgMjYuMS00OS40IDI2LjEtNzcuNy0uMi0xMi42LTItMjUuMS01LjYtMzcuMXpNMTg0IDg1MlY1NjhoODF2Mjg0aC04MXptNjM2LjQtMzUzbC0yMS45IDE5IDEzLjkgMjUuNGE1Ni4yIDU2LjIgMCAwMTYuOSAyNy4zYzAgMTYuNS03LjIgMzIuMi0xOS42IDQzbC0yMS45IDE5IDEzLjkgMjUuNGE1Ni4yIDU2LjIgMCAwMTYuOSAyNy4zYzAgMTYuNS03LjIgMzIuMi0xOS42IDQzbC0yMS45IDE5IDEzLjkgMjUuNGE1Ni4yIDU2LjIgMCAwMTYuOSAyNy4zYzAgMjIuNC0xMy4yIDQyLjYtMzMuNiA1MS44SDMyOVY1NjQuOGw5OS41LTM2MC41YTQ0LjEgNDQuMSAwIDAxNDIuMi0zMi4zYzcuNiAwIDE1LjEgMi4yIDIxLjEgNi43IDkuOSA3LjQgMTUuMiAxOC42IDE0LjYgMzAuNWwtOS42IDE5OC40aDMxNC40QzgyOSA0MTguNSA4NDAgNDM2LjkgODQwIDQ1NmMwIDE2LjUtNy4yIDMyLjEtMTkuNiA0M3oiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
