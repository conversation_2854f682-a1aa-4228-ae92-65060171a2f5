import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![reconciliation](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY3NiA2MjNjLTE4LjggMC0zNCAxNS4yLTM0IDM0czE1LjIgMzQgMzQgMzQgMzQtMTUuMiAzNC0zNC0xNS4yLTM0LTM0LTM0em0yMDQtNDU1SDY2OGMwLTMwLjktMjUuMS01Ni01Ni01NmgtODBjLTMwLjkgMC01NiAyNS4xLTU2IDU2SDI2NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjAwaC04OGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NDQ4YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDMzNmMxNy43IDAgMzItMTQuMyAzMi0zMnYtMTZoMzY4YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjIwMGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNDQ4IDg0OEgxNzZWNjE2aDI3MnYyMzJ6bTAtMjk2SDE3NnYtODhoMjcydjg4em0yMC0yNzJ2LTQ4aDcydi01Nmg2NHY1Nmg3MnY0OEg0Njh6bTE4MCAxNjh2NTZjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04di01NmMwLTQuNCAzLjYtOCA4LThoNDhjNC40IDAgOCAzLjYgOCA4em0yOCAzMDFjLTUwLjggMC05Mi00MS4yLTkyLTkyczQxLjItOTIgOTItOTIgOTIgNDEuMiA5MiA5Mi00MS4yIDkyLTkyIDkyem05Mi0yNDVjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04di05NmMwLTQuNCAzLjYtOCA4LThoNDhjNC40IDAgOCAzLjYgOCA4djk2em0tOTIgNjFjLTUwLjggMC05MiA0MS4yLTkyIDkyczQxLjIgOTIgOTIgOTIgOTItNDEuMiA5Mi05Mi00MS4yLTkyLTkyLTkyem0wIDEyNmMtMTguOCAwLTM0LTE1LjItMzQtMzRzMTUuMi0zNCAzNC0zNCAzNCAxNS4yIDM0IDM0LTE1LjIgMzQtMzQgMzR6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
