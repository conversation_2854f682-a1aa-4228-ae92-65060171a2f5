import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![skin](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAzMThjLTc5LjIgMC0xNDguNS00OC44LTE3Ni43LTEyMEgxODJ2MTk2aDExOXY0MzJoNDIyVjM5NGgxMTlWMTk4SDY4OC43Yy0yOC4yIDcxLjItOTcuNSAxMjAtMTc2LjcgMTIweiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODcwIDEyNkg2NjMuOGMtMTcuNCAwLTMyLjkgMTEuOS0zNyAyOS4zQzYxNC4zIDIwOC4xIDU2NyAyNDYgNTEyIDI0NnMtMTAyLjMtMzcuOS0xMTQuOC05MC43YTM3LjkzIDM3LjkzIDAgMDAtMzctMjkuM0gxNTRhNDQgNDQgMCAwMC00NCA0NHYyNTJhNDQgNDQgMCAwMDQ0IDQ0aDc1djM4OGE0NCA0NCAwIDAwNDQgNDRoNDc4YTQ0IDQ0IDAgMDA0NC00NFY0NjZoNzVhNDQgNDQgMCAwMDQ0LTQ0VjE3MGE0NCA0NCAwIDAwLTQ0LTQ0em0tMjggMjY4SDcyM3Y0MzJIMzAxVjM5NEgxODJWMTk4aDE1My4zYzI4LjIgNzEuMiA5Ny41IDEyMCAxNzYuNyAxMjBzMTQ4LjUtNDguOCAxNzYuNy0xMjBIODQydjE5NnoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
