import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![number](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3MiAzOTRjNC40IDAgOC0zLjYgOC04di02MGMwLTQuNC0zLjYtOC04LThINzA4VjE1MmMwLTQuNC0zLjYtOC04LThoLTY0Yy00LjQgMC04IDMuNi04IDh2MTY2SDQwMFYxNTJjMC00LjQtMy42LTgtOC04aC02NGMtNC40IDAtOCAzLjYtOCA4djE2NkgxNTJjLTQuNCAwLTggMy42LTggOHY2MGMwIDQuNCAzLjYgOCA4IDhoMTY4djIzNkgxNTJjLTQuNCAwLTggMy42LTggOHY2MGMwIDQuNCAzLjYgOCA4IDhoMTY4djE2NmMwIDQuNCAzLjYgOCA4IDhoNjRjNC40IDAgOC0zLjYgOC04VjcwNmgyMjh2MTY2YzAgNC40IDMuNiA4IDggOGg2NGM0LjQgMCA4LTMuNiA4LThWNzA2aDE2NGM0LjQgMCA4LTMuNiA4LTh2LTYwYzAtNC40LTMuNi04LTgtOEg3MDhWMzk0aDE2NHpNNjI4IDYzMEg0MDBWMzk0aDIyOHYyMzZ6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
