import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![trademark-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xNjQuNyA2NjAuMmMtMS4xLjUtMi4zLjgtMy41LjhoLTYyYy0zLjEgMC01LjktMS44LTcuMi00LjZsLTc0LjYtMTU5LjJoLTg4LjdWNzE3YzAgNC40LTMuNiA4LTggOEgzNzhjLTQuNCAwLTgtMy42LTgtOFYzMDdjMC00LjQgMy42LTggOC04aDE1NS42Yzk4LjggMCAxNDQuMiA1OS45IDE0NC4yIDEzMS4xIDAgNzAuMi00My42IDEwNi40LTc4LjQgMTE5LjJsODAuOCAxNjQuMmMyLjEgMy45LjQgOC43LTMuNSAxMC43ek01MjMuOSAzNTdoLTgzLjR2MTQ4SDUyMmM1MyAwIDgyLjgtMjUuNiA4Mi44LTcyLjQgMC01MC4zLTMyLjktNzUuNi04MC45LTc1LjZ6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
