import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![moon](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNDg5LjUgMTExLjY2YzMwLjY1LTEuOCA0NS45OCAzNi40NCAyMi41OCA1Ni4zM0EyNDMuMzUgMjQzLjM1IDAgMDA0MjYgMzU0YzAgMTM0Ljc2IDEwOS4yNCAyNDQgMjQ0IDI0NCA3Mi41OCAwIDEzOS45LTMxLjgzIDE4Ni4wMS04Ni4wOCAxOS44Ny0yMy4zOCA1OC4wNy04LjEgNTYuMzQgMjIuNTNDOTAwLjQgNzQ1LjgyIDcyNS4xNSA5MTIgNTEyLjUgOTEyIDI5MS4zMSA5MTIgMTEyIDczMi42OSAxMTIgNTExLjVjMC0yMTEuMzkgMTY0LjI5LTM4Ni4wMiAzNzQuMi0zOTkuNjVsLjItLjAxem0tODEuMTUgNzkuNzVsLTQuMTEgMS4zNkMyNzEuMSAyMzcuOTQgMTc2IDM2NC4wOSAxNzYgNTExLjUgMTc2IDY5Ny4zNCAzMjYuNjYgODQ4IDUxMi41IDg0OGMxNDguMjggMCAyNzQuOTQtOTYuMiAzMTkuNDUtMjMwLjQxbC42My0xLjkzLS4xMS4wN2EzMDcuMDYgMzA3LjA2IDAgMDEtMTU5LjczIDQ2LjI2TDY3MCA2NjJjLTE3MC4xIDAtMzA4LTEzNy45LTMwOC0zMDggMC01OC42IDE2LjQ4LTExNC41NCA0Ni4yNy0xNjIuNDd6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
