import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![interaction](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptMTE0LTQwMS45YzAtNTUuMyA0NC42LTEwMC4xIDk5LjctMTAwLjFoMjA1Ljh2LTUzLjRjMC01LjYgNi41LTguOCAxMC45LTUuM0w3MjMuNSAzNjVjMy41IDIuNyAzLjUgOCAwIDEwLjdsLTEwOS4xIDg1LjdjLTQuNCAzLjUtMTAuOS40LTEwLjktNS4zdi01My40SDM5Ny44Yy0xOS42IDAtMzUuNSAxNS45LTM1LjUgMzUuNnY3OC45YzAgMy44LTMuMSA2LjgtNi44IDYuOGgtNTAuN2MtMy44IDAtNi44LTMtNi44LTd2LTc4Ljl6bTIuNiAyMTAuM2wxMDkuMS04NS43YzQuNC0zLjUgMTAuOS0uNCAxMC45IDUuM3Y1My40aDIwNS42YzE5LjYgMCAzNS41LTE1LjkgMzUuNS0zNS42di03OC45YzAtMy44IDMuMS02LjggNi44LTYuOGg1MC43YzMuOCAwIDYuOCAzLjEgNi44IDYuOHY3OC45YzAgNTUuMy00NC42IDEwMC4xLTk5LjcgMTAwLjFINDIwLjZ2NTMuNGMwIDUuNi02LjUgOC44LTEwLjkgNS4zbC0xMDkuMS04NS43Yy0zLjUtMi43LTMuNS04IDAtMTAuNXoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTMwNC44IDUyNGg1MC43YzMuNyAwIDYuOC0zIDYuOC02Ljh2LTc4LjljMC0xOS43IDE1LjktMzUuNiAzNS41LTM1LjZoMjA1Ljd2NTMuNGMwIDUuNyA2LjUgOC44IDEwLjkgNS4zbDEwOS4xLTg1LjdjMy41LTIuNyAzLjUtOCAwLTEwLjdsLTEwOS4xLTg1LjdjLTQuNC0zLjUtMTAuOS0uMy0xMC45IDUuM1YzMzhIMzk3LjdjLTU1LjEgMC05OS43IDQ0LjgtOTkuNyAxMDAuMVY1MTdjMCA0IDMgNyA2LjggN3ptLTQuMiAxMzQuOWwxMDkuMSA4NS43YzQuNCAzLjUgMTAuOS4zIDEwLjktNS4zdi01My40aDIwNS43YzU1LjEgMCA5OS43LTQ0LjggOTkuNy0xMDAuMXYtNzguOWMwLTMuNy0zLTYuOC02LjgtNi44aC01MC43Yy0zLjcgMC02LjggMy02LjggNi44djc4LjljMCAxOS43LTE1LjkgMzUuNi0zNS41IDM1LjZINDIwLjZWNTY4YzAtNS43LTYuNS04LjgtMTAuOS01LjNsLTEwOS4xIDg1LjdjLTMuNSAyLjUtMy41IDcuOCAwIDEwLjV6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
