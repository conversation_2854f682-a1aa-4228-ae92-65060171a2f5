{"version": 3, "sources": [], "sections": [{"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/apisportsgame-cms/src/lib/api.ts"], "sourcesContent": ["// APISportsGame CMS - API Client\n// Base API client setup theo CMS Development Guide\n\nimport axios, { AxiosInstance, AxiosResponse } from 'axios';\nimport { \n  SystemUser, \n  RegisteredUser, \n  League, \n  Team, \n  Fixture,\n  LoginRequest,\n  LoginResponse,\n  RegisterRequest,\n  PaginatedResponse,\n  ApiError,\n  DashboardStats,\n  SyncStatus\n} from '@/types';\n\n// ============================================================================\n// API CONFIGURATION\n// ============================================================================\n\nconst API_BASE_URL = 'http://localhost:3000';\n\n// API Endpoints theo CMS Development Guide\nexport const endpoints = {\n  // Authentication endpoints\n  auth: {\n    login: '/auth/login',\n    profile: '/auth/profile',\n    refresh: '/auth/refresh',\n    logout: '/auth/logout',\n    adminRegister: '/auth/admin/register'\n  },\n  // RegisteredUser endpoints\n  users: {\n    register: '/users/register',\n    login: '/users/login',\n    verifyEmail: '/users/verify-email',\n    profile: '/users/profile',\n    apiUsage: '/users/api-usage'\n  },\n  // League endpoints\n  leagues: {\n    list: '/leagues',\n    active: '/leagues/active',\n    byId: (id: number) => `/leagues/${id}`,\n    create: '/leagues',\n    update: (id: number) => `/leagues/${id}`,\n    delete: (id: number) => `/leagues/${id}`,\n    fixtures: (id: number) => `/leagues/${id}/fixtures`\n  },\n  // Team endpoints\n  teams: {\n    list: '/teams',\n    byId: (id: number) => `/teams/${id}`,\n    create: '/teams',\n    update: (id: number) => `/teams/${id}`,\n    delete: (id: number) => `/teams/${id}`,\n    fixtures: (id: number) => `/teams/${id}/fixtures`\n  },\n  // Fixture endpoints\n  fixtures: {\n    list: '/fixtures',\n    byId: (id: number) => `/fixtures/${id}`,\n    live: '/fixtures/live',\n    today: '/fixtures/today',\n    byDate: (date: string) => `/fixtures/date/${date}`,\n    create: '/fixtures',\n    update: (id: number) => `/fixtures/${id}`,\n    delete: (id: number) => `/fixtures/${id}`\n  },\n  // Sync endpoints\n  sync: {\n    leagues: '/sync/leagues',\n    teams: '/sync/teams',\n    fixtures: '/sync/fixtures',\n    daily: '/sync/daily',\n    status: '/sync/status'\n  }\n};\n\n// ============================================================================\n// AXIOS INSTANCE SETUP\n// ============================================================================\n\nclass ApiClient {\n  private instance: AxiosInstance;\n\n  constructor() {\n    this.instance = axios.create({\n      baseURL: API_BASE_URL,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      timeout: 10000,\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor - thêm JWT token\n    this.instance.interceptors.request.use(\n      (config) => {\n        const token = this.getToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Response interceptor - xử lý errors\n    this.instance.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        // Handle 401 errors - token expired\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n          \n          try {\n            await this.refreshToken();\n            const token = this.getToken();\n            if (token) {\n              originalRequest.headers.Authorization = `Bearer ${token}`;\n              return this.instance(originalRequest);\n            }\n          } catch (refreshError) {\n            this.logout();\n            window.location.href = '/auth/login';\n          }\n        }\n\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  private getToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('access_token');\n    }\n    return null;\n  }\n\n  private setToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('access_token', token);\n    }\n  }\n\n  private removeToken(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('refresh_token');\n    }\n  }\n\n  private async refreshToken(): Promise<void> {\n    const refreshToken = typeof window !== 'undefined' \n      ? localStorage.getItem('refresh_token') \n      : null;\n    \n    if (!refreshToken) {\n      throw new Error('No refresh token');\n    }\n\n    const response = await this.instance.post(endpoints.auth.refresh, {\n      refresh_token: refreshToken\n    });\n\n    const { access_token } = response.data;\n    this.setToken(access_token);\n  }\n\n  private logout(): void {\n    this.removeToken();\n  }\n\n  // ============================================================================\n  // AUTHENTICATION METHODS\n  // ============================================================================\n\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    const response = await this.instance.post<LoginResponse>(\n      endpoints.auth.login, \n      credentials\n    );\n    \n    const { access_token, refresh_token } = response.data;\n    this.setToken(access_token);\n    \n    if (typeof window !== 'undefined') {\n      localStorage.setItem('refresh_token', refresh_token);\n    }\n    \n    return response.data;\n  }\n\n  async getProfile(): Promise<SystemUser | RegisteredUser> {\n    const response = await this.instance.get<SystemUser | RegisteredUser>(\n      endpoints.auth.profile\n    );\n    return response.data;\n  }\n\n  async logoutUser(): Promise<void> {\n    try {\n      await this.instance.post(endpoints.auth.logout);\n    } finally {\n      this.logout();\n    }\n  }\n\n  async createSystemUser(userData: any): Promise<SystemUser> {\n    const response = await this.instance.post<SystemUser>(\n      endpoints.auth.adminRegister,\n      userData\n    );\n    return response.data;\n  }\n\n  // ============================================================================\n  // LEAGUE METHODS\n  // ============================================================================\n\n  async getLeagues(params?: any): Promise<PaginatedResponse<League>> {\n    const response = await this.instance.get<PaginatedResponse<League>>(\n      endpoints.leagues.list,\n      { params }\n    );\n    return response.data;\n  }\n\n  async getLeague(id: number): Promise<League> {\n    const response = await this.instance.get<League>(\n      endpoints.leagues.byId(id)\n    );\n    return response.data;\n  }\n\n  async createLeague(leagueData: any): Promise<League> {\n    const response = await this.instance.post<League>(\n      endpoints.leagues.create,\n      leagueData\n    );\n    return response.data;\n  }\n\n  async updateLeague(id: number, leagueData: any): Promise<League> {\n    const response = await this.instance.put<League>(\n      endpoints.leagues.update(id),\n      leagueData\n    );\n    return response.data;\n  }\n\n  async deleteLeague(id: number): Promise<void> {\n    await this.instance.delete(endpoints.leagues.delete(id));\n  }\n\n  // ============================================================================\n  // TEAM METHODS\n  // ============================================================================\n\n  async getTeams(params?: any): Promise<PaginatedResponse<Team>> {\n    const response = await this.instance.get<PaginatedResponse<Team>>(\n      endpoints.teams.list,\n      { params }\n    );\n    return response.data;\n  }\n\n  async getTeam(id: number): Promise<Team> {\n    const response = await this.instance.get<Team>(\n      endpoints.teams.byId(id)\n    );\n    return response.data;\n  }\n\n  async createTeam(teamData: any): Promise<Team> {\n    const response = await this.instance.post<Team>(\n      endpoints.teams.create,\n      teamData\n    );\n    return response.data;\n  }\n\n  async updateTeam(id: number, teamData: any): Promise<Team> {\n    const response = await this.instance.put<Team>(\n      endpoints.teams.update(id),\n      teamData\n    );\n    return response.data;\n  }\n\n  async deleteTeam(id: number): Promise<void> {\n    await this.instance.delete(endpoints.teams.delete(id));\n  }\n\n  // ============================================================================\n  // FIXTURE METHODS\n  // ============================================================================\n\n  async getFixtures(params?: any): Promise<PaginatedResponse<Fixture>> {\n    const response = await this.instance.get<PaginatedResponse<Fixture>>(\n      endpoints.fixtures.list,\n      { params }\n    );\n    return response.data;\n  }\n\n  async getFixture(id: number): Promise<Fixture> {\n    const response = await this.instance.get<Fixture>(\n      endpoints.fixtures.byId(id)\n    );\n    return response.data;\n  }\n\n  async getLiveFixtures(): Promise<Fixture[]> {\n    const response = await this.instance.get<Fixture[]>(\n      endpoints.fixtures.live\n    );\n    return response.data;\n  }\n\n  async getTodayFixtures(): Promise<Fixture[]> {\n    const response = await this.instance.get<Fixture[]>(\n      endpoints.fixtures.today\n    );\n    return response.data;\n  }\n\n  // ============================================================================\n  // SYNC METHODS\n  // ============================================================================\n\n  async syncLeagues(): Promise<void> {\n    await this.instance.post(endpoints.sync.leagues);\n  }\n\n  async syncTeams(): Promise<void> {\n    await this.instance.post(endpoints.sync.teams);\n  }\n\n  async syncFixtures(): Promise<void> {\n    await this.instance.post(endpoints.sync.fixtures);\n  }\n\n  async getSyncStatus(): Promise<SyncStatus> {\n    const response = await this.instance.get<SyncStatus>(\n      endpoints.sync.status\n    );\n    return response.data;\n  }\n}\n\n// Export singleton instance\nexport const apiClient = new ApiClient();\nexport default apiClient;\n"], "names": [], "mappings": "AAAA,iCAAiC;AACjC,mDAAmD;;;;;;AAEnD;;AAgBA,+EAA+E;AAC/E,oBAAoB;AACpB,+EAA+E;AAE/E,MAAM,eAAe;AAGd,MAAM,YAAY;IACvB,2BAA2B;IAC3B,MAAM;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,eAAe;IACjB;IACA,2BAA2B;IAC3B,OAAO;QACL,UAAU;QACV,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;IACZ;IACA,mBAAmB;IACnB,SAAS;QACP,MAAM;QACN,QAAQ;QACR,MAAM,CAAC,KAAe,CAAC,SAAS,EAAE,IAAI;QACtC,QAAQ;QACR,QAAQ,CAAC,KAAe,CAAC,SAAS,EAAE,IAAI;QACxC,QAAQ,CAAC,KAAe,CAAC,SAAS,EAAE,IAAI;QACxC,UAAU,CAAC,KAAe,CAAC,SAAS,EAAE,GAAG,SAAS,CAAC;IACrD;IACA,iBAAiB;IACjB,OAAO;QACL,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,OAAO,EAAE,IAAI;QACpC,QAAQ;QACR,QAAQ,CAAC,KAAe,CAAC,OAAO,EAAE,IAAI;QACtC,QAAQ,CAAC,KAAe,CAAC,OAAO,EAAE,IAAI;QACtC,UAAU,CAAC,KAAe,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;IACnD;IACA,oBAAoB;IACpB,UAAU;QACR,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,UAAU,EAAE,IAAI;QACvC,MAAM;QACN,OAAO;QACP,QAAQ,CAAC,OAAiB,CAAC,eAAe,EAAE,MAAM;QAClD,QAAQ;QACR,QAAQ,CAAC,KAAe,CAAC,UAAU,EAAE,IAAI;QACzC,QAAQ,CAAC,KAAe,CAAC,UAAU,EAAE,IAAI;IAC3C;IACA,iBAAiB;IACjB,MAAM;QACJ,SAAS;QACT,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;IACV;AACF;AAEA,+EAA+E;AAC/E,uBAAuB;AACvB,+EAA+E;AAE/E,MAAM;IACI,SAAwB;IAEhC,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC3B,SAAS;YACT,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEQ,oBAAoB;QAC1B,uCAAuC;QACvC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;YACC,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YAClD;YACA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;QAG5B,sCAAsC;QACtC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,OAAO;YACL,MAAM,kBAAkB,MAAM,MAAM;YAEpC,oCAAoC;YACpC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;gBAC7D,gBAAgB,MAAM,GAAG;gBAEzB,IAAI;oBACF,MAAM,IAAI,CAAC,YAAY;oBACvB,MAAM,QAAQ,IAAI,CAAC,QAAQ;oBAC3B,IAAI,OAAO;wBACT,gBAAgB,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;wBACzD,OAAO,IAAI,CAAC,QAAQ,CAAC;oBACvB;gBACF,EAAE,OAAO,cAAc;oBACrB,IAAI,CAAC,MAAM;oBACX,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB;YACF;YAEA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEQ,WAA0B;QAChC,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEQ,SAAS,KAAa,EAAQ;QACpC,uCAAmC;;QAEnC;IACF;IAEQ,cAAoB;QAC1B,uCAAmC;;QAGnC;IACF;IAEA,MAAc,eAA8B;QAC1C,MAAM,eAAe,6EAEjB;QAEJ,wCAAmB;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAChE,eAAe;QACjB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,SAAS,IAAI;QACtC,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEQ,SAAe;QACrB,IAAI,CAAC,WAAW;IAClB;IAEA,+EAA+E;IAC/E,yBAAyB;IACzB,+EAA+E;IAE/E,MAAM,MAAM,WAAyB,EAA0B;QAC7D,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,UAAU,IAAI,CAAC,KAAK,EACpB;QAGF,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,SAAS,IAAI;QACrD,IAAI,CAAC,QAAQ,CAAC;QAEd,uCAAmC;;QAEnC;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAmD;QACvD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,IAAI,CAAC,OAAO;QAExB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAA4B;QAChC,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM;QAChD,SAAU;YACR,IAAI,CAAC,MAAM;QACb;IACF;IAEA,MAAM,iBAAiB,QAAa,EAAuB;QACzD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,UAAU,IAAI,CAAC,aAAa,EAC5B;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,+EAA+E;IAC/E,iBAAiB;IACjB,+EAA+E;IAE/E,MAAM,WAAW,MAAY,EAAsC;QACjE,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,OAAO,CAAC,IAAI,EACtB;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,UAAU,EAAU,EAAmB;QAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,OAAO,CAAC,IAAI,CAAC;QAEzB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,UAAe,EAAmB;QACnD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,UAAU,OAAO,CAAC,MAAM,EACxB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,EAAU,EAAE,UAAe,EAAmB;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,OAAO,CAAC,MAAM,CAAC,KACzB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,EAAU,EAAiB;QAC5C,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,OAAO,CAAC,MAAM,CAAC;IACtD;IAEA,+EAA+E;IAC/E,eAAe;IACf,+EAA+E;IAE/E,MAAM,SAAS,MAAY,EAAoC;QAC7D,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,KAAK,CAAC,IAAI,EACpB;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,QAAQ,EAAU,EAAiB;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,KAAK,CAAC,IAAI,CAAC;QAEvB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,QAAa,EAAiB;QAC7C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,UAAU,KAAK,CAAC,MAAM,EACtB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,EAAU,EAAE,QAAa,EAAiB;QACzD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,KAAK,CAAC,MAAM,CAAC,KACvB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,MAAM,CAAC;IACpD;IAEA,+EAA+E;IAC/E,kBAAkB;IAClB,+EAA+E;IAE/E,MAAM,YAAY,MAAY,EAAuC;QACnE,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,QAAQ,CAAC,IAAI,EACvB;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,QAAQ,CAAC,IAAI,CAAC;QAE1B,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,kBAAsC;QAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,QAAQ,CAAC,IAAI;QAEzB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,mBAAuC;QAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,QAAQ,CAAC,KAAK;QAE1B,OAAO,SAAS,IAAI;IACtB;IAEA,+EAA+E;IAC/E,eAAe;IACf,+EAA+E;IAE/E,MAAM,cAA6B;QACjC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO;IACjD;IAEA,MAAM,YAA2B;QAC/B,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK;IAC/C;IAEA,MAAM,eAA8B;QAClC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ;IAClD;IAEA,MAAM,gBAAqC;QACzC,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,IAAI,CAAC,MAAM;QAEvB,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY,IAAI;uCACd"}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/apisportsgame-cms/src/stores/auth-store.ts"], "sourcesContent": ["// APISportsGame CMS - Authentication Store\n// Zustand store cho dual authentication system\n\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { SystemUser, RegisteredUser, LoginRequest } from '@/types';\nimport { apiClient } from '@/lib/api';\n\n// ============================================================================\n// AUTH STORE TYPES\n// ============================================================================\n\ninterface AuthState {\n  // State\n  user: SystemUser | RegisteredUser | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n  \n  // Actions\n  login: (credentials: LoginRequest) => Promise<void>;\n  logout: () => void;\n  getProfile: () => Promise<void>;\n  clearError: () => void;\n  \n  // Helpers\n  isSystemUser: () => boolean;\n  isRegisteredUser: () => boolean;\n  hasRole: (role: string) => boolean;\n  hasTier: (tier: string) => boolean;\n}\n\n// ============================================================================\n// AUTH STORE IMPLEMENTATION\n// ============================================================================\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // ========================================================================\n      // LOGIN ACTION\n      // ========================================================================\n      login: async (credentials: LoginRequest) => {\n        set({ isLoading: true, error: null });\n        \n        try {\n          const response = await apiClient.login(credentials);\n          \n          set({\n            user: response.user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message || \n                              error.message || \n                              'Đăng nhập thất bại';\n          \n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n          \n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // LOGOUT ACTION\n      // ========================================================================\n      logout: () => {\n        try {\n          apiClient.logoutUser();\n        } catch (error) {\n          console.error('Logout error:', error);\n        } finally {\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n        }\n      },\n\n      // ========================================================================\n      // GET PROFILE ACTION\n      // ========================================================================\n      getProfile: async () => {\n        set({ isLoading: true, error: null });\n        \n        try {\n          const user = await apiClient.getProfile();\n          \n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message || \n                              error.message || \n                              'Không thể lấy thông tin người dùng';\n          \n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n          \n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // CLEAR ERROR ACTION\n      // ========================================================================\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // ========================================================================\n      // HELPER METHODS\n      // ========================================================================\n      isSystemUser: () => {\n        const { user } = get();\n        return user !== null && 'role' in user;\n      },\n\n      isRegisteredUser: () => {\n        const { user } = get();\n        return user !== null && 'tier' in user;\n      },\n\n      hasRole: (role: string) => {\n        const { user, isSystemUser } = get();\n        if (!isSystemUser() || !user) return false;\n        \n        const systemUser = user as SystemUser;\n        return systemUser.role === role;\n      },\n\n      hasTier: (tier: string) => {\n        const { user, isRegisteredUser } = get();\n        if (!isRegisteredUser() || !user) return false;\n        \n        const registeredUser = user as RegisteredUser;\n        return registeredUser.tier === tier;\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// ============================================================================\n// AUTH HOOKS\n// ============================================================================\n\n// Hook để check permissions\nexport const usePermissions = () => {\n  const { user, isSystemUser, isRegisteredUser, hasRole, hasTier } = useAuthStore();\n\n  return {\n    // User type checks\n    isSystemUser: isSystemUser(),\n    isRegisteredUser: isRegisteredUser(),\n    \n    // Role checks (SystemUser)\n    isAdmin: hasRole('admin'),\n    isEditor: hasRole('editor'),\n    isModerator: hasRole('moderator'),\n    \n    // Tier checks (RegisteredUser)\n    isFree: hasTier('free'),\n    isPremium: hasTier('premium'),\n    isEnterprise: hasTier('enterprise'),\n    \n    // Permission checks\n    canManageUsers: hasRole('admin'),\n    canManageLeagues: hasRole('admin') || hasRole('editor'),\n    canManageTeams: hasRole('admin') || hasRole('editor'),\n    canManageFixtures: hasRole('admin') || hasRole('editor') || hasRole('moderator'),\n    canSync: hasRole('admin') || hasRole('editor'),\n    canViewAnalytics: hasRole('admin') || hasRole('editor'),\n    \n    // Current user\n    currentUser: user,\n  };\n};\n\n// Hook để check authentication status\nexport const useAuth = () => {\n  const { \n    user, \n    isAuthenticated, \n    isLoading, \n    error, \n    login, \n    logout, \n    getProfile, \n    clearError \n  } = useAuthStore();\n\n  return {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    getProfile,\n    clearError,\n  };\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,+CAA+C;;;;;;AAK/C;AAHA;AACA;;;;AAgCO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,2EAA2E;QAC3E,eAAe;QACf,2EAA2E;QAC3E,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,KAAK,CAAC;gBAEvC,IAAI;oBACF,MAAM,SAAS,IAAI;oBACnB,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACvB,MAAM,OAAO,IACb;gBAEpB,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,gBAAgB;QAChB,2EAA2E;QAC3E,QAAQ;YACN,IAAI;gBACF,iHAAA,CAAA,YAAS,CAAC,UAAU;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC,SAAU;gBACR,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,OAAO,MAAM,iHAAA,CAAA,YAAS,CAAC,UAAU;gBAEvC,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACvB,MAAM,OAAO,IACb;gBAEpB,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,2EAA2E;QAC3E,iBAAiB;QACjB,2EAA2E;QAC3E,cAAc;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,SAAS,QAAQ,UAAU;QACpC;QAEA,kBAAkB;YAChB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,SAAS,QAAQ,UAAU;QACpC;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG;YAC/B,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO;YAErC,MAAM,aAAa;YACnB,OAAO,WAAW,IAAI,KAAK;QAC7B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG;YACnC,IAAI,CAAC,sBAAsB,CAAC,MAAM,OAAO;YAEzC,MAAM,iBAAiB;YACvB,OAAO,eAAe,IAAI,KAAK;QACjC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AASG,MAAM,iBAAiB;IAC5B,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnE,OAAO;QACL,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAElB,2BAA2B;QAC3B,SAAS,QAAQ;QACjB,UAAU,QAAQ;QAClB,aAAa,QAAQ;QAErB,+BAA+B;QAC/B,QAAQ,QAAQ;QAChB,WAAW,QAAQ;QACnB,cAAc,QAAQ;QAEtB,oBAAoB;QACpB,gBAAgB,QAAQ;QACxB,kBAAkB,QAAQ,YAAY,QAAQ;QAC9C,gBAAgB,QAAQ,YAAY,QAAQ;QAC5C,mBAAmB,QAAQ,YAAY,QAAQ,aAAa,QAAQ;QACpE,SAAS,QAAQ,YAAY,QAAQ;QACrC,kBAAkB,QAAQ,YAAY,QAAQ;QAE9C,eAAe;QACf,aAAa;IACf;AACF;AAGO,MAAM,UAAU;IACrB,MAAM,EACJ,IAAI,EACJ,eAAe,EACf,SAAS,EACT,KAAK,EACL,KAAK,EACL,MAAM,EACN,UAAU,EACV,UAAU,EACX,GAAG;IAEJ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF"}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/apisportsgame-cms/src/app/page.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Home Page\n// Redirect to appropriate page based on authentication status\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Spin } from 'antd';\nimport { useAuth } from '@/stores/auth-store';\n\nexport default function Home() {\n  const router = useRouter();\n  const { isAuthenticated, isLoading } = useAuth();\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (isAuthenticated) {\n        router.push('/dashboard');\n      } else {\n        router.push('/auth/login');\n      }\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <Spin size=\"large\" tip=\"Đang tải...\" />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,gCAAgC;AAChC,8DAA8D;AAE9D;AACA;AAEA;AADA;AAPA;;;;;;AAUe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,8KAAA,CAAA,OAAI;YAAC,MAAK;YAAQ,KAAI;;;;;;;;;;;AAG7B"}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}