module.exports = {

"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/6BZD2SY2.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@tanstack_query-devtools_build_7b18f1._.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/6BZD2SY2.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/G73NX4V5.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@tanstack_query-devtools_build_929d13._.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/G73NX4V5.js [app-ssr] (ecmascript)");
    });
});
}}),

};