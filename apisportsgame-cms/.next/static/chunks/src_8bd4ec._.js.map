{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/apisportsgame-cms/src/lib/api.ts"], "sourcesContent": ["// APISportsGame CMS - API Client\n// Base API client setup theo CMS Development Guide\n\nimport axios, { AxiosInstance, AxiosResponse } from 'axios';\nimport { \n  SystemUser, \n  RegisteredUser, \n  League, \n  Team, \n  Fixture,\n  LoginRequest,\n  LoginResponse,\n  RegisterRequest,\n  PaginatedResponse,\n  ApiError,\n  DashboardStats,\n  SyncStatus\n} from '@/types';\n\n// ============================================================================\n// API CONFIGURATION\n// ============================================================================\n\nconst API_BASE_URL = 'http://localhost:3000';\n\n// API Endpoints theo CMS Development Guide\nexport const endpoints = {\n  // Authentication endpoints\n  auth: {\n    login: '/auth/login',\n    profile: '/auth/profile',\n    refresh: '/auth/refresh',\n    logout: '/auth/logout',\n    adminRegister: '/auth/admin/register'\n  },\n  // RegisteredUser endpoints\n  users: {\n    register: '/users/register',\n    login: '/users/login',\n    verifyEmail: '/users/verify-email',\n    profile: '/users/profile',\n    apiUsage: '/users/api-usage'\n  },\n  // League endpoints\n  leagues: {\n    list: '/leagues',\n    active: '/leagues/active',\n    byId: (id: number) => `/leagues/${id}`,\n    create: '/leagues',\n    update: (id: number) => `/leagues/${id}`,\n    delete: (id: number) => `/leagues/${id}`,\n    fixtures: (id: number) => `/leagues/${id}/fixtures`\n  },\n  // Team endpoints\n  teams: {\n    list: '/teams',\n    byId: (id: number) => `/teams/${id}`,\n    create: '/teams',\n    update: (id: number) => `/teams/${id}`,\n    delete: (id: number) => `/teams/${id}`,\n    fixtures: (id: number) => `/teams/${id}/fixtures`\n  },\n  // Fixture endpoints\n  fixtures: {\n    list: '/fixtures',\n    byId: (id: number) => `/fixtures/${id}`,\n    live: '/fixtures/live',\n    today: '/fixtures/today',\n    byDate: (date: string) => `/fixtures/date/${date}`,\n    create: '/fixtures',\n    update: (id: number) => `/fixtures/${id}`,\n    delete: (id: number) => `/fixtures/${id}`\n  },\n  // Sync endpoints\n  sync: {\n    leagues: '/sync/leagues',\n    teams: '/sync/teams',\n    fixtures: '/sync/fixtures',\n    daily: '/sync/daily',\n    status: '/sync/status'\n  }\n};\n\n// ============================================================================\n// AXIOS INSTANCE SETUP\n// ============================================================================\n\nclass ApiClient {\n  private instance: AxiosInstance;\n\n  constructor() {\n    this.instance = axios.create({\n      baseURL: API_BASE_URL,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      timeout: 10000,\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor - thêm JWT token\n    this.instance.interceptors.request.use(\n      (config) => {\n        const token = this.getToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Response interceptor - xử lý errors\n    this.instance.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        // Handle 401 errors - token expired\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n          \n          try {\n            await this.refreshToken();\n            const token = this.getToken();\n            if (token) {\n              originalRequest.headers.Authorization = `Bearer ${token}`;\n              return this.instance(originalRequest);\n            }\n          } catch (refreshError) {\n            this.logout();\n            window.location.href = '/auth/login';\n          }\n        }\n\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  private getToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('access_token');\n    }\n    return null;\n  }\n\n  private setToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('access_token', token);\n    }\n  }\n\n  private removeToken(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('refresh_token');\n    }\n  }\n\n  private async refreshToken(): Promise<void> {\n    const refreshToken = typeof window !== 'undefined' \n      ? localStorage.getItem('refresh_token') \n      : null;\n    \n    if (!refreshToken) {\n      throw new Error('No refresh token');\n    }\n\n    const response = await this.instance.post(endpoints.auth.refresh, {\n      refresh_token: refreshToken\n    });\n\n    const { access_token } = response.data;\n    this.setToken(access_token);\n  }\n\n  private logout(): void {\n    this.removeToken();\n  }\n\n  // ============================================================================\n  // AUTHENTICATION METHODS\n  // ============================================================================\n\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    const response = await this.instance.post<LoginResponse>(\n      endpoints.auth.login, \n      credentials\n    );\n    \n    const { access_token, refresh_token } = response.data;\n    this.setToken(access_token);\n    \n    if (typeof window !== 'undefined') {\n      localStorage.setItem('refresh_token', refresh_token);\n    }\n    \n    return response.data;\n  }\n\n  async getProfile(): Promise<SystemUser | RegisteredUser> {\n    const response = await this.instance.get<SystemUser | RegisteredUser>(\n      endpoints.auth.profile\n    );\n    return response.data;\n  }\n\n  async logoutUser(): Promise<void> {\n    try {\n      await this.instance.post(endpoints.auth.logout);\n    } finally {\n      this.logout();\n    }\n  }\n\n  async createSystemUser(userData: any): Promise<SystemUser> {\n    const response = await this.instance.post<SystemUser>(\n      endpoints.auth.adminRegister,\n      userData\n    );\n    return response.data;\n  }\n\n  // ============================================================================\n  // LEAGUE METHODS\n  // ============================================================================\n\n  async getLeagues(params?: any): Promise<PaginatedResponse<League>> {\n    const response = await this.instance.get<PaginatedResponse<League>>(\n      endpoints.leagues.list,\n      { params }\n    );\n    return response.data;\n  }\n\n  async getLeague(id: number): Promise<League> {\n    const response = await this.instance.get<League>(\n      endpoints.leagues.byId(id)\n    );\n    return response.data;\n  }\n\n  async createLeague(leagueData: any): Promise<League> {\n    const response = await this.instance.post<League>(\n      endpoints.leagues.create,\n      leagueData\n    );\n    return response.data;\n  }\n\n  async updateLeague(id: number, leagueData: any): Promise<League> {\n    const response = await this.instance.put<League>(\n      endpoints.leagues.update(id),\n      leagueData\n    );\n    return response.data;\n  }\n\n  async deleteLeague(id: number): Promise<void> {\n    await this.instance.delete(endpoints.leagues.delete(id));\n  }\n\n  // ============================================================================\n  // TEAM METHODS\n  // ============================================================================\n\n  async getTeams(params?: any): Promise<PaginatedResponse<Team>> {\n    const response = await this.instance.get<PaginatedResponse<Team>>(\n      endpoints.teams.list,\n      { params }\n    );\n    return response.data;\n  }\n\n  async getTeam(id: number): Promise<Team> {\n    const response = await this.instance.get<Team>(\n      endpoints.teams.byId(id)\n    );\n    return response.data;\n  }\n\n  async createTeam(teamData: any): Promise<Team> {\n    const response = await this.instance.post<Team>(\n      endpoints.teams.create,\n      teamData\n    );\n    return response.data;\n  }\n\n  async updateTeam(id: number, teamData: any): Promise<Team> {\n    const response = await this.instance.put<Team>(\n      endpoints.teams.update(id),\n      teamData\n    );\n    return response.data;\n  }\n\n  async deleteTeam(id: number): Promise<void> {\n    await this.instance.delete(endpoints.teams.delete(id));\n  }\n\n  // ============================================================================\n  // FIXTURE METHODS\n  // ============================================================================\n\n  async getFixtures(params?: any): Promise<PaginatedResponse<Fixture>> {\n    const response = await this.instance.get<PaginatedResponse<Fixture>>(\n      endpoints.fixtures.list,\n      { params }\n    );\n    return response.data;\n  }\n\n  async getFixture(id: number): Promise<Fixture> {\n    const response = await this.instance.get<Fixture>(\n      endpoints.fixtures.byId(id)\n    );\n    return response.data;\n  }\n\n  async getLiveFixtures(): Promise<Fixture[]> {\n    const response = await this.instance.get<Fixture[]>(\n      endpoints.fixtures.live\n    );\n    return response.data;\n  }\n\n  async getTodayFixtures(): Promise<Fixture[]> {\n    const response = await this.instance.get<Fixture[]>(\n      endpoints.fixtures.today\n    );\n    return response.data;\n  }\n\n  // ============================================================================\n  // SYNC METHODS\n  // ============================================================================\n\n  async syncLeagues(): Promise<void> {\n    await this.instance.post(endpoints.sync.leagues);\n  }\n\n  async syncTeams(): Promise<void> {\n    await this.instance.post(endpoints.sync.teams);\n  }\n\n  async syncFixtures(): Promise<void> {\n    await this.instance.post(endpoints.sync.fixtures);\n  }\n\n  async getSyncStatus(): Promise<SyncStatus> {\n    const response = await this.instance.get<SyncStatus>(\n      endpoints.sync.status\n    );\n    return response.data;\n  }\n}\n\n// Export singleton instance\nexport const apiClient = new ApiClient();\nexport default apiClient;\n"], "names": [], "mappings": "AAAA,iCAAiC;AACjC,mDAAmD;;;;;;AAEnD;;AAgBA,+EAA+E;AAC/E,oBAAoB;AACpB,+EAA+E;AAE/E,MAAM,eAAe;AAGd,MAAM,YAAY;IACvB,2BAA2B;IAC3B,MAAM;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,eAAe;IACjB;IACA,2BAA2B;IAC3B,OAAO;QACL,UAAU;QACV,OAAO;QACP,aAAa;QACb,SAAS;QACT,UAAU;IACZ;IACA,mBAAmB;IACnB,SAAS;QACP,MAAM;QACN,QAAQ;QACR,MAAM,CAAC,KAAe,CAAC,SAAS,EAAE,IAAI;QACtC,QAAQ;QACR,QAAQ,CAAC,KAAe,CAAC,SAAS,EAAE,IAAI;QACxC,QAAQ,CAAC,KAAe,CAAC,SAAS,EAAE,IAAI;QACxC,UAAU,CAAC,KAAe,CAAC,SAAS,EAAE,GAAG,SAAS,CAAC;IACrD;IACA,iBAAiB;IACjB,OAAO;QACL,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,OAAO,EAAE,IAAI;QACpC,QAAQ;QACR,QAAQ,CAAC,KAAe,CAAC,OAAO,EAAE,IAAI;QACtC,QAAQ,CAAC,KAAe,CAAC,OAAO,EAAE,IAAI;QACtC,UAAU,CAAC,KAAe,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;IACnD;IACA,oBAAoB;IACpB,UAAU;QACR,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,UAAU,EAAE,IAAI;QACvC,MAAM;QACN,OAAO;QACP,QAAQ,CAAC,OAAiB,CAAC,eAAe,EAAE,MAAM;QAClD,QAAQ;QACR,QAAQ,CAAC,KAAe,CAAC,UAAU,EAAE,IAAI;QACzC,QAAQ,CAAC,KAAe,CAAC,UAAU,EAAE,IAAI;IAC3C;IACA,iBAAiB;IACjB,MAAM;QACJ,SAAS;QACT,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;IACV;AACF;AAEA,+EAA+E;AAC/E,uBAAuB;AACvB,+EAA+E;AAE/E,MAAM;IACI,SAAwB;IAEhC,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAC3B,SAAS;YACT,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEQ,oBAAoB;QAC1B,uCAAuC;QACvC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;YACC,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YAClD;YACA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;QAG5B,sCAAsC;QACtC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,OAAO;YACL,MAAM,kBAAkB,MAAM,MAAM;YAEpC,oCAAoC;YACpC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;gBAC7D,gBAAgB,MAAM,GAAG;gBAEzB,IAAI;oBACF,MAAM,IAAI,CAAC,YAAY;oBACvB,MAAM,QAAQ,IAAI,CAAC,QAAQ;oBAC3B,IAAI,OAAO;wBACT,gBAAgB,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;wBACzD,OAAO,IAAI,CAAC,QAAQ,CAAC;oBACvB;gBACF,EAAE,OAAO,cAAc;oBACrB,IAAI,CAAC,MAAM;oBACX,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB;YACF;YAEA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEQ,WAA0B;QAChC,wCAAmC;YACjC,OAAO,aAAa,OAAO,CAAC;QAC9B;;IAEF;IAEQ,SAAS,KAAa,EAAQ;QACpC,wCAAmC;YACjC,aAAa,OAAO,CAAC,gBAAgB;QACvC;IACF;IAEQ,cAAoB;QAC1B,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,MAAc,eAA8B;QAC1C,MAAM,eAAe,uCACjB,aAAa,OAAO,CAAC;QAGzB,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAChE,eAAe;QACjB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,SAAS,IAAI;QACtC,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEQ,SAAe;QACrB,IAAI,CAAC,WAAW;IAClB;IAEA,+EAA+E;IAC/E,yBAAyB;IACzB,+EAA+E;IAE/E,MAAM,MAAM,WAAyB,EAA0B;QAC7D,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,UAAU,IAAI,CAAC,KAAK,EACpB;QAGF,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,SAAS,IAAI;QACrD,IAAI,CAAC,QAAQ,CAAC;QAEd,wCAAmC;YACjC,aAAa,OAAO,CAAC,iBAAiB;QACxC;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAmD;QACvD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,IAAI,CAAC,OAAO;QAExB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAA4B;QAChC,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM;QAChD,SAAU;YACR,IAAI,CAAC,MAAM;QACb;IACF;IAEA,MAAM,iBAAiB,QAAa,EAAuB;QACzD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,UAAU,IAAI,CAAC,aAAa,EAC5B;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,+EAA+E;IAC/E,iBAAiB;IACjB,+EAA+E;IAE/E,MAAM,WAAW,MAAY,EAAsC;QACjE,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,OAAO,CAAC,IAAI,EACtB;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,UAAU,EAAU,EAAmB;QAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,OAAO,CAAC,IAAI,CAAC;QAEzB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,UAAe,EAAmB;QACnD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,UAAU,OAAO,CAAC,MAAM,EACxB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,EAAU,EAAE,UAAe,EAAmB;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,OAAO,CAAC,MAAM,CAAC,KACzB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,EAAU,EAAiB;QAC5C,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,OAAO,CAAC,MAAM,CAAC;IACtD;IAEA,+EAA+E;IAC/E,eAAe;IACf,+EAA+E;IAE/E,MAAM,SAAS,MAAY,EAAoC;QAC7D,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,KAAK,CAAC,IAAI,EACpB;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,QAAQ,EAAU,EAAiB;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,KAAK,CAAC,IAAI,CAAC;QAEvB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,QAAa,EAAiB;QAC7C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvC,UAAU,KAAK,CAAC,MAAM,EACtB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,EAAU,EAAE,QAAa,EAAiB;QACzD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,KAAK,CAAC,MAAM,CAAC,KACvB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,MAAM,CAAC;IACpD;IAEA,+EAA+E;IAC/E,kBAAkB;IAClB,+EAA+E;IAE/E,MAAM,YAAY,MAAY,EAAuC;QACnE,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,QAAQ,CAAC,IAAI,EACvB;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,QAAQ,CAAC,IAAI,CAAC;QAE1B,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,kBAAsC;QAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,QAAQ,CAAC,IAAI;QAEzB,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,mBAAuC;QAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,QAAQ,CAAC,KAAK;QAE1B,OAAO,SAAS,IAAI;IACtB;IAEA,+EAA+E;IAC/E,eAAe;IACf,+EAA+E;IAE/E,MAAM,cAA6B;QACjC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO;IACjD;IAEA,MAAM,YAA2B;QAC/B,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK;IAC/C;IAEA,MAAM,eAA8B;QAClC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ;IAClD;IAEA,MAAM,gBAAqC;QACzC,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtC,UAAU,IAAI,CAAC,MAAM;QAEvB,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY,IAAI;uCACd"}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/apisportsgame-cms/src/stores/auth-store.ts"], "sourcesContent": ["// APISportsGame CMS - Authentication Store\n// Zustand store cho dual authentication system\n\nimport { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { SystemUser, RegisteredUser, LoginRequest } from '@/types';\nimport { apiClient } from '@/lib/api';\n\n// ============================================================================\n// AUTH STORE TYPES\n// ============================================================================\n\ninterface AuthState {\n  // State\n  user: SystemUser | RegisteredUser | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n  \n  // Actions\n  login: (credentials: LoginRequest) => Promise<void>;\n  logout: () => void;\n  getProfile: () => Promise<void>;\n  clearError: () => void;\n  \n  // Helpers\n  isSystemUser: () => boolean;\n  isRegisteredUser: () => boolean;\n  hasRole: (role: string) => boolean;\n  hasTier: (tier: string) => boolean;\n}\n\n// ============================================================================\n// AUTH STORE IMPLEMENTATION\n// ============================================================================\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // ========================================================================\n      // LOGIN ACTION\n      // ========================================================================\n      login: async (credentials: LoginRequest) => {\n        set({ isLoading: true, error: null });\n        \n        try {\n          const response = await apiClient.login(credentials);\n          \n          set({\n            user: response.user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message || \n                              error.message || \n                              'Đăng nhập thất bại';\n          \n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n          \n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // LOGOUT ACTION\n      // ========================================================================\n      logout: () => {\n        try {\n          apiClient.logoutUser();\n        } catch (error) {\n          console.error('Logout error:', error);\n        } finally {\n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null,\n          });\n        }\n      },\n\n      // ========================================================================\n      // GET PROFILE ACTION\n      // ========================================================================\n      getProfile: async () => {\n        set({ isLoading: true, error: null });\n        \n        try {\n          const user = await apiClient.getProfile();\n          \n          set({\n            user,\n            isAuthenticated: true,\n            isLoading: false,\n            error: null,\n          });\n        } catch (error: any) {\n          const errorMessage = error.response?.data?.message || \n                              error.message || \n                              'Không thể lấy thông tin người dùng';\n          \n          set({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: errorMessage,\n          });\n          \n          throw error;\n        }\n      },\n\n      // ========================================================================\n      // CLEAR ERROR ACTION\n      // ========================================================================\n      clearError: () => {\n        set({ error: null });\n      },\n\n      // ========================================================================\n      // HELPER METHODS\n      // ========================================================================\n      isSystemUser: () => {\n        const { user } = get();\n        return user !== null && 'role' in user;\n      },\n\n      isRegisteredUser: () => {\n        const { user } = get();\n        return user !== null && 'tier' in user;\n      },\n\n      hasRole: (role: string) => {\n        const { user, isSystemUser } = get();\n        if (!isSystemUser() || !user) return false;\n        \n        const systemUser = user as SystemUser;\n        return systemUser.role === role;\n      },\n\n      hasTier: (tier: string) => {\n        const { user, isRegisteredUser } = get();\n        if (!isRegisteredUser() || !user) return false;\n        \n        const registeredUser = user as RegisteredUser;\n        return registeredUser.tier === tier;\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// ============================================================================\n// AUTH HOOKS\n// ============================================================================\n\n// Hook để check permissions\nexport const usePermissions = () => {\n  const { user, isSystemUser, isRegisteredUser, hasRole, hasTier } = useAuthStore();\n\n  return {\n    // User type checks\n    isSystemUser: isSystemUser(),\n    isRegisteredUser: isRegisteredUser(),\n    \n    // Role checks (SystemUser)\n    isAdmin: hasRole('admin'),\n    isEditor: hasRole('editor'),\n    isModerator: hasRole('moderator'),\n    \n    // Tier checks (RegisteredUser)\n    isFree: hasTier('free'),\n    isPremium: hasTier('premium'),\n    isEnterprise: hasTier('enterprise'),\n    \n    // Permission checks\n    canManageUsers: hasRole('admin'),\n    canManageLeagues: hasRole('admin') || hasRole('editor'),\n    canManageTeams: hasRole('admin') || hasRole('editor'),\n    canManageFixtures: hasRole('admin') || hasRole('editor') || hasRole('moderator'),\n    canSync: hasRole('admin') || hasRole('editor'),\n    canViewAnalytics: hasRole('admin') || hasRole('editor'),\n    \n    // Current user\n    currentUser: user,\n  };\n};\n\n// Hook để check authentication status\nexport const useAuth = () => {\n  const { \n    user, \n    isAuthenticated, \n    isLoading, \n    error, \n    login, \n    logout, \n    getProfile, \n    clearError \n  } = useAuthStore();\n\n  return {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    getProfile,\n    clearError,\n  };\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,+CAA+C;;;;;;AAK/C;AAHA;AACA;;;;;AAgCO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,2EAA2E;QAC3E,eAAe;QACf,2EAA2E;QAC3E,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAC;gBAEvC,IAAI;oBACF,MAAM,SAAS,IAAI;oBACnB,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACvB,MAAM,OAAO,IACb;gBAEpB,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,gBAAgB;QAChB,2EAA2E;QAC3E,QAAQ;YACN,IAAI;gBACF,oHAAA,CAAA,YAAS,CAAC,UAAU;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC,SAAU;gBACR,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,OAAO,MAAM,oHAAA,CAAA,YAAS,CAAC,UAAU;gBAEvC,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACvB,MAAM,OAAO,IACb;gBAEpB,IAAI;oBACF,MAAM;oBACN,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBAEA,MAAM;YACR;QACF;QAEA,2EAA2E;QAC3E,qBAAqB;QACrB,2EAA2E;QAC3E,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,2EAA2E;QAC3E,iBAAiB;QACjB,2EAA2E;QAC3E,cAAc;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,SAAS,QAAQ,UAAU;QACpC;QAEA,kBAAkB;YAChB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,SAAS,QAAQ,UAAU;QACpC;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG;YAC/B,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO;YAErC,MAAM,aAAa;YACnB,OAAO,WAAW,IAAI,KAAK;QAC7B;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG;YACnC,IAAI,CAAC,sBAAsB,CAAC,MAAM,OAAO;YAEzC,MAAM,iBAAiB;YACvB,OAAO,eAAe,IAAI,KAAK;QACjC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AASG,MAAM,iBAAiB;;IAC5B,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnE,OAAO;QACL,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAElB,2BAA2B;QAC3B,SAAS,QAAQ;QACjB,UAAU,QAAQ;QAClB,aAAa,QAAQ;QAErB,+BAA+B;QAC/B,QAAQ,QAAQ;QAChB,WAAW,QAAQ;QACnB,cAAc,QAAQ;QAEtB,oBAAoB;QACpB,gBAAgB,QAAQ;QACxB,kBAAkB,QAAQ,YAAY,QAAQ;QAC9C,gBAAgB,QAAQ,YAAY,QAAQ;QAC5C,mBAAmB,QAAQ,YAAY,QAAQ,aAAa,QAAQ;QACpE,SAAS,QAAQ,YAAY,QAAQ;QACrC,kBAAkB,QAAQ,YAAY,QAAQ;QAE9C,eAAe;QACf,aAAa;IACf;AACF;GA7Ba;;QACwD;;;AA+B9D,MAAM,UAAU;;IACrB,MAAM,EACJ,IAAI,EACJ,eAAe,EACf,SAAS,EACT,KAAK,EACL,KAAK,EACL,MAAM,EACN,UAAU,EACV,UAAU,EACX,GAAG;IAEJ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IAtBa;;QAUP"}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/apisportsgame-cms/src/components/layouts/dashboard-layout.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Dashboard Layout\n// Main layout cho dashboard với navigation và authentication\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport {\n  Layout,\n  Menu,\n  Avatar,\n  Dropdown,\n  Typography,\n  Space,\n  Button,\n  Badge,\n  Spin,\n  Alert\n} from 'antd';\nimport {\n  DashboardOutlined,\n  UserOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  SyncOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n  BellOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  ApiOutlined\n} from '@ant-design/icons';\nimport { useAuth, usePermissions } from '@/stores/auth-store';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title, Text } = Typography;\n\n// ============================================================================\n// DASHBOARD LAYOUT COMPONENT\n// ============================================================================\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, isAuthenticated, isLoading, logout, getProfile } = useAuth();\n  const permissions = usePermissions();\n  \n  const [collapsed, setCollapsed] = useState(false);\n  const [mounted, setMounted] = useState(false);\n\n  // ========================================================================\n  // EFFECTS\n  // ========================================================================\n  \n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  // Check authentication và load profile\n  useEffect(() => {\n    if (!isAuthenticated && !isLoading) {\n      router.push('/auth/login');\n      return;\n    }\n\n    if (isAuthenticated && !user) {\n      getProfile().catch(() => {\n        router.push('/auth/login');\n      });\n    }\n  }, [isAuthenticated, isLoading, user, router, getProfile]);\n\n  // ========================================================================\n  // MENU CONFIGURATION\n  // ========================================================================\n  \n  const getMenuItems = () => {\n    const items = [\n      {\n        key: '/dashboard',\n        icon: <DashboardOutlined />,\n        label: 'Dashboard',\n      }\n    ];\n\n    // User Management - chỉ admin\n    if (permissions.canManageUsers) {\n      items.push({\n        key: '/dashboard/users',\n        icon: <UserOutlined />,\n        label: 'Quản lý Users',\n      });\n    }\n\n    // Sports Data Management\n    if (permissions.canManageLeagues) {\n      items.push({\n        key: '/dashboard/leagues',\n        icon: <TrophyOutlined />,\n        label: 'Quản lý Leagues',\n      });\n    }\n\n    if (permissions.canManageTeams) {\n      items.push({\n        key: '/dashboard/teams',\n        icon: <TeamOutlined />,\n        label: 'Quản lý Teams',\n      });\n    }\n\n    if (permissions.canManageFixtures) {\n      items.push({\n        key: '/dashboard/fixtures',\n        icon: <CalendarOutlined />,\n        label: 'Quản lý Fixtures',\n      });\n    }\n\n    // Analytics\n    if (permissions.canViewAnalytics) {\n      items.push({\n        key: '/dashboard/analytics',\n        icon: <BarChartOutlined />,\n        label: 'Analytics',\n      });\n    }\n\n    // Sync Operations\n    if (permissions.canSync) {\n      items.push({\n        key: '/dashboard/sync',\n        icon: <SyncOutlined />,\n        label: 'Sync Operations',\n      });\n    }\n\n    return items;\n  };\n\n  // ========================================================================\n  // USER DROPDOWN MENU\n  // ========================================================================\n  \n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: 'Thông tin cá nhân',\n      onClick: () => router.push('/dashboard/profile'),\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: 'Cài đặt',\n      onClick: () => router.push('/dashboard/settings'),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: 'Đăng xuất',\n      onClick: () => {\n        logout();\n        router.push('/auth/login');\n      },\n    },\n  ];\n\n  // ========================================================================\n  // HANDLERS\n  // ========================================================================\n  \n  const handleMenuClick = ({ key }: { key: string }) => {\n    router.push(key);\n  };\n\n  const toggleCollapsed = () => {\n    setCollapsed(!collapsed);\n  };\n\n  // ========================================================================\n  // LOADING STATE\n  // ========================================================================\n  \n  if (!mounted || isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Spin size=\"large\" tip=\"Đang tải...\" />\n      </div>\n    );\n  }\n\n  // ========================================================================\n  // UNAUTHENTICATED STATE\n  // ========================================================================\n  \n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Alert\n          message=\"Chưa đăng nhập\"\n          description=\"Vui lòng đăng nhập để tiếp tục\"\n          type=\"warning\"\n          showIcon\n        />\n      </div>\n    );\n  }\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n  \n  return (\n    <Layout className=\"min-h-screen\">\n      {/* Sidebar */}\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={collapsed}\n        theme=\"dark\"\n        width={256}\n        className=\"shadow-lg\"\n      >\n        {/* Logo */}\n        <div className=\"h-16 flex items-center justify-center border-b border-gray-700\">\n          <Space>\n            <ApiOutlined className=\"text-white text-xl\" />\n            {!collapsed && (\n              <Title level={4} className=\"text-white m-0\">\n                CMS\n              </Title>\n            )}\n          </Space>\n        </div>\n\n        {/* Navigation Menu */}\n        <Menu\n          theme=\"dark\"\n          mode=\"inline\"\n          selectedKeys={[pathname]}\n          items={getMenuItems()}\n          onClick={handleMenuClick}\n          className=\"border-r-0\"\n        />\n      </Sider>\n\n      {/* Main Layout */}\n      <Layout>\n        {/* Header */}\n        <Header className=\"bg-white shadow-sm px-4 flex items-center justify-between\">\n          {/* Left side */}\n          <Space>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={toggleCollapsed}\n              className=\"text-lg\"\n            />\n            <Title level={4} className=\"m-0\">\n              APISportsGame CMS\n            </Title>\n          </Space>\n\n          {/* Right side */}\n          <Space size=\"middle\">\n            {/* Notifications */}\n            <Badge count={0} showZero={false}>\n              <Button\n                type=\"text\"\n                icon={<BellOutlined />}\n                className=\"text-lg\"\n              />\n            </Badge>\n\n            {/* User Info */}\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space className=\"cursor-pointer hover:bg-gray-50 px-2 py-1 rounded\">\n                <Avatar\n                  size=\"small\"\n                  icon={<UserOutlined />}\n                  className=\"bg-blue-500\"\n                />\n                <div className=\"text-left\">\n                  <div className=\"text-sm font-medium\">\n                    {user?.username}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    {permissions.isSystemUser && `${(user as any)?.role}`}\n                    {permissions.isRegisteredUser && `${(user as any)?.tier}`}\n                  </div>\n                </div>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n\n        {/* Content */}\n        <Content className=\"p-6 bg-gray-50 overflow-auto\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n}\n\nexport default DashboardLayout;\n"], "names": [], "mappings": ";;;;;AAEA,uCAAuC;AACvC,6DAA6D;AAE7D;AACA;AA4BA;AA3BA;AAAA;AAYA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;AAYA;AAZA;AAAA;AAYA;AAAA;AAZA;AAYA;AAZA;AAAA;AAAA;AAAA;;;AAPA;;;;;;AAoCA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAU3B,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IAChE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACvE,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,2EAA2E;IAC3E,UAAU;IACV,2EAA2E;IAE3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,WAAW;QACb;oCAAG,EAAE;IAEL,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,WAAW;gBAClC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,mBAAmB,CAAC,MAAM;gBAC5B,aAAa,KAAK;iDAAC;wBACjB,OAAO,IAAI,CAAC;oBACd;;YACF;QACF;oCAAG;QAAC;QAAiB;QAAW;QAAM;QAAQ;KAAW;IAEzD,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,eAAe;QACnB,MAAM,QAAQ;YACZ;gBACE,KAAK;gBACL,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;gBACxB,OAAO;YACT;SACD;QAED,8BAA8B;QAC9B,IAAI,YAAY,cAAc,EAAE;YAC9B,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,yBAAyB;QACzB,IAAI,YAAY,gBAAgB,EAAE;YAChC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;gBACrB,OAAO;YACT;QACF;QAEA,IAAI,YAAY,cAAc,EAAE;YAC9B,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,IAAI,YAAY,iBAAiB,EAAE;YACjC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gBACvB,OAAO;YACT;QACF;QAEA,YAAY;QACZ,IAAI,YAAY,gBAAgB,EAAE;YAChC,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gBACvB,OAAO;YACT;QACF;QAEA,kBAAkB;QAClB,IAAI,YAAY,OAAO,EAAE;YACvB,MAAM,IAAI,CAAC;gBACT,KAAK;gBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gBACnB,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,2EAA2E;IAC3E,qBAAqB;IACrB,2EAA2E;IAE3E,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,SAAS,IAAM,OAAO,IAAI,CAAC;QAC7B;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,SAAS;gBACP;gBACA,OAAO,IAAI,CAAC;YACd;QACF;KACD;IAED,2EAA2E;IAC3E,WAAW;IACX,2EAA2E;IAE3E,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,aAAa,CAAC;IAChB;IAEA,2EAA2E;IAC3E,gBAAgB;IAChB,2EAA2E;IAE3E,IAAI,CAAC,WAAW,WAAW;QACzB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,iLAAA,CAAA,OAAI;gBAAC,MAAK;gBAAQ,KAAI;;;;;;;;;;;IAG7B;IAEA,2EAA2E;IAC3E,wBAAwB;IACxB,2EAA2E;IAE3E,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;;;;;;;;;;;IAIhB;IAEA,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAE3E,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAU;;0BAEhB,6LAAC;gBACC,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,OAAM;gBACN,OAAO;gBACP,WAAU;;kCAGV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mMAAA,CAAA,QAAK;;8CACJ,6LAAC,mNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACtB,CAAC,2BACA,6LAAC;oCAAM,OAAO;oCAAG,WAAU;8CAAiB;;;;;;;;;;;;;;;;;kCAQlD,6LAAC,iLAAA,CAAA,OAAI;wBACH,OAAM;wBACN,MAAK;wBACL,cAAc;4BAAC;yBAAS;wBACxB,OAAO;wBACP,SAAS;wBACT,WAAU;;;;;;;;;;;;0BAKd,6LAAC,qLAAA,CAAA,SAAM;;kCAEL,6LAAC;wBAAO,WAAU;;0CAEhB,6LAAC,mMAAA,CAAA,QAAK;;kDACJ,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,0BAAY,6LAAC,iOAAA,CAAA,qBAAkB;;;;mEAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAEZ,6LAAC;wCAAM,OAAO;wCAAG,WAAU;kDAAM;;;;;;;;;;;;0CAMnC,6LAAC,mMAAA,CAAA,QAAK;gCAAC,MAAK;;kDAEV,6LAAC,mLAAA,CAAA,QAAK;wCAAC,OAAO;wCAAG,UAAU;kDACzB,cAAA,6LAAC,qMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4CACnB,WAAU;;;;;;;;;;;kDAKd,6LAAC,yLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAc;wCAC7B,WAAU;wCACV,KAAK;kDAEL,cAAA,6LAAC,mMAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,6LAAC,qLAAA,CAAA,SAAM;oDACL,MAAK;oDACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oDACnB,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,MAAM;;;;;;sEAET,6LAAC;4DAAI,WAAU;;gEACZ,YAAY,YAAY,IAAI,GAAI,MAAc,MAAM;gEACpD,YAAY,gBAAgB,IAAI,GAAI,MAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrE,6LAAC;wBAAQ,WAAU;kCAChB;;;;;;;;;;;;;;;;;;AAKX;GA9QgB;;QACC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACqC,iIAAA,CAAA,UAAO;QACpD,iIAAA,CAAA,iBAAc;;;KAJpB;uCAgRD"}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/APISportsGamev2-FECMS/apisportsgame-cms/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\n// APISportsGame CMS - Dashboard Home Page\n// Main dashboard với statistics và overview\n\nimport React from 'react';\nimport { \n  Row, \n  Col, \n  Card, \n  Statistic, \n  Typography, \n  Space,\n  <PERSON><PERSON>,\n  Alert,\n  Divider\n} from 'antd';\nimport {\n  UserOutlined,\n  TeamOutlined,\n  TrophyOutlined,\n  CalendarOutlined,\n  ApiOutlined,\n  SyncOutlined,\n  <PERSON><PERSON><PERSON>Outlined,\n  CheckCircleOutlined\n} from '@ant-design/icons';\nimport { DashboardLayout } from '@/components/layouts/dashboard-layout';\nimport { usePermissions } from '@/stores/auth-store';\n\nconst { Title, Text } = Typography;\n\n// ============================================================================\n// DASHBOARD PAGE COMPONENT\n// ============================================================================\n\nexport default function DashboardPage() {\n  const permissions = usePermissions();\n\n  // ========================================================================\n  // MOCK DATA - sẽ được thay thế bằng real API calls\n  // ========================================================================\n  const stats = {\n    totalUsers: 1250,\n    totalSystemUsers: 15,\n    totalRegisteredUsers: 1235,\n    totalLeagues: 45,\n    totalTeams: 890,\n    totalFixtures: 12500,\n    apiCallsToday: 8750,\n    activeUsers: 125,\n  };\n\n  const recentActivities = [\n    { id: 1, action: 'Sync completed', time: '5 phút trước', type: 'success' },\n    { id: 2, action: 'New user registered', time: '10 phút trước', type: 'info' },\n    { id: 3, action: 'League updated', time: '15 phút trước', type: 'warning' },\n    { id: 4, action: 'Fixture created', time: '20 phút trước', type: 'info' },\n  ];\n\n  // ========================================================================\n  // RENDER\n  // ========================================================================\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Page Header */}\n        <div>\n          <Title level={2} className=\"mb-2\">\n            Dashboard\n          </Title>\n          <Text type=\"secondary\" className=\"text-base\">\n            Chào mừng trở lại, {permissions.currentUser?.username}! \n            Đây là tổng quan hệ thống APISportsGame CMS.\n          </Text>\n        </div>\n\n        {/* User Info Alert */}\n        <Alert\n          message={`Đăng nhập với quyền: ${\n            permissions.isSystemUser \n              ? `SystemUser (${(permissions.currentUser as any)?.role})` \n              : `RegisteredUser (${(permissions.currentUser as any)?.tier})`\n          }`}\n          type=\"info\"\n          showIcon\n          icon={<CheckCircleOutlined />}\n          className=\"mb-6\"\n        />\n\n        {/* Statistics Cards */}\n        <Row gutter={[16, 16]}>\n          {/* Users Stats */}\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Tổng Users\"\n                value={stats.totalUsers}\n                prefix={<UserOutlined />}\n                valueStyle={{ color: '#1890ff' }}\n              />\n              <div className=\"mt-2 text-xs text-gray-500\">\n                System: {stats.totalSystemUsers} | Registered: {stats.totalRegisteredUsers}\n              </div>\n            </Card>\n          </Col>\n\n          {/* Leagues Stats */}\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Leagues\"\n                value={stats.totalLeagues}\n                prefix={<TrophyOutlined />}\n                valueStyle={{ color: '#52c41a' }}\n              />\n              <div className=\"mt-2 text-xs text-gray-500\">\n                Active leagues trong hệ thống\n              </div>\n            </Card>\n          </Col>\n\n          {/* Teams Stats */}\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Teams\"\n                value={stats.totalTeams}\n                prefix={<TeamOutlined />}\n                valueStyle={{ color: '#faad14' }}\n              />\n              <div className=\"mt-2 text-xs text-gray-500\">\n                Đội bóng đã đồng bộ\n              </div>\n            </Card>\n          </Col>\n\n          {/* Fixtures Stats */}\n          <Col xs={24} sm={12} lg={6}>\n            <Card>\n              <Statistic\n                title=\"Fixtures\"\n                value={stats.totalFixtures}\n                prefix={<CalendarOutlined />}\n                valueStyle={{ color: '#722ed1' }}\n              />\n              <div className=\"mt-2 text-xs text-gray-500\">\n                Trận đấu trong database\n              </div>\n            </Card>\n          </Col>\n        </Row>\n\n        {/* API Usage & Activity */}\n        <Row gutter={[16, 16]}>\n          {/* API Usage */}\n          <Col xs={24} lg={12}>\n            <Card \n              title={\n                <Space>\n                  <ApiOutlined />\n                  API Usage Today\n                </Space>\n              }\n              extra={\n                <Button type=\"link\" size=\"small\">\n                  Xem chi tiết\n                </Button>\n              }\n            >\n              <Statistic\n                value={stats.apiCallsToday}\n                suffix=\"/ 100,000\"\n                valueStyle={{ color: '#1890ff' }}\n              />\n              <div className=\"mt-4\">\n                <div className=\"flex justify-between text-sm\">\n                  <span>Active Users</span>\n                  <span className=\"font-medium\">{stats.activeUsers}</span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n                  <div \n                    className=\"bg-blue-500 h-2 rounded-full\" \n                    style={{ width: `${(stats.apiCallsToday / 100000) * 100}%` }}\n                  ></div>\n                </div>\n              </div>\n            </Card>\n          </Col>\n\n          {/* Recent Activities */}\n          <Col xs={24} lg={12}>\n            <Card \n              title={\n                <Space>\n                  <BarChartOutlined />\n                  Hoạt động gần đây\n                </Space>\n              }\n              extra={\n                <Button type=\"link\" size=\"small\">\n                  Xem tất cả\n                </Button>\n              }\n            >\n              <div className=\"space-y-3\">\n                {recentActivities.map((activity) => (\n                  <div key={activity.id} className=\"flex justify-between items-center\">\n                    <div>\n                      <div className=\"text-sm font-medium\">{activity.action}</div>\n                      <div className=\"text-xs text-gray-500\">{activity.time}</div>\n                    </div>\n                    <div className={`w-2 h-2 rounded-full ${\n                      activity.type === 'success' ? 'bg-green-500' :\n                      activity.type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'\n                    }`}></div>\n                  </div>\n                ))}\n              </div>\n            </Card>\n          </Col>\n        </Row>\n\n        {/* Quick Actions */}\n        <Card title=\"Quick Actions\">\n          <Row gutter={[16, 16]}>\n            {permissions.canSync && (\n              <Col xs={24} sm={12} md={8} lg={6}>\n                <Button \n                  type=\"primary\" \n                  icon={<SyncOutlined />} \n                  block\n                  size=\"large\"\n                >\n                  Sync Data\n                </Button>\n              </Col>\n            )}\n            \n            {permissions.canManageUsers && (\n              <Col xs={24} sm={12} md={8} lg={6}>\n                <Button \n                  icon={<UserOutlined />} \n                  block\n                  size=\"large\"\n                >\n                  Manage Users\n                </Button>\n              </Col>\n            )}\n            \n            {permissions.canManageLeagues && (\n              <Col xs={24} sm={12} md={8} lg={6}>\n                <Button \n                  icon={<TrophyOutlined />} \n                  block\n                  size=\"large\"\n                >\n                  Manage Leagues\n                </Button>\n              </Col>\n            )}\n            \n            {permissions.canViewAnalytics && (\n              <Col xs={24} sm={12} md={8} lg={6}>\n                <Button \n                  icon={<BarChartOutlined />} \n                  block\n                  size=\"large\"\n                >\n                  View Analytics\n                </Button>\n              </Col>\n            )}\n          </Row>\n        </Card>\n\n        {/* System Status */}\n        <Card title=\"System Status\">\n          <Row gutter={[16, 16]}>\n            <Col span={8}>\n              <div className=\"text-center\">\n                <div className=\"text-2xl text-green-500 mb-2\">●</div>\n                <div className=\"font-medium\">API Server</div>\n                <div className=\"text-sm text-gray-500\">Online</div>\n              </div>\n            </Col>\n            <Col span={8}>\n              <div className=\"text-center\">\n                <div className=\"text-2xl text-green-500 mb-2\">●</div>\n                <div className=\"font-medium\">Database</div>\n                <div className=\"text-sm text-gray-500\">Connected</div>\n              </div>\n            </Col>\n            <Col span={8}>\n              <div className=\"text-center\">\n                <div className=\"text-2xl text-yellow-500 mb-2\">●</div>\n                <div className=\"font-medium\">Sync Service</div>\n                <div className=\"text-sm text-gray-500\">Running</div>\n              </div>\n            </Col>\n          </Row>\n        </Card>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AA2BA;AACA;AAtBA;AAAA;AAWA;AAXA;AAAA;AAAA;AAAA;AAWA;AAAA;AAAA;AAAA;AAXA;AAWA;AAXA;AAWA;AAAA;;;AAjBA;;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAMnB,SAAS;;IACtB,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEjC,2EAA2E;IAC3E,mDAAmD;IACnD,2EAA2E;IAC3E,MAAM,QAAQ;QACZ,YAAY;QACZ,kBAAkB;QAClB,sBAAsB;QACtB,cAAc;QACd,YAAY;QACZ,eAAe;QACf,eAAe;QACf,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB;YAAE,IAAI;YAAG,QAAQ;YAAkB,MAAM;YAAgB,MAAM;QAAU;QACzE;YAAE,IAAI;YAAG,QAAQ;YAAuB,MAAM;YAAiB,MAAM;QAAO;QAC5E;YAAE,IAAI;YAAG,QAAQ;YAAkB,MAAM;YAAiB,MAAM;QAAU;QAC1E;YAAE,IAAI;YAAG,QAAQ;YAAmB,MAAM;YAAiB,MAAM;QAAO;KACzE;IAED,2EAA2E;IAC3E,SAAS;IACT,2EAA2E;IAC3E,qBACE,6LAAC,uJAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;;sCACC,6LAAC;4BAAM,OAAO;4BAAG,WAAU;sCAAO;;;;;;sCAGlC,6LAAC;4BAAK,MAAK;4BAAY,WAAU;;gCAAY;gCACvB,YAAY,WAAW,EAAE;gCAAS;;;;;;;;;;;;;8BAM1D,6LAAC,mLAAA,CAAA,QAAK;oBACJ,SAAS,CAAC,qBAAqB,EAC7B,YAAY,YAAY,GACpB,CAAC,YAAY,EAAG,YAAY,WAAW,EAAU,KAAK,CAAC,CAAC,GACxD,CAAC,gBAAgB,EAAG,YAAY,WAAW,EAAU,KAAK,CAAC,CAAC,EAChE;oBACF,MAAK;oBACL,QAAQ;oBACR,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;oBAC1B,WAAU;;;;;;8BAIZ,6LAAC,+KAAA,CAAA,MAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;;sCAEnB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,6LAAC,iLAAA,CAAA,OAAI;;kDACH,6LAAC,2LAAA,CAAA,YAAS;wCACR,OAAM;wCACN,OAAO,MAAM,UAAU;wCACvB,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,YAAY;4CAAE,OAAO;wCAAU;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;4CAA6B;4CACjC,MAAM,gBAAgB;4CAAC;4CAAgB,MAAM,oBAAoB;;;;;;;;;;;;;;;;;;sCAMhF,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,6LAAC,iLAAA,CAAA,OAAI;;kDACH,6LAAC,2LAAA,CAAA,YAAS;wCACR,OAAM;wCACN,OAAO,MAAM,YAAY;wCACzB,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;wCACvB,YAAY;4CAAE,OAAO;wCAAU;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;sCAOhD,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,6LAAC,iLAAA,CAAA,OAAI;;kDACH,6LAAC,2LAAA,CAAA,YAAS;wCACR,OAAM;wCACN,OAAO,MAAM,UAAU;wCACvB,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,YAAY;4CAAE,OAAO;wCAAU;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;sCAOhD,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;sCACvB,cAAA,6LAAC,iLAAA,CAAA,OAAI;;kDACH,6LAAC,2LAAA,CAAA,YAAS;wCACR,OAAM;wCACN,OAAO,MAAM,aAAa;wCAC1B,sBAAQ,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wCACzB,YAAY;4CAAE,OAAO;wCAAU;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;8BAQlD,6LAAC,+KAAA,CAAA,MAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;;sCAEnB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;sCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;gCACH,qBACE,6LAAC,mMAAA,CAAA,QAAK;;sDACJ,6LAAC,mNAAA,CAAA,cAAW;;;;;wCAAG;;;;;;;gCAInB,qBACE,6LAAC,qMAAA,CAAA,SAAM;oCAAC,MAAK;oCAAO,MAAK;8CAAQ;;;;;;;kDAKnC,6LAAC,2LAAA,CAAA,YAAS;wCACR,OAAO,MAAM,aAAa;wCAC1B,QAAO;wCACP,YAAY;4CAAE,OAAO;wCAAU;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEAAe,MAAM,WAAW;;;;;;;;;;;;0DAElD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,AAAC,MAAM,aAAa,GAAG,SAAU,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQrE,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;sCACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;gCACH,qBACE,6LAAC,mMAAA,CAAA,QAAK;;sDACJ,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wCAAG;;;;;;;gCAIxB,qBACE,6LAAC,qMAAA,CAAA,SAAM;oCAAC,MAAK;oCAAO,MAAK;8CAAQ;;;;;;0CAKnC,cAAA,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,6LAAC;4CAAsB,WAAU;;8DAC/B,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAuB,SAAS,MAAM;;;;;;sEACrD,6LAAC;4DAAI,WAAU;sEAAyB,SAAS,IAAI;;;;;;;;;;;;8DAEvD,6LAAC;oDAAI,WAAW,CAAC,qBAAqB,EACpC,SAAS,IAAI,KAAK,YAAY,iBAC9B,SAAS,IAAI,KAAK,YAAY,kBAAkB,eAChD;;;;;;;2CARM,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;8BAiB/B,6LAAC,iLAAA,CAAA,OAAI;oBAAC,OAAM;8BACV,cAAA,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;;4BAClB,YAAY,OAAO,kBAClB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;0CAC9B,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,KAAK;oCACL,MAAK;8CACN;;;;;;;;;;;4BAMJ,YAAY,cAAc,kBACzB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;0CAC9B,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,KAAK;oCACL,MAAK;8CACN;;;;;;;;;;;4BAMJ,YAAY,gBAAgB,kBAC3B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;0CAC9B,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oCACrB,KAAK;oCACL,MAAK;8CACN;;;;;;;;;;;4BAMJ,YAAY,gBAAgB,kBAC3B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;0CAC9B,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oCACvB,KAAK;oCACL,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;8BAST,6LAAC,iLAAA,CAAA,OAAI;oBAAC,OAAM;8BACV,cAAA,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;;0CACnB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA+B;;;;;;sDAC9C,6LAAC;4CAAI,WAAU;sDAAc;;;;;;sDAC7B,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAG3C,6LAAC,+KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA+B;;;;;;sDAC9C,6LAAC;4CAAI,WAAU;sDAAc;;;;;;sDAC7B,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAG3C,6LAAC,+KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;sDAAc;;;;;;sDAC7B,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;GA9QwB;;QACF,iIAAA,CAAA,iBAAc;;;KADZ"}}, {"offset": {"line": 1747, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}