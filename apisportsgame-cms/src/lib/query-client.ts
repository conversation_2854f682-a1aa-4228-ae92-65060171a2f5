// APISportsGame CMS - TanStack Query Configuration
// Setup theo CMS Development Guide specifications

import { QueryClient } from '@tanstack/react-query';

// Query Client configuration theo performance rules
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (cacheTime renamed to gcTime)
      retry: 3,
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});

// Query Keys - centralized key management
export const queryKeys = {
  // Auth keys
  auth: {
    profile: ['auth', 'profile'] as const,
  },
  
  // League keys
  leagues: {
    all: ['leagues'] as const,
    lists: () => [...queryKeys.leagues.all, 'list'] as const,
    list: (filters?: any) => [...queryKeys.leagues.lists(), filters] as const,
    details: () => [...queryKeys.leagues.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.leagues.details(), id] as const,
    active: () => [...queryKeys.leagues.all, 'active'] as const,
  },
  
  // Team keys
  teams: {
    all: ['teams'] as const,
    lists: () => [...queryKeys.teams.all, 'list'] as const,
    list: (filters?: any) => [...queryKeys.teams.lists(), filters] as const,
    details: () => [...queryKeys.teams.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.teams.details(), id] as const,
  },
  
  // Fixture keys
  fixtures: {
    all: ['fixtures'] as const,
    lists: () => [...queryKeys.fixtures.all, 'list'] as const,
    list: (filters?: any) => [...queryKeys.fixtures.lists(), filters] as const,
    details: () => [...queryKeys.fixtures.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.fixtures.details(), id] as const,
    live: () => [...queryKeys.fixtures.all, 'live'] as const,
    today: () => [...queryKeys.fixtures.all, 'today'] as const,
  },
  
  // User keys
  users: {
    all: ['users'] as const,
    lists: () => [...queryKeys.users.all, 'list'] as const,
    list: (filters?: any) => [...queryKeys.users.lists(), filters] as const,
    details: () => [...queryKeys.users.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.users.details(), id] as const,
  },
  
  // Sync keys
  sync: {
    all: ['sync'] as const,
    status: () => [...queryKeys.sync.all, 'status'] as const,
  },
  
  // Dashboard keys
  dashboard: {
    all: ['dashboard'] as const,
    stats: () => [...queryKeys.dashboard.all, 'stats'] as const,
  },
} as const;
