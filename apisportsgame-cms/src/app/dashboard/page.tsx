'use client';

// APISportsGame CMS - Dashboard Home Page
// Main dashboard với statistics và overview

import React from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Typography, 
  Space,
  <PERSON><PERSON>,
  Alert,
  Divider
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  TrophyOutlined,
  CalendarOutlined,
  ApiOutlined,
  SyncOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { DashboardLayout } from '@/components/layouts/dashboard-layout';
import { usePermissions } from '@/stores/auth-store';

const { Title, Text } = Typography;

// ============================================================================
// DASHBOARD PAGE COMPONENT
// ============================================================================

export default function DashboardPage() {
  const permissions = usePermissions();

  // ========================================================================
  // MOCK DATA - sẽ được thay thế bằng real API calls
  // ========================================================================
  const stats = {
    totalUsers: 1250,
    totalSystemUsers: 15,
    totalRegisteredUsers: 1235,
    totalLeagues: 45,
    totalTeams: 890,
    totalFixtures: 12500,
    apiCallsToday: 8750,
    activeUsers: 125,
  };

  const recentActivities = [
    { id: 1, action: 'Sync completed', time: '5 phút trước', type: 'success' },
    { id: 2, action: 'New user registered', time: '10 phút trước', type: 'info' },
    { id: 3, action: 'League updated', time: '15 phút trước', type: 'warning' },
    { id: 4, action: 'Fixture created', time: '20 phút trước', type: 'info' },
  ];

  // ========================================================================
  // RENDER
  // ========================================================================
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <Title level={2} className="mb-2">
            Dashboard
          </Title>
          <Text type="secondary" className="text-base">
            Chào mừng trở lại, {permissions.currentUser?.username}! 
            Đây là tổng quan hệ thống APISportsGame CMS.
          </Text>
        </div>

        {/* User Info Alert */}
        <Alert
          message={`Đăng nhập với quyền: ${
            permissions.isSystemUser 
              ? `SystemUser (${(permissions.currentUser as any)?.role})` 
              : `RegisteredUser (${(permissions.currentUser as any)?.tier})`
          }`}
          type="info"
          showIcon
          icon={<CheckCircleOutlined />}
          className="mb-6"
        />

        {/* Statistics Cards */}
        <Row gutter={[16, 16]}>
          {/* Users Stats */}
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Tổng Users"
                value={stats.totalUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
              <div className="mt-2 text-xs text-gray-500">
                System: {stats.totalSystemUsers} | Registered: {stats.totalRegisteredUsers}
              </div>
            </Card>
          </Col>

          {/* Leagues Stats */}
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Leagues"
                value={stats.totalLeagues}
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
              <div className="mt-2 text-xs text-gray-500">
                Active leagues trong hệ thống
              </div>
            </Card>
          </Col>

          {/* Teams Stats */}
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Teams"
                value={stats.totalTeams}
                prefix={<TeamOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
              <div className="mt-2 text-xs text-gray-500">
                Đội bóng đã đồng bộ
              </div>
            </Card>
          </Col>

          {/* Fixtures Stats */}
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Fixtures"
                value={stats.totalFixtures}
                prefix={<CalendarOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
              <div className="mt-2 text-xs text-gray-500">
                Trận đấu trong database
              </div>
            </Card>
          </Col>
        </Row>

        {/* API Usage & Activity */}
        <Row gutter={[16, 16]}>
          {/* API Usage */}
          <Col xs={24} lg={12}>
            <Card 
              title={
                <Space>
                  <ApiOutlined />
                  API Usage Today
                </Space>
              }
              extra={
                <Button type="link" size="small">
                  Xem chi tiết
                </Button>
              }
            >
              <Statistic
                value={stats.apiCallsToday}
                suffix="/ 100,000"
                valueStyle={{ color: '#1890ff' }}
              />
              <div className="mt-4">
                <div className="flex justify-between text-sm">
                  <span>Active Users</span>
                  <span className="font-medium">{stats.activeUsers}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full" 
                    style={{ width: `${(stats.apiCallsToday / 100000) * 100}%` }}
                  ></div>
                </div>
              </div>
            </Card>
          </Col>

          {/* Recent Activities */}
          <Col xs={24} lg={12}>
            <Card 
              title={
                <Space>
                  <BarChartOutlined />
                  Hoạt động gần đây
                </Space>
              }
              extra={
                <Button type="link" size="small">
                  Xem tất cả
                </Button>
              }
            >
              <div className="space-y-3">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex justify-between items-center">
                    <div>
                      <div className="text-sm font-medium">{activity.action}</div>
                      <div className="text-xs text-gray-500">{activity.time}</div>
                    </div>
                    <div className={`w-2 h-2 rounded-full ${
                      activity.type === 'success' ? 'bg-green-500' :
                      activity.type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
                    }`}></div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
        </Row>

        {/* Quick Actions */}
        <Card title="Quick Actions">
          <Row gutter={[16, 16]}>
            {permissions.canSync && (
              <Col xs={24} sm={12} md={8} lg={6}>
                <Button 
                  type="primary" 
                  icon={<SyncOutlined />} 
                  block
                  size="large"
                >
                  Sync Data
                </Button>
              </Col>
            )}
            
            {permissions.canManageUsers && (
              <Col xs={24} sm={12} md={8} lg={6}>
                <Button 
                  icon={<UserOutlined />} 
                  block
                  size="large"
                >
                  Manage Users
                </Button>
              </Col>
            )}
            
            {permissions.canManageLeagues && (
              <Col xs={24} sm={12} md={8} lg={6}>
                <Button 
                  icon={<TrophyOutlined />} 
                  block
                  size="large"
                >
                  Manage Leagues
                </Button>
              </Col>
            )}
            
            {permissions.canViewAnalytics && (
              <Col xs={24} sm={12} md={8} lg={6}>
                <Button 
                  icon={<BarChartOutlined />} 
                  block
                  size="large"
                >
                  View Analytics
                </Button>
              </Col>
            )}
          </Row>
        </Card>

        {/* System Status */}
        <Card title="System Status">
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <div className="text-center">
                <div className="text-2xl text-green-500 mb-2">●</div>
                <div className="font-medium">API Server</div>
                <div className="text-sm text-gray-500">Online</div>
              </div>
            </Col>
            <Col span={8}>
              <div className="text-center">
                <div className="text-2xl text-green-500 mb-2">●</div>
                <div className="font-medium">Database</div>
                <div className="text-sm text-gray-500">Connected</div>
              </div>
            </Col>
            <Col span={8}>
              <div className="text-center">
                <div className="text-2xl text-yellow-500 mb-2">●</div>
                <div className="font-medium">Sync Service</div>
                <div className="text-sm text-gray-500">Running</div>
              </div>
            </Col>
          </Row>
        </Card>
      </div>
    </DashboardLayout>
  );
}
