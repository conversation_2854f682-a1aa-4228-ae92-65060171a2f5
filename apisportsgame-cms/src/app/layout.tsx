import type { Metadata } from "next";
import "./globals.css";
import { Providers } from "@/components/providers";

export const metadata: Metadata = {
  title: "APISportsGame CMS",
  description: "Frontend CMS for APISportsGame - Quản lý giải đấu, tr<PERSON><PERSON> đấu, đ<PERSON><PERSON> bóng và người dùng",
  keywords: ["CMS", "Sports", "Football", "API", "Management"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="vi">
      <body className="antialiased">
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
