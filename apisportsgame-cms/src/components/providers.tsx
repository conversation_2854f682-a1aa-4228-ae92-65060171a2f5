'use client';

// APISportsGame CMS - Providers Component
// Setup all providers theo CMS Development Guide

import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ConfigProvider, theme, App } from 'antd';
import viVN from 'antd/locale/vi_VN';
import { queryClient } from '@/lib/query-client';

// ============================================================================
// PROVIDERS COMPONENT
// ============================================================================

interface ProvidersProps {
  children: React.ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider
        locale={viVN}
        theme={{
          algorithm: theme.defaultAlgorithm,
          token: {
            // Primary colors
            colorPrimary: '#1890ff',
            colorSuccess: '#52c41a',
            colorWarning: '#faad14',
            colorError: '#ff4d4f',
            colorInfo: '#1890ff',
            
            // Layout
            borderRadius: 6,
            wireframe: false,
            
            // Typography
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
            fontSize: 14,
            
            // Spacing
            padding: 16,
            margin: 16,
          },
          components: {
            // Layout components
            Layout: {
              headerBg: '#001529',
              siderBg: '#001529',
              bodyBg: '#f0f2f5',
            },
            
            // Menu components
            Menu: {
              darkItemBg: '#001529',
              darkItemSelectedBg: '#1890ff',
              darkItemHoverBg: '#1890ff',
            },
            
            // Table components
            Table: {
              headerBg: '#fafafa',
              headerColor: '#262626',
              rowHoverBg: '#f5f5f5',
            },
            
            // Card components
            Card: {
              headerBg: '#fafafa',
              actionsBg: '#fafafa',
            },
            
            // Button components
            Button: {
              borderRadius: 6,
              controlHeight: 32,
            },
            
            // Form components
            Form: {
              labelColor: '#262626',
              labelFontSize: 14,
            },
          },
        }}
      >
        <App>
          {children}
        </App>
      </ConfigProvider>
      
      {/* React Query Devtools - chỉ hiển thị trong development */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools 
          initialIsOpen={false} 
          position="bottom-right"
        />
      )}
    </QueryClientProvider>
  );
}

// ============================================================================
// THEME PROVIDER (Optional - for dark mode support)
// ============================================================================

interface ThemeProviderProps {
  children: React.ReactNode;
  darkMode?: boolean;
}

export function ThemeProvider({ children, darkMode = false }: ThemeProviderProps) {
  const themeConfig = {
    algorithm: darkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
    token: {
      colorPrimary: '#1890ff',
      colorSuccess: '#52c41a',
      colorWarning: '#faad14',
      colorError: '#ff4d4f',
      colorInfo: '#1890ff',
      borderRadius: 6,
      wireframe: false,
    },
    components: {
      Layout: {
        headerBg: darkMode ? '#141414' : '#001529',
        siderBg: darkMode ? '#141414' : '#001529',
        bodyBg: darkMode ? '#000000' : '#f0f2f5',
      },
      Menu: {
        darkItemBg: darkMode ? '#141414' : '#001529',
        darkItemSelectedBg: '#1890ff',
        darkItemHoverBg: '#1890ff',
      },
    },
  };

  return (
    <ConfigProvider theme={themeConfig} locale={viVN}>
      <App>
        {children}
      </App>
    </ConfigProvider>
  );
}

export default Providers;
