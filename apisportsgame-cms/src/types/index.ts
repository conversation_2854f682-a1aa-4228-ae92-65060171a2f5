// APISportsGame CMS - TypeScript Types
// Copied exactly from CMS Development Guide

// ============================================================================
// AUTHENTICATION TYPES
// ============================================================================

export interface SystemUser {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'editor' | 'moderator';
  isActive: boolean;
  lastLoginAt: Date;
  createdAt: Date;
}

export interface RegisteredUser {
  id: number;
  username: string;
  email: string;
  tier: 'free' | 'premium' | 'enterprise';
  isActive: boolean;
  isEmailVerified: boolean;
  apiCallsUsed: number;
  apiCallsLimit: number | null;
  subscriptionEndDate: Date | null;
  lastLoginAt: Date;
  createdAt: Date;
}

export type SystemRole = 'admin' | 'editor' | 'moderator';
export type RegisteredUserTier = 'free' | 'premium' | 'enterprise';

// JWT Token Structures
export interface SystemUserToken {
  sub: number;
  username: string;
  email: string;
  role: SystemRole;
  userType: 'system';
}

export interface RegisteredUserToken {
  sub: number;
  username: string;
  email: string;
  tier: RegisteredUserTier;
  userType: 'registered';
  isEmailVerified: boolean;
}

// ============================================================================
// SPORTS DATA TYPES
// ============================================================================

export interface League {
  id: number;
  apiFootballId: number;
  name: string;
  country: string;
  logo: string;
  flag: string;
  season: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Team {
  id: number;
  apiFootballId: number;
  name: string;
  code: string;
  country: string;
  founded: number;
  logo: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Fixture {
  id: number;
  apiFootballId: number;
  referee: string;
  timezone: string;
  date: Date;
  timestamp: number;
  // Venue
  venueId: number;
  venueName: string;
  venueCity: string;
  // Status
  statusLong: string;
  statusShort: string;
  elapsed: number;
  // League
  leagueId: number;
  leagueName: string;
  leagueSeason: number;
  leagueRound: string;
  // Teams
  homeTeamId: number;
  homeTeamName: string;
  homeTeamLogo: string;
  awayTeamId: number;
  awayTeamName: string;
  awayTeamLogo: string;
  // Score
  goalsHome: number;
  goalsAway: number;
  scoreHalftimeHome: number;
  scoreHalftimeAway: number;
  scoreFulltimeHome: number;
  scoreFulltimeAway: number;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiError {
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: PaginationMeta;
}

// ============================================================================
// AUTH REQUEST/RESPONSE TYPES
// ============================================================================

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  user: SystemUser | RegisteredUser;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

// ============================================================================
// FORM TYPES
// ============================================================================

export interface CreateLeagueForm {
  name: string;
  country: string;
  season: number;
  logo?: string;
  flag?: string;
}

export interface CreateTeamForm {
  name: string;
  code: string;
  country: string;
  founded: number;
  logo?: string;
}

export interface CreateSystemUserForm {
  username: string;
  email: string;
  password: string;
  role: SystemRole;
}

// ============================================================================
// FILTER TYPES
// ============================================================================

export interface LeagueFilters {
  country?: string;
  season?: number;
  isActive?: boolean;
  search?: string;
}

export interface TeamFilters {
  country?: string;
  search?: string;
}

export interface FixtureFilters {
  leagueId?: number;
  teamId?: number;
  date?: string;
  status?: string;
  search?: string;
}

export interface UserFilters {
  role?: SystemRole;
  tier?: RegisteredUserTier;
  isActive?: boolean;
  search?: string;
}

// ============================================================================
// DASHBOARD TYPES
// ============================================================================

export interface DashboardStats {
  totalUsers: number;
  totalSystemUsers: number;
  totalRegisteredUsers: number;
  totalLeagues: number;
  totalTeams: number;
  totalFixtures: number;
  apiCallsToday: number;
  activeUsers: number;
}

export interface SyncStatus {
  lastSync: Date;
  isRunning: boolean;
  nextScheduled: Date;
  errors: string[];
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface TableColumn<T> {
  key: string;
  title: string;
  dataIndex: keyof T;
  render?: (value: any, record: T) => React.ReactNode;
  sorter?: boolean;
  width?: number;
}

export interface FormFieldProps {
  name: string;
  label: string;
  required?: boolean;
  placeholder?: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea';
  options?: { label: string; value: any }[];
}
